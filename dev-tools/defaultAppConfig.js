module.exports = {
  isRoot: false, // Is the app a root shell
  isFederated: true, // Is the app federated
  disableBrotliCompression: false, // Enable brotli compression for production builds, Disable for login app.
  minimizeCss: false, // Minimize css for production builds. Only true for root shell
  enableMiniCssExtract: false, // Enable mini-css-extract-plugin. Only true for root shell
  enableHtmlWebpackPlugin: false, // Enable html-webpack-plugin. Only true for root shell
  alias: {
    moment: 'moment/moment.js',
    tcomponents: '@tekion/tekion-components/src',
    tstyles: '@tekion/tekion-styles-next/scss',
    tbase: '@tekion/tekion-base',
    '@tekion/tekion-components': '@tekion/tekion-components/src',
    tbusiness: '@tekion/tekion-business/src',
    twidgets: '@tekion/tekion-widgets/src',
    tmpviInspection: '@tekion/tekion-service-mpvi-inspection',
  },
  miniCssExtractLoader: {
    enabled: false, // Disable mini-css-extract-plugin. Only enable for root shell
  },
};
