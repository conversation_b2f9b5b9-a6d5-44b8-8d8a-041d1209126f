export const FETCH_PDF_URL_FAILURE_ERROR_MESSAGE = __('Failed to get PDF URL');

export const PDF_GENERATION_FAILED_ERROR = new Error(__('PDF generation failed'));

export const PDF_GENERATION_POLLING_ERROR_MESSAGES = {
  timeoutErrorMessage: new Error(__('Timeout while polling PDF generation status')),
  pollingCancelledErrorMessage: new Error(__('Error while polling PDF generation status')),
};

export const PDF_REQUEST_LOG_STATUS = {
  REQUESTED: 'REQUESTED',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
};
