// Lodash
import _head from 'lodash/head';

// Utils
import { downloadURI } from 'tcomponents/utils/downloadFile';
import getDataFromResponse from 'tbase/utils/getDataFromResponse';
import toastAPIError from 'utils/toastAPIError';

// Readers
import bacsFileMediaItemReader from 'readers/accountsPayable/bacs/BacsFileMediaItem';
import bacsFilesMediaReader from 'readers/accountsPayable/bacs/BacsFilesMedia';

// Helpers
import { getFirstSignedURLFromMediaList, getFirstFileNameFromMediaList } from 'tbusiness/helpers/mediaV3.helpers';

// Services
import { getSignedURLs } from 'tbusiness/services/mediaV3';
import { fetchBacsFileMediaItem } from 'services/bacsFileServices';

const BACS_DOWNLOAD_ERROR_MESSAGE = __('Failed to download BACs file');

const handleBacsFileDownloadFailure = err => toastAPIError(err, BACS_DOWNLOAD_ERROR_MESSAGE);

const downloadFile = mediaResponse => {
  const mediaUrl = getFirstSignedURLFromMediaList(mediaResponse);
  const fileName = getFirstFileNameFromMediaList(mediaResponse);
  return downloadURI(mediaUrl, fileName);
};

export const downloadBacsFileFromMediaItem = bacsFileMediaItem => {
  const mediaId = bacsFileMediaItemReader.mediaId(bacsFileMediaItem);
  return getSignedURLs(mediaId).then(downloadFile).catch(handleBacsFileDownloadFailure);
};

const handleFetchBacsFileMediaDataSuccess = response => {
  const bacsFileMediaData = getDataFromResponse(response);
  const bacsFileHistoryItems = bacsFilesMediaReader.historyItems(bacsFileMediaData);
  const latestBacsFileMediaItem = _head(bacsFileHistoryItems);

  return downloadBacsFileFromMediaItem(latestBacsFileMediaItem);
};

const downloadBacsFile = (paymentId, checkType) =>
  fetchBacsFileMediaItem(paymentId, checkType)
    .then(handleFetchBacsFileMediaDataSuccess)
    .catch(handleBacsFileDownloadFailure);

export default downloadBacsFile;
