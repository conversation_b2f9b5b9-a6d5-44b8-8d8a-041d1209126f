/* eslint-disable import/order */
import { compose } from 'recompose';

// Utils
import { getErrorMessage } from 'tbase/utils/errorUtils';
import Poller from 'tbase/utils/Poller';
import errToaster from 'utils/errToaster';

// Services
import getDataFromResponse from 'tbase/utils/getDataFromResponse';
import { fetchPdfUrl, getPdfRequestLog } from '../services/typstPdfData';
import { getSignedURLs } from 'tbusiness/services/mediaV3';

// Helpers
import { getFirstSignedURLFromMediaList } from 'tbusiness/helpers/mediaV3.helpers';

// Constants
import {
  PDF_REQUEST_LOG_STATUS,
  PDF_GENERATION_POLLING_ERROR_MESSAGES,
  PDF_GENERATION_FAILED_ERROR,
  FETCH_PDF_URL_FAILURE_ERROR_MESSAGE,
} from '../constants/typstPdfPolling';

// Readers
import mediaReader from 'tbase/readers/Media';
import pdfRequestLogReader from '../readers/PdfRequestLog';

const pollPdfRequestLog = generationId => () => getPdfRequestLog(generationId).then(getDataFromResponse);

const isPollingForPdfRequestLogCompleted = pdfRequestLog => {
  const status = pdfRequestLogReader.status(pdfRequestLog);
  if (status === PDF_REQUEST_LOG_STATUS.REQUESTED) return false;
  return true;
};

const pollPdfGenerationStatus = async generationId => {
  const poller = new Poller(
    pollPdfRequestLog(generationId),
    isPollingForPdfRequestLogCompleted,
    undefined,
    undefined,
    undefined,
    PDF_GENERATION_POLLING_ERROR_MESSAGES
  );
  const pdfRequestLog = await poller.run();

  const status = pdfRequestLogReader.status(pdfRequestLog);
  if (status === PDF_REQUEST_LOG_STATUS.FAILED) throw PDF_GENERATION_FAILED_ERROR;

  return pdfRequestLog;
};

const getMediaId = compose(mediaReader.mediaId, pdfRequestLogReader.mediaItem);

const getTypstPdfUrl = async request => {
  try {
    const pdfResponse = await fetchPdfUrl(request);
    const generationId = pdfResponse?.data?.generationId;
    const mediaId = await pollPdfGenerationStatus(generationId).then(getMediaId);
    const pdfUrl = await getSignedURLs(mediaId).then(getFirstSignedURLFromMediaList);
    return pdfUrl;
  } catch (err) {
    const errorMessage = getErrorMessage(err, FETCH_PDF_URL_FAILURE_ERROR_MESSAGE);
    errToaster(errorMessage);
    throw err;
  }
};

export default getTypstPdfUrl;
