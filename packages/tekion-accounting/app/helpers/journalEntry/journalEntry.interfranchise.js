// Lodash
import _size from 'lodash/size';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { SINGLE_DEALER_COUNT } from 'constants/journalEntry/journalEntry.general';

export const getShouldShowFranchise = (
  interFranchiseTransactionEnabledTenantDealersMap = EMPTY_OBJECT,
  sourceDealerId
) =>
  interFranchiseTransactionEnabledTenantDealersMap[sourceDealerId] &&
  _size(interFranchiseTransactionEnabledTenantDealersMap) !== SINGLE_DEALER_COUNT;
