// Constants
import PROGRAM_FIELD_KEYS from 'tbase/constants/accounting/programFieldKeys';
import FEATURE_FLAGS from 'tbusiness/appServices/accounting/constants/featureFlags';
import { EMPTY_FEATURE_FLAGS } from 'twidgets/context/featureFlags';
import { EMPTY_OBJECT } from 'tbase/app.constants';

// Common
import {
  makeInitialValues,
  TRANSACTION_FIELD_ID,
  updateIFPostingsWithMultiControlInfo,
  updatePostingsWithMultiControlInfo,
} from 'twidgets/appServices/accounting/organisms/configurableTransaction';

// Helpers
import { getFormValuesWithValidatedPostings } from 'twidgets/appServices/accounting/organisms/interFranchiseJournalEntry/organisms/configurableEditIFJE';

// Services
import {
  tenantDealerLookupByNumber,
  controlNumberResponseNormalizer,
} from 'twidgets/appServices/accounting/molecules/interDealerControlNumberInput';
import { fetchScheduleBalanceForTenantDealer } from 'tbusiness/appServices/accounting/services/interDealerControlNumberInput/tenantDealer';

// Utils
import getArraySafeValue from 'tbase/utils/getArraySafeValue';
import exposeFunctionsIfTestEnvironment from 'tbase/utils/test/exposeFunctionsIfTestEnvironment';

const getControlValidator = isEnterpriseV2Enabled =>
  isEnterpriseV2Enabled ? updateIFPostingsWithMultiControlInfo : updatePostingsWithMultiControlInfo;

const getInitialValuesWithValidatedPostings = async ({
  initialValues,
  isMultiControlEnabled,
  accountsMapByDealerId,
  accountingSettingsByDealerId,
  journalsMapByDealerId,
  targetIFJournalByDealerId,
  programFieldMap,
  isEnterpriseV2Enabled,
  featureFlags,
}) => {
  if (isMultiControlEnabled) {
    const controlValidator = getControlValidator(isEnterpriseV2Enabled);
    const multiControlValidatedValues = await controlValidator(
      initialValues,
      programFieldMap?.[PROGRAM_FIELD_KEYS.CONTROL_TYPE]
    );
    return multiControlValidatedValues;
  }

  const sourceDealerId = getArraySafeValue(initialValues?.[TRANSACTION_FIELD_ID.FRANCHISE]);

  const controlValidatedValues = await getFormValuesWithValidatedPostings({
    initialValues,
    sourceDealerId,
    accountsMapByDealerId,
    accountingSettingsByDealerId,
    journalsMapByDealerId,
    targetIFJournalByDealerId,
    lookupByNumber: tenantDealerLookupByNumber,
    fetchScheduleBalance: fetchScheduleBalanceForTenantDealer,
    controlNumberResponseNormalizer,
    featureFlags,
  });
  return controlValidatedValues;
};

const makeInitialValuesWithValidatedPostings = async ({
  postings,
  dealerInfo,
  transaction,
  programFieldMap,
  attachmentDetails,
  accountsMapByDealerId,
  journalsMapByDealerId,
  targetIFJournalByDealerId,
  accountingSettingsByDealerId,
  hasInterFranchiseJEPermission,
  featureFlags = EMPTY_FEATURE_FLAGS,
  templatePostingPresetMap = EMPTY_OBJECT,
}) => {
  const isControlTagsEnabled = featureFlags.getFlag(FEATURE_FLAGS.CONTROL_TAGS_ENABLED);
  const isEnterpriseV2Enabled = featureFlags.getFlag(FEATURE_FLAGS.IS_ENTERPRISE_V2_ENABLED);
  const isWorkspaceParent = featureFlags.getFlag(FEATURE_FLAGS.IS_WORKSPACE_PARENT);

  const initialValues = makeInitialValues({
    transaction,
    postings,
    dealerInfo,
    attachmentDetails,
    isWorkspaceParent,
    controlTagsEnabled: isControlTagsEnabled,
    accountsMapByDealerId,
    journalsMapByDealerId,
    hasInterFranchiseJEPermission,
    isEnterpriseJE: isEnterpriseV2Enabled,
    templatePostingPresetMap,
  });

  const initialValuesWithValidatedPostings = await getInitialValuesWithValidatedPostings({
    initialValues,
    programFieldMap,
    accountsMapByDealerId,
    journalsMapByDealerId,
    isEnterpriseV2Enabled,
    targetIFJournalByDealerId,
    accountingSettingsByDealerId,
    isMultiControlEnabled: isControlTagsEnabled,
    featureFlags,
  });

  return initialValuesWithValidatedPostings;
};

export default makeInitialValuesWithValidatedPostings;

export const __tests__ = exposeFunctionsIfTestEnvironment({ getControlValidator });
