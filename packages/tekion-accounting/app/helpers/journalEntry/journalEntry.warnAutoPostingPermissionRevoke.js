import React from 'react';

// Lodash
import _keys from 'lodash/keys';
import _flatMap from 'lodash/flatMap';
import _union from 'lodash/union';
import _compact from 'lodash/compact';
import _pickBy from 'lodash/pickBy';
import _at from 'lodash/at';
import _uniq from 'lodash/uniq';
import _join from 'lodash/join';

// Utils
import {
  isDraftedTransaction,
  isErroredTransaction,
} from 'tbusiness/appServices/accounting/utils/transaction/transactionUtils';

// Common
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';

// Helpers
import { isTransactionAutoPostingEditEnabled } from 'tbusiness/appServices/accounting/helpers/permissions';

// Constants
import FIELD_LABEL_MAP from 'twidgets/appServices/accounting/organisms/transactionPostingForm/helpers/baseFormConfig/constants/baseFormConfig.fieldLabelMap';
import { POSTING_TABLE_COLUMN_IDS_MAP } from 'twidgets/appServices/accounting/organisms/transactionPostingForm/organisms/postingTable/constants/postingTable.general';
import { BASE_FORM_FIELDS } from 'twidgets/appServices/accounting/organisms/transactionPostingForm';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { AUTO_POSTING, INTER_COMPANY_AUTO_POSTING } from 'tbusiness/appServices/accounting/constants/transactionTypes';
import { REACT_POST_PROCESSOR_NAME } from '@tekion/tekion-i18n/constants/i18next.postProcessor';
import { POPUP_AUTO_CLOSE_DELAY } from 'constants/journalEntry/journalEntry.general';

export const toastEditAutoPostingPermissionWarning = fieldLabelsWithError => {
  if (!fieldLabelsWithError) return;

  toaster(
    TOASTER_TYPE.WARN,
    __(
      'This Journal Entry has mandatory fields <fieldLabels> that are missing that will prevent submission. Please contact a user who can edit auto posting Journal Entries, or add the “Edit Auto-Posted Journal Entries” permission to your role to add the missing mandatory field.',
      {
        postProcess: REACT_POST_PROCESSOR_NAME,
        fieldLabels: <b>{fieldLabelsWithError}</b>,
      }
    ),
    { autoClose: POPUP_AUTO_CLOSE_DELAY }
  );
};

const isAutoPostingTransaction = transactionType =>
  [AUTO_POSTING, INTER_COMPANY_AUTO_POSTING].includes(transactionType);

export const checkAutoPostingEditPermission = (permissions, transactionType, status) => {
  const isAutoPosting = isAutoPostingTransaction(transactionType);
  const isDraftedOrErrored = isDraftedTransaction(status) || isErroredTransaction(status);
  if (!isAutoPosting || !isDraftedOrErrored) return true;

  const hasAutoPostingEditPermission = isTransactionAutoPostingEditEnabled(permissions);
  return hasAutoPostingEditPermission;
};

// Form error toast
const getFieldIdsWithError = formErrors => _keys(_pickBy(formErrors));

const getPostingFieldLabelsWithError = postingFieldErrors => {
  const postingIdsWithError = _uniq(_flatMap(postingFieldErrors, getFieldIdsWithError));

  return _at(POSTING_TABLE_COLUMN_IDS_MAP, postingIdsWithError);
};

const getFieldLabelsWithError = (formErrors = EMPTY_OBJECT, postingFieldKey = BASE_FORM_FIELDS.POSTINGS.id) => {
  const fieldIdsWithError = getFieldIdsWithError(formErrors);
  let fieldLabelsWithError = _at(FIELD_LABEL_MAP, fieldIdsWithError);

  const postingFieldErrors = formErrors[postingFieldKey];

  if (postingFieldErrors) {
    const postingFieldLabelsWithError = getPostingFieldLabelsWithError(postingFieldErrors);
    fieldLabelsWithError = _union(fieldLabelsWithError, postingFieldLabelsWithError);
  }

  return _join(_compact(fieldLabelsWithError), ', ');
};

export const warnIfAutoPostingEditPermissionRevoked = ({
  permissions,
  transactionType,
  status,
  formErrors,
  postingFieldKey,
}) => {
  if (checkAutoPostingEditPermission(permissions, transactionType, status)) return;

  const fieldLabelsWithError = getFieldLabelsWithError(formErrors, postingFieldKey);
  toastEditAutoPostingPermissionWarning(fieldLabelsWithError);
};
