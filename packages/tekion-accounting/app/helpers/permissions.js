// Constants
import { PERMISSIONS } from '@tekion/tekion-base/constants/permissions';

// Helpers
import { permissionValidations } from '@tekion/tekion-components/src/widgets/permissionsHelper';

export const hasCreateShellInvoicePermission = permissions => {
  const hasPermission = permissionValidations.validatePermissions(permissions, {
    validFor: [PERMISSIONS.ACCOUNTING.CUSTOMER_INVOICE.CREATE_SHELL_INVOICE],
  });
  return hasPermission;
};
