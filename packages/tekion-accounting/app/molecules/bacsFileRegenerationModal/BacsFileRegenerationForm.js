import React, { useCallback, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import moment from 'moment';

// Lodash
import _noop from 'lodash/noop';

// Containers
import withFormPageState from 'tcomponents/connectors/withFormPageState';

// Components
import { FormWithSubmission } from 'tcomponents/pages/formPage';

// Constants
import { BACS_FILE_REGENERATION_MODAL_CONTEXT_ID } from './constants/bacsFileRegenerationModal.general';

// Helpers
import { makeFormConfig, makeSections } from './helpers/bacsFileRegenerationModal.formConfig';
import { mapFormValuesToBacsRegenerationDetails, makeInitialValues } from './helpers/bacsFileRegenerationModal.general';

const FormWithState = withFormPageState()(FormWithSubmission);

const BacsFileRegenerationForm = props => {
  const { onSubmit, bacsPaymentDateLabel, initialPaymentDate } = props;
  const getFieldsRef = useRef(defaultMemoize(makeFormConfig(bacsPaymentDateLabel)));
  const getSectionsRef = useRef(defaultMemoize(makeSections));

  const initialFormValues = useMemo(() => makeInitialValues(initialPaymentDate), [initialPaymentDate]);

  const handleOnFormSubmit = useCallback(
    formValues => {
      const bacsRegenerationDetails = mapFormValuesToBacsRegenerationDetails(formValues);
      onSubmit(bacsRegenerationDetails);
    },
    [onSubmit]
  );

  return (
    <FormWithState
      getFields={getFieldsRef.current}
      getSections={getSectionsRef.current}
      onSubmit={handleOnFormSubmit}
      contextId={BACS_FILE_REGENERATION_MODAL_CONTEXT_ID}
      initialValues={initialFormValues}
    />
  );
};

BacsFileRegenerationForm.propTypes = {
  onSubmit: PropTypes.func,
  bacsPaymentDateLabel: PropTypes.string,
  initialPaymentDate: PropTypes.instanceOf(moment),
};

BacsFileRegenerationForm.defaultProps = {
  onSubmit: _noop,
  bacsPaymentDateLabel: '',
  initialPaymentDate: undefined,
};

export default BacsFileRegenerationForm;
