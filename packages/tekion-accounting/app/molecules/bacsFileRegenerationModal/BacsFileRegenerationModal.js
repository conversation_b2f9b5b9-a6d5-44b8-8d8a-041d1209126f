/* eslint-disable import/order */
import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

// Lodash
import _noop from 'lodash/noop';

// Constants
import {
  BACS_FILE_REGENERATION_MODAL_CONTEXT_ID,
  BACS_FILE_REGENERATION_MODAL_TITLE,
  BACS_FILE_REGENERATION_SUBMIT_BUTTON_TEXT,
} from './constants/bacsFileRegenerationModal.general';

// Components
import Modal from 'tcomponents/molecules/Modal';
import BacsFileRegenerationForm from './BacsFileRegenerationForm';

// Utils
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';

const BacsFileRegenerationModal = props => {
  const { isVisible, title, submitBtnText, isLoading, onSubmit, onCancel, initialPaymentDate, bacsPaymentDateLabel } =
    props;

  const handleSubmit = () => {
    triggerSubmit(BACS_FILE_REGENERATION_MODAL_CONTEXT_ID);
  };

  return (
    <Modal
      onCancel={onCancel}
      onSubmit={handleSubmit}
      title={title}
      submitBtnText={submitBtnText}
      visible={isVisible}
      width={Modal.SIZES.SM}
      destroyOnClose
      loading={isLoading}>
      <BacsFileRegenerationForm
        onSubmit={onSubmit}
        initialPaymentDate={initialPaymentDate}
        bacsPaymentDateLabel={bacsPaymentDateLabel}
      />
    </Modal>
  );
};

BacsFileRegenerationModal.propTypes = {
  isVisible: PropTypes.bool,
  onSubmit: PropTypes.func,
  onCancel: PropTypes.func,
  title: PropTypes.string,
  submitBtnText: PropTypes.string,
  initialPaymentDate: PropTypes.instanceOf(moment),
  isLoading: PropTypes.bool,
  bacsPaymentDateLabel: PropTypes.string,
};

BacsFileRegenerationModal.defaultProps = {
  isVisible: false,
  onSubmit: _noop,
  onCancel: _noop,
  title: BACS_FILE_REGENERATION_MODAL_TITLE,
  submitBtnText: BACS_FILE_REGENERATION_SUBMIT_BUTTON_TEXT,
  initialPaymentDate: undefined,
  isLoading: false,
  bacsPaymentDateLabel: undefined,
};

export default BacsFileRegenerationModal;
