// Utils
import { isRequiredRule } from 'tbase/utils/formValidators';

// Renderers
import InputDatePickerField from 'twidgets/fieldRenderers/inputDatePickerField';

// Helpers
import { isBacsPaymentDateInvalid, bacsPaymentDateValidator } from '../helpers/bacsFileRegenerationModal.validators';

// Constants
import FIELD_IDS from './bacsFileRegenerationModal.fieldIds';

export const PAYMENT_DATE = {
  id: FIELD_IDS.PAYMENT_DATE,
  renderer: InputDatePickerField,
  renderOptions: {
    required: true,
    validators: [isRequiredRule, bacsPaymentDateValidator],
    disabledDate: isBacsPaymentDateInvalid,
    placeholder: __('Select new payment date'),
  },
};
