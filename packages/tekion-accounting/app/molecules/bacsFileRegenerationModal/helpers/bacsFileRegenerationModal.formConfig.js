// Utils
import addToRenderOptions from 'tbase/utils/addToRenderOptions';

// Constants
import { FORM_CONFIG } from '../constants/bacsFileRegenerationModal.formConfig';
import { PAYMENT_DATE } from '../constants/bacsFileRegenerationModal.fields';
import FIELD_IDS from '../constants/bacsFileRegenerationModal.fieldIds';

export const makeFormConfig = bacsPaymentDateLabel => () => ({
  ...FORM_CONFIG,
  [PAYMENT_DATE.id]: addToRenderOptions(PAYMENT_DATE, [
    {
      path: 'label',
      value: bacsPaymentDateLabel,
    },
  ]),
});

export const makeSections = () => [
  {
    className: 'p-0',
    rows: [
      {
        columns: [FIELD_IDS.PAYMENT_DATE],
      },
    ],
  },
];
