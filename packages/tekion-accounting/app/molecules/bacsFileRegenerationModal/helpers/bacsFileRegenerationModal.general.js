// Lodash
import _isEmpty from 'lodash/isEmpty';

// Utils
import { getToday, toMoment } from 'tbase/utils/dateUtils';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import FIELD_IDS from '../constants/bacsFileRegenerationModal.fieldIds';

export const mapFormValuesToBacsRegenerationDetails = (formValues = EMPTY_OBJECT) => ({
  bacsFilePaymentDate: formValues[FIELD_IDS.PAYMENT_DATE],
});

export const makeInitialValues = initialPaymentDate => ({
  [FIELD_IDS.PAYMENT_DATE]: _isEmpty(initialPaymentDate) ? getToday() : initialPaymentDate,
});

export const makeRegenerateBacsFileRequest = (paymentIdentifier, checkType, bacsPaymentDetails = EMPTY_OBJECT) => {
  const { bacsFilePaymentDate = toMoment() } = bacsPaymentDetails;

  return { paymentIdentifier, paymentSubType: checkType, paymentDate: +bacsFilePaymentDate };
};
