// Utils
import { isValidDate } from 'tbase/utils/dateUtils';
import isDateAfterXDaysFromToday from 'utils/date/isDateAfterXDaysFromToday';
import isDateBeforeTodaysDate from 'utils/date/isDateBeforeTodaysDate';

// Constants
import { MAX_VALID_BACS_FILE_DAYS } from '../constants/bacsFileRegenerationModal.general';

export const isBacsPaymentDateInvalid = dateMoment => {
  const isPastDate = isDateBeforeTodaysDate(dateMoment);
  const isDateAfterMaxDaysFromToday = isDateAfterXDaysFromToday(MAX_VALID_BACS_FILE_DAYS, dateMoment);
  const isInvalidBacsPaymentDate = isPastDate || isDateAfterMaxDaysFromToday;
  return isInvalidBacsPaymentDate;
};

export const bacsPaymentDateValidator = (fieldId, bacsPaymentDate) => {
  if (!isValidDate(bacsPaymentDate)) {
    return { isValid: false, message: __('Invalid Date') };
  }

  const isInvalidBacsPaymentDate = isBacsPaymentDateInvalid(bacsPaymentDate);
  if (isInvalidBacsPaymentDate) {
    const isPastDate = isDateBeforeTodaysDate(bacsPaymentDate);

    if (isPastDate) {
      return {
        isValid: false,
        message: __('Payment date cannot be in the past'),
      };
    }

    return {
      isValid: false,
      message: __('Payment date cannot be more than {{maxValidBacsFileDateFromToday}} calendar days in the future', {
        maxValidBacsFileDateFromToday: MAX_VALID_BACS_FILE_DAYS,
      }),
    };
  }

  return { isValid: true };
};
