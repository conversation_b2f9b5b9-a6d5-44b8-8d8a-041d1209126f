// Constants
import PROGRAM_ACCOUNT_TYPES from 'tbusiness/appServices/accounting/constants/programAccountTypes';
import { CONTROL_TYPES } from 'tbase/constants/accounting/controlTypes';
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { getObjectWithOverriddenValues } from '@tekion/testing-utilities';

import { __tests__ } from '../configurableCreateGlAccountForm.makeSections';

const { getFieldRendererParams } = __tests__;

describe('getFieldRendererParams', () => {
  // Base test objects
  const BASE_EXPECTED_RESULT = {
    showUnitAnalysisGroup: false,
    showControlFormatField: false,
    disableAccount: false,
    showfield: false,
    showPrefixName: false,
  };

  // Base test data for input values
  const BASE_VALUES = {
    accountTypeId: PROGRAM_ACCOUNT_TYPES.OTHER,
    controlField: CONTROL_TYPES.OTHER,
  };

  const BASE_DEALER_ID = '12345';

  it('should return default params when no arguments are passed', () => {
    // Act
    const result = getFieldRendererParams({});

    // Assert
    expect(result).toStrictEqual(BASE_EXPECTED_RESULT);
  });

  it('should return default params when values is EMPTY_OBJECT', () => {
    // Act
    const result = getFieldRendererParams({ values: EMPTY_OBJECT });

    // Assert
    expect(result).toStrictEqual(BASE_EXPECTED_RESULT);
  });

  it('should set showUnitAnalysisGroup to true when accountTypeId is SALE', () => {
    // Arrange
    const values = getObjectWithOverriddenValues(BASE_VALUES, {
      accountTypeId: PROGRAM_ACCOUNT_TYPES.SALE,
    });

    const expectedResult = getObjectWithOverriddenValues(BASE_EXPECTED_RESULT, {
      showUnitAnalysisGroup: true,
    });

    // Act
    const result = getFieldRendererParams({ values });

    // Assert
    expect(result).toStrictEqual(expectedResult);
  });

  it('should set showControlFormatField to true when controlField is CUSTOM_FORMAT', () => {
    // Arrange
    const values = getObjectWithOverriddenValues(BASE_VALUES, {
      controlField: CONTROL_TYPES.CUSTOM_FORMAT,
    });

    const expectedResult = getObjectWithOverriddenValues(BASE_EXPECTED_RESULT, {
      showControlFormatField: true,
    });

    // Act
    const result = getFieldRendererParams({ values });

    // Assert
    expect(result).toStrictEqual(expectedResult);
  });

  it('should set both flags when accountTypeId is SALE and controlField is CUSTOM_FORMAT', () => {
    // Arrange
    const values = getObjectWithOverriddenValues(BASE_VALUES, {
      accountTypeId: PROGRAM_ACCOUNT_TYPES.SALE,
      controlField: CONTROL_TYPES.CUSTOM_FORMAT,
    });

    const expectedResult = getObjectWithOverriddenValues(BASE_EXPECTED_RESULT, {
      showUnitAnalysisGroup: true,
      showControlFormatField: true,
    });

    // Act
    const result = getFieldRendererParams({ values });

    // Assert
    expect(result).toStrictEqual(expectedResult);
  });

  it('should return default flags when accountTypeId and controlField do not match conditions', () => {
    // Arrange
    const values = BASE_VALUES;

    // Act
    const result = getFieldRendererParams({ values });

    // Assert
    expect(result).toStrictEqual(BASE_EXPECTED_RESULT);
  });

  it('should set showPrefixName to true when dealerId is in ledgerPrefixEnabledDealers', () => {
    // Arrange
    const dealerId = BASE_DEALER_ID;
    const ledgerPrefixEnabledDealers = [{ dealerId }];

    const expectedResult = getObjectWithOverriddenValues(BASE_EXPECTED_RESULT, {
      showPrefixName: true,
    });

    // Act
    const result = getFieldRendererParams({ values: EMPTY_OBJECT, ledgerPrefixEnabledDealers, dealerId });

    // Assert
    expect(result).toStrictEqual(expectedResult);
  });

  it('should set showPrefixName to false when dealerId is not in ledgerPrefixEnabledDealers', () => {
    // Arrange
    const dealerId = BASE_DEALER_ID;
    const ledgerPrefixEnabledDealers = [{ dealerId: '67890' }];

    // Act
    const result = getFieldRendererParams({ values: EMPTY_OBJECT, ledgerPrefixEnabledDealers, dealerId });

    // Assert
    expect(result).toStrictEqual(BASE_EXPECTED_RESULT);
  });

  it('should set showPrefixName to false when ledgerPrefixEnabledDealers is empty', () => {
    // Arrange
    const dealerId = BASE_DEALER_ID;

    // Act
    const result = getFieldRendererParams({ values: EMPTY_OBJECT, ledgerPrefixEnabledDealers: EMPTY_ARRAY, dealerId });

    // Assert
    expect(result).toStrictEqual(BASE_EXPECTED_RESULT);
  });
});
