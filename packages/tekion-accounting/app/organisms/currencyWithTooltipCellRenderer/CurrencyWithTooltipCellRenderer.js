import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

// Loadash
import _noop from 'lodash/noop';

// Components
import CurrencyInput from 'twidgets/appServices/accounting/organisms/currencyInput';
import Tooltip from 'tcomponents/atoms/tooltip';

// Constants
import { POPOVER_TRIGGER } from 'tcomponents/molecules/popover';

// Helpers
import { formatContent } from 'twidgets/appServices/accounting/cellRenderers/amountDisplayWithTooltipCellRenderer';

const CurrencyWithTooltipCellRenderer = props => {
  const { disabled, content, trigger, onChange, value, precision, min, max, suffix, prefix, hasError, ...restProps } =
    props;

  const formattedContent = useMemo(() => formatContent(content), [content]);

  if (disabled && content) {
    return (
      <Tooltip title={formattedContent} trigger={trigger}>
        <CurrencyInput
          disabled={disabled}
          onChange={onChange}
          value={value}
          precision={precision}
          min={min}
          max={max}
          suffix={suffix}
          prefix={prefix}
          hasError={hasError}
          {...restProps}
        />
      </Tooltip>
    );
  }

  return (
    <CurrencyInput
      disabled={disabled}
      onChange={onChange}
      value={value}
      precision={precision}
      min={min}
      max={max}
      suffix={suffix}
      prefix={prefix}
      hasError={hasError}
      {...restProps}
    />
  );
};

CurrencyWithTooltipCellRenderer.propTypes = {
  disabled: PropTypes.bool,
  content: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.node, PropTypes.array, PropTypes.object]),
  trigger: PropTypes.string,
  onChange: PropTypes.func,
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  precision: PropTypes.number,
  min: PropTypes.number,
  max: PropTypes.number,
  suffix: PropTypes.string,
  prefix: PropTypes.string,
  hasError: PropTypes.bool,
};

CurrencyWithTooltipCellRenderer.defaultProps = {
  disabled: false,
  content: undefined,
  trigger: POPOVER_TRIGGER.HOVER,
  onChange: _noop,
  precision: 2,
  hasError: false,
  value: undefined,
  min: undefined,
  max: undefined,
  suffix: undefined,
  prefix: undefined,
};

export default CurrencyWithTooltipCellRenderer;
