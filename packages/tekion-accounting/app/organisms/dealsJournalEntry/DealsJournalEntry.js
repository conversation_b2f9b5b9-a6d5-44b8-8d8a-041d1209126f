/* eslint-disable import/order */
/* eslint-disable react/sort-comp */
/* eslint no-param-reassign: 0 */
import React, { PureComponent } from 'react';
import { defaultMemoize } from 'reselect';
import PropTypes from 'prop-types';
import { compose, withProps } from 'recompose';

// Lodash
import _size from 'lodash/size';
import _noop from 'lodash/noop';

// Utils
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';

// Readers
import * as salesSetupReader from 'tbase/marketScan/readers/salesSetup.reader';
import dealerInfoReader from 'tbase/readers/DealerInfo';
import transactionReader from 'tbase/readers/Transaction';
import accountingSettingsReader from 'tbase/readers/accounting/AccountingSetting';
import { getBuyerInfo, getCustomerNumberForDisplay } from 'tbase/marketScan/readers/customer.reader';

// Containers
import withSize from 'tcomponents/hoc/withSize';
import withPropertyConsumer from 'tcomponents/organisms/propertyProvider/withPropertyConsumer';
import withPermissions from 'tcomponents/connectors/withPermissions';
import withFormPageState from 'tcomponents/connectors/withFormPageState';

// Builders
import ConfigurableEntityMetadata from 'tbusiness/builders/ConfigurableEntityMetadata';

// Constants
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { ACTION_TYPES } from './constants/dealsJournalEntry.actionTypes';
import { CENTRALISED_JOURNAL_ENTRIES } from 'tbase/constants/appConfigs/accountingAppConfigs';
import { INTER_COMPANY } from 'tbusiness/appServices/accounting/constants/transactionTypes';
import { WITH_SIZE_OPTIONS, FORM_VIEW_CONFIG_NAME } from './constants/dealsJournalEntry.general';

// Helpers
import { AmountInputFormatProvider } from 'twidgets/appServices/accounting/context/amountInputFormatContext';
import { isViewMode, isEditableDeal, getMultiControlProps } from './helpers/dealsJournalEntry.general';
import { getGrossAmountProps } from 'tbase/helpers/accounting/journalEntry';
import { fetchTargetJournalDetails } from 'tbusiness/appServices/accounting/helpers/interFranchise';
import ACTION_HANDLERS from './helpers/dealsJournalEntry.actionHandlers';
import makeFields from './helpers/dealsJournalEntry.createMakeFields';
import { makeExcludedAccountIdsMapForParticipatingDealers } from 'helpers/interFranchise/account';

// Common
import {
  ConfigurablePostedTransaction,
  TRANSACTION_FIELD_ID,
  getSections as makeSections,
} from 'twidgets/appServices/accounting/organisms/configurableTransaction';

// Components
import Button from 'tcomponents/atoms/Button';
import ConfigurableEditIFJE from 'twidgets/appServices/accounting/organisms/interFranchiseJournalEntry/organisms/configurableEditIFJE';
import Label from 'tcomponents/atoms/Label';
import Spinner from 'tcomponents/molecules/SpinnerComponent/Spinner';

// Providers
import DealerRoleInfoProvider from 'twidgets/providers/dealerRoleInfoProvider';
import ConfigurableBootstrapProvider from 'context/configurableBootstrapProvider';
import { withFeatureFlags, FeatureFlags } from 'twidgets/context/featureFlags';

// Styles
import styles from './dealsJournalEntry.module.scss';

class DealsJournalEntryContainer extends PureComponent {
  getGrossAmountProps = defaultMemoize(getGrossAmountProps);

  fetchTargetJournalDetails = defaultMemoize(fetchTargetJournalDetails);

  makeFields = defaultMemoize(makeFields);

  makeSections = defaultMemoize(makeSections);

  makeExcludedAccountIdsByDealerId = defaultMemoize(makeExcludedAccountIdsMapForParticipatingDealers);

  componentDidMount() {
    this.onInit();
  }

  componentDidUpdate(prevProps) {
    const { accountPostingId } = this.props;
    const { accountPostingId: prevAccountPostingId } = prevProps;
    if (prevAccountPostingId !== accountPostingId) {
      this.fetchOnInit();
    }
  }

  fetchOnInit() {
    const { onAction, accountPostingId, isResetting } = this.props;
    onAction({
      type: ACTION_TYPES.ON_INIT,
      payload: {
        accountPostingId,
        isResetting,
      },
    });
  }

  onInit() {
    const { fetchAccountingMetaData, fetchOffsetAccountIdsByDealerId, fetchTenantDealers } = this.props;

    fetchAccountingMetaData();
    fetchOffsetAccountIdsByDealerId();
    fetchTenantDealers();
    this.fetchOnInit();
  }

  resetWrapperFunc = () => {
    const { confirmationModal: ConfirmationModal } = this.props;
    return ConfirmationModal.show({
      title: __('Refresh Transaction Lines'),
      message: __('Are you sure want to Refresh Transaction Lines ? The changes are irreversible.'),
      submitBtnText: __('Refresh Transaction Lines'),
      onSubmitCb: async () => {
        ConfirmationModal.showLoader();
        await this.handleOnReset(true);
        ConfirmationModal.hideLoader();
        ConfirmationModal.close();
      },
    });
  };

  handleSaveAsDraft = async isReset => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SAVE_AS_DRAFT,
      payload: { isReset },
    });
  };

  handleSubmit = async (preClose = false) => {
    const { handleSubmit: parentHandleSubmit, formContextId } = this.props;
    const postDealProcessing = await parentHandleSubmit(preClose);
    if (postDealProcessing) return;
    const additional = { preClose };
    triggerSubmit(formContextId, additional);
  };

  handleSubmitWithPreClose = async () => {
    await this.handleSubmit(true);
  };

  handleSubmitWithOutPreClose = async () => {
    await this.handleSubmit(false);
  };

  getHeaderComponent = resumePrecloseDeal => {
    const {
      deal,
      isDealAcquired,
      salesSetupInfo,
      transactionPostingHeader: TransactionPostingHeader,
      accountPostingId,
      isPosting,
      isSavingDraft,
      transactionStatus,
      isResetting,
      hasTransactionPostingEdit,
      transaction,
    } = this.props;
    const isEditAutoPostedDealTransactionEnabled = hasTransactionPostingEdit();
    const { status: dealStatus, customers } = deal || EMPTY_OBJECT;
    const buyer = getBuyerInfo(customers);
    const customerDisplayNumber = getCustomerNumberForDisplay(buyer);
    const oldCustomerDisplayId = buyer?.oldDisplayId || null;
    const isEditable = isEditableDeal(isDealAcquired, hasTransactionPostingEdit);
    const transactionNumber = transaction?.transactionNumber;
    return (
      <TransactionPostingHeader
        accountPostingId={accountPostingId}
        onSubmit={this.handleSubmitWithOutPreClose}
        isPosting={isPosting}
        isSavingDraft={isSavingDraft}
        dealStatus={dealStatus}
        isEditable={isEditable}
        customerId={customerDisplayNumber}
        oldCustomerDisplayId={oldCustomerDisplayId}
        saveAsDraft={this.handleSaveAsDraft}
        transactionStatus={transactionStatus}
        isResetting={isResetting}
        reset={this.resetWrapperFunc}
        onPreclose={this.handleSubmitWithPreClose}
        canPreclose={salesSetupReader.isPrecloseFlowEnabled(salesSetupInfo)}
        resumedPreCloseDeal={resumePrecloseDeal}
        isEditAutoPostedDealTransactionEnabled={isEditAutoPostedDealTransactionEnabled}
        transactionNumber={transactionNumber}
      />
    );
  };

  handleOnReset = async (shouldReset, deal) => {
    const { onAction } = this.props;
    await onAction({
      type: ACTION_TYPES.RESET,
      payload: { shouldReset, deal },
    });
  };

  renderResumeButton = () => {
    const { resuming, handleOnResumedPreClosedDeal } = this.props;
    return (
      <Button view={Button.VIEW.TERTIARY} onClick={handleOnResumedPreClosedDeal} loading={resuming}>
        {__('Resume')}
      </Button>
    );
  };

  getResumeDetailsForPreclosedDeal = () => {
    const { deal, dealStatus } = this.props;
    const getResumePreclose =
      deal.status === dealStatus.PRE_CLOSED
        ? [
            {
              info: __('This deal has been Pre-closed. Click on resume to continue.'),
              priorityLevel: 1,
              id: 'PRE_CLOSE',
              renderActionComponents: this.renderResumeButton,
            },
          ]
        : null;

    return getResumePreclose;
  };

  fetchTargetJournalForDealerIds = async dealerIds => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.FETCH_TARGET_JOURNAL_FOR_DEALER_IDS,
      payload: { dealerIds },
    });
  };

  getFields(viewOnly) {
    const {
      entityMetadata,
      transaction,
      dealerInfo,
      journalsMapByDealerId,
      interFranchiseTransactionEnabledTenantDealersMap,
      setupFieldsByDealerId,
      accountsMapByDealerId,
      accountingSettingsByDealerId,
      activeMonthByDealerId,
      activeYearByDealerId,
      postingAllowedForUserByDealerId,
      bootstrapByDealerId,
      targetIFJournalByDealerId,
      featureFlags,
      values,
      programFieldMap,
      isConfigurableBootstrapLoadingStatusByDealerId,
      dealerInfoByDealerId,
      hasEditAutoPostedDealTransaction,
      onAction,
      distributionKeysMapByDealerId,
      apSetupByDealerId,
      ifClearingAccountIdsByDealerId,
      salesChainListByDealerId,
      lookupAssetSupportMap,
      permissions,
    } = this.props;
    const isEditAutoPostedDealTransactionEnabled = hasEditAutoPostedDealTransaction();
    const excludedAccountIdsByDealerId = this.makeExcludedAccountIdsByDealerId(
      apSetupByDealerId,
      ifClearingAccountIdsByDealerId,
      interFranchiseTransactionEnabledTenantDealersMap
    );
    const fields = this.makeFields(
      entityMetadata,
      transaction,
      dealerInfo,
      journalsMapByDealerId,
      interFranchiseTransactionEnabledTenantDealersMap,
      setupFieldsByDealerId,
      accountsMapByDealerId,
      accountingSettingsByDealerId,
      activeMonthByDealerId,
      activeYearByDealerId,
      postingAllowedForUserByDealerId,
      bootstrapByDealerId,
      targetIFJournalByDealerId,
      this.fetchTargetJournalForDealerIds,
      featureFlags,
      values,
      programFieldMap,
      isConfigurableBootstrapLoadingStatusByDealerId,
      dealerInfoByDealerId,
      isEditAutoPostedDealTransactionEnabled,
      viewOnly,
      onAction,
      distributionKeysMapByDealerId,
      excludedAccountIdsByDealerId,
      salesChainListByDealerId,
      lookupAssetSupportMap,
      permissions
    );
    return fields;
  }

  renderStickyBanner(resumePrecloseDeal) {
    const { stickyBanner: StickyBanner } = this.props;
    return _size(resumePrecloseDeal) ? <StickyBanner reminders={resumePrecloseDeal} onClose={this.onClose} /> : null;
  }

  render() {
    const {
      isDealAcquired,
      contentHeight,
      dealerInfo,
      accountsMapByDealerId,
      centralisedEnabledApps,
      permissions,
      loading,
      entityMetadata,
      programFieldMap,
      setupFields,
      isTransactionDetailSecured,
      dealerInfoByDealerId,
      onAction,
      accountingSettingsByDealerId,
      values,
      isTransactionFetched,
      journalsMapByDealerId,
      initialError,
      errors,
      isViewOnly,
      transaction,
      targetIFJournalByDealerId,
      getTenantPropertyValue,
      featureFlags,
      setupFieldsByDealerId,
      hasTransactionPostingEdit,
      isLoading,
      formContextId,
      alignContentCenter: AlignContentCenter,
    } = this.props;
    if (loading || !isTransactionFetched || isLoading) return <AlignContentCenter content={<Spinner />} />;

    if (initialError) {
      return <AlignContentCenter content={<Label className={styles.errorLabel}>{__('Error!')}</Label>} />;
    }

    const viewOnly = isViewMode(isDealAcquired, isViewOnly, hasTransactionPostingEdit);

    const fields = this.getFields(viewOnly);
    const referenceType = values[TRANSACTION_FIELD_ID.REFERENCE_TYPE];

    const sections = this.makeSections(
      entityMetadata,
      referenceType,
      INTER_COMPANY,
      undefined,
      false,
      permissions,
      true,
      FORM_VIEW_CONFIG_NAME,
      false,
      undefined,
      false,
      featureFlags
    );

    const currentDealerId = dealerInfoReader.id(dealerInfo);

    const accountingSettings = accountingSettingsByDealerId[currentDealerId];
    const accountsMap = accountsMapByDealerId[currentDealerId];
    const journalsMap = journalsMapByDealerId[currentDealerId];
    const transactionType = transactionReader.transactionType(transaction);
    const includeTwoDecimalsInAmountAutomatically =
      accountingSettingsReader.includeTwoDecimalPlacesInAmountFields(accountingSettings);

    const resumePrecloseDeal = this.getResumeDetailsForPreclosedDeal();

    if (viewOnly) {
      const grossAmountProps = this.getGrossAmountProps(accountingSettings, accountsMap, transaction);

      return (
        <>
          {this.renderStickyBanner(resumePrecloseDeal)}
          <ConfigurableBootstrapProvider>
            <ConfigurablePostedTransaction
              headerComponent={() => this.getHeaderComponent(resumePrecloseDeal)}
              values={values}
              entityMetadata={entityMetadata}
              journalsMap={journalsMap}
              programFieldMap={programFieldMap}
              dealerInfo={dealerInfo}
              accountsMap={accountsMap}
              transactionType={transactionType}
              accountingSettings={accountingSettings}
              shouldEnableEditing={false}
              setupFields={setupFields}
              contentHeight={contentHeight}
              grossAmountProps={grossAmountProps}
              isTransactionDetailSecured={isTransactionDetailSecured}
              dealerInfoByDealerId={dealerInfoByDealerId}
              transaction={transaction}
              onAction={onAction}
              centralisedEnabledApps={centralisedEnabledApps}
              hideReferenceRedirectionIcon
            />
          </ConfigurableBootstrapProvider>
        </>
      );
    }

    const grossAmountProps = this.getGrossAmountProps(accountingSettings, accountsMap, transaction, true);

    return (
      <DealerRoleInfoProvider>
        <ConfigurableBootstrapProvider>
          <AmountInputFormatProvider value={includeTwoDecimalsInAmountAutomatically}>
            <ConfigurableEditIFJE
              dealerInfo={dealerInfo}
              headerComponent={() => this.getHeaderComponent(resumePrecloseDeal)}
              formFields={fields}
              sections={sections}
              onClose={this.onClose}
              makeEditTransactionSubmitData={this.makeCreateTransactionSubmitData}
              journalsMap={journalsMap}
              setupFieldsByDealerId={setupFieldsByDealerId}
              accountsMapByDealerId={accountsMapByDealerId}
              contextId={formContextId}
              onAction={onAction}
              values={values}
              errors={errors}
              useKeyBoardShortcuts
              targetIFJournalByDealerId={targetIFJournalByDealerId}
              getTenantPropertyValue={getTenantPropertyValue}
              featureFlags={featureFlags}
              centralisedAppKey={CENTRALISED_JOURNAL_ENTRIES.getKey()}
              isIFUserSecurityEnabled={false}
              grossAmountProps={grossAmountProps}
            />
          </AmountInputFormatProvider>
        </ConfigurableBootstrapProvider>
      </DealerRoleInfoProvider>
    );
  }
}

DealsJournalEntryContainer.propTypes = {
  deal: PropTypes.object,
  isDealAcquired: PropTypes.bool,
  contentHeight: PropTypes.number.isRequired,
  salesSetupInfo: PropTypes.object.isRequired,
  accountsMapByDealerId: PropTypes.object,
  bootstrapByDealerId: PropTypes.object,
  journalsMapByDealerId: PropTypes.object,
  centralisedEnabledApps: PropTypes.array,
  featureFlags: PropTypes.instanceOf(FeatureFlags).isRequired,
  dealerInfo: PropTypes.object,
  transactionPostingHeader: PropTypes.elementType.isRequired,
  dealStatus: PropTypes.object.isRequired,
  stickyBanner: PropTypes.elementType.isRequired,
  hasTransactionPostingEdit: PropTypes.func.isRequired,
  hasEditAutoPostedDealTransaction: PropTypes.func.isRequired,
  isTransactionFetched: PropTypes.bool,
  accountPostingId: PropTypes.string,
  permissions: PropTypes.array.isRequired,
  loading: PropTypes.bool,
  entityMetadata: PropTypes.instanceOf(ConfigurableEntityMetadata),
  programFieldMap: PropTypes.object,
  setupFields: PropTypes.object,
  isTransactionDetailSecured: PropTypes.bool,
  dealerInfoByDealerId: PropTypes.object,
  onAction: PropTypes.func.isRequired,
  accountingSettingsByDealerId: PropTypes.object,
  values: PropTypes.object,
  initialError: PropTypes.string,
  errors: PropTypes.object,
  isViewOnly: PropTypes.bool,
  transactionStatus: PropTypes.string,
  transaction: PropTypes.object,
  targetIFJournalByDealerId: PropTypes.object,
  getTenantPropertyValue: PropTypes.func.isRequired,
  setupFieldsByDealerId: PropTypes.object,
  isLoading: PropTypes.bool,
  formContextId: PropTypes.string.isRequired,
  distributionKeysMapByDealerId: PropTypes.object,
  apSetupByDealerId: PropTypes.object,
  ifClearingAccountIdsByDealerId: PropTypes.object,
  fetchAccountingMetaData: PropTypes.func,
  fetchOffsetAccountIdsByDealerId: PropTypes.func,
  fetchTenantDealers: PropTypes.func,
  handleSubmit: PropTypes.func,
  salesChainListByDealerId: PropTypes.object,
  lookupAssetSupportMap: PropTypes.object,
  interFranchiseTransactionEnabledTenantDealersMap: PropTypes.object,
  activeMonthByDealerId: PropTypes.object,
  activeYearByDealerId: PropTypes.object,
  postingAllowedForUserByDealerId: PropTypes.object,
  confirmationModal: PropTypes.object.isRequired,
  isResetting: PropTypes.bool,
  isPosting: PropTypes.bool,
  isSavingDraft: PropTypes.bool,
  resuming: PropTypes.bool,
  handleOnResumedPreClosedDeal: PropTypes.func.isRequired,
  isConfigurableBootstrapLoadingStatusByDealerId: PropTypes.object,
  alignContentCenter: PropTypes.elementType.isRequired,
};

DealsJournalEntryContainer.defaultProps = {
  deal: EMPTY_OBJECT,
  isDealAcquired: false,
  accountsMapByDealerId: EMPTY_OBJECT,
  bootstrapByDealerId: EMPTY_OBJECT,
  journalsMapByDealerId: EMPTY_OBJECT,
  centralisedEnabledApps: EMPTY_ARRAY,
  dealerInfo: EMPTY_OBJECT,
  isTransactionFetched: false,
  accountPostingId: undefined,
  loading: false,
  entityMetadata: undefined,
  programFieldMap: EMPTY_OBJECT,
  setupFields: EMPTY_ARRAY,
  isTransactionDetailSecured: false,
  dealerInfoByDealerId: EMPTY_OBJECT,
  accountingSettingsByDealerId: EMPTY_OBJECT,
  values: EMPTY_OBJECT,
  initialError: undefined,
  errors: EMPTY_OBJECT,
  isViewOnly: false,
  transactionStatus: undefined,
  transaction: EMPTY_OBJECT,
  targetIFJournalByDealerId: EMPTY_OBJECT,
  setupFieldsByDealerId: EMPTY_OBJECT,
  isLoading: false,
  distributionKeysMapByDealerId: EMPTY_OBJECT,
  apSetupByDealerId: EMPTY_OBJECT,
  ifClearingAccountIdsByDealerId: EMPTY_OBJECT,
  fetchAccountingMetaData: _noop,
  fetchOffsetAccountIdsByDealerId: _noop,
  fetchTenantDealers: _noop,
  handleSubmit: _noop,
  salesChainListByDealerId: EMPTY_OBJECT,
  lookupAssetSupportMap: EMPTY_OBJECT,
  interFranchiseTransactionEnabledTenantDealersMap: EMPTY_OBJECT,
  activeMonthByDealerId: EMPTY_OBJECT,
  activeYearByDealerId: EMPTY_OBJECT,
  postingAllowedForUserByDealerId: EMPTY_OBJECT,
  isResetting: false,
  isPosting: false,
  isSavingDraft: false,
  resuming: false,
  isConfigurableBootstrapLoadingStatusByDealerId: EMPTY_OBJECT,
};

export default compose(
  withPropertyConsumer,
  withPermissions,
  withProps(getMultiControlProps),
  withFeatureFlags,
  withSize(WITH_SIZE_OPTIONS),
  withFormPageState(undefined, ACTION_HANDLERS)
)(DealsJournalEntryContainer);
