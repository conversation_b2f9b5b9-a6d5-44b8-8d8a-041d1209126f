/* eslint-disable import/order */
import { compose } from 'recompose';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';

// Component
import DealsJournalEntry from './DealsJournalEntry';

// Constants
import { ACCOUNTING } from 'tbase/constants/appServices';
import ASSET_TYPES_BY_MODULE from 'tbusiness/constants/configurableEntity/assetTypesByModule';

// Actions
import { getConfigurableEntityMetadata as getConfigurableEntityMetadataAction } from 'tbusiness/actions/configurableEntity';
import { fetchAccountingMetaData } from './actions/dealsJournalEntry.metaData';
import { fetchOffsetAccountIdsByDealerIdAction } from 'actions/offsets.actions';
import { fetchTenantDealersAction } from 'actions/tenantDealers.actions';

// Containers
import withAsyncReducer from 'tcomponents/connectors/withAsyncReducer';
import withConfigurableEntityMetadata from 'containers/withConfigurableEntityMetadata';
import withParentSalesChain from 'containers/withParentSalesChain';

// Builders
import ConfigurableEntityMetadata from 'tbusiness/builders/ConfigurableEntityMetadata';

// Reducers
import dealsJournalEntryReducer from './reducers/index';

// Selectors
import { getConfigurableEntityMetadata as getConfigurableEntityMetadataSelector } from 'tbusiness/reducers/configurableEntity';
import {
  getAllAccountsMap,
  getAllJournalsMap,
  getSetupFields,
  getCurrentActiveMonth,
  getActiveYear,
  isPostingAllowedForUser,
  getApSetup,
  getDistributionKeysMap,
  getArSetup,
  getAccountingSettings,
  checkIfMultiControlInputDrawerIsVisible,
  getIsBootstrapLoading,
  getAccountsMap,
  getAccountsMapByDealerId,
  getAllAccountsMapByDealerId,
  getJournalsMapByDealerId,
  getSetupFieldsByDealerId,
  getCurrentActiveMonthByDealerId,
  getActiveYearByDealerId,
  isPostingAllowedForUserByDealerId,
  getApSetupByDealerId,
  getArSetupByDealerId,
  getAccountingSettingsByDealerId,
  getProgramFieldMap,
  getBootstrapByDealerIdMap,
  getDistributionKeysMapByDealerId,
  getSalesChainListByDealerId,
  getLookupAssetSupportMap,
  getBootstrapByDealerIdLoadingData,
} from './reducers/selectors/dealsJournalEntry.selectors';
import { getCentralisedEnabledApps } from 'twidgets/organisms/AppSkeleton/appSkeleton/containers/withCentralApplications/reducers/withCentralApplications.selectors';
import { getLoginData } from 'tcomponents/pages/authentication/reducers/authentication.selectors';
import { getCustomFieldsByDealerId } from 'reducers/selectors/appCache/customFieldsByDealerId';
import {
  getIFClearingAccountIds,
  getIFClearingAccountIdsByDealerId,
  getInterFranchiseTransactionEnabledTenantDealersMap,
} from 'reducers/selectors/appCache/offsets';
import { getTenantDealersById } from 'reducers/selectors/appCache/tenantDealers';

const mapStateToProps = (state, props) => {
  const { dealerInfo } = props;
  return {
    entityMetadata: getConfigurableEntityMetadataSelector(
      state,
      ASSET_TYPES_BY_MODULE[ACCOUNTING].JOURNAL_ENTRY,
      ACCOUNTING
    ),
    isLoading: getIsBootstrapLoading(state),
    allAccountsMap: getAllAccountsMap(state),
    journalsMap: getAllJournalsMap(state),
    setupFields: getSetupFields(state),
    activeMonth: getCurrentActiveMonth(state),
    activeYear: getActiveYear(state),
    postingAllowedForUser: isPostingAllowedForUser(state),
    apSetup: getApSetup(state),
    distributionKeysMap: getDistributionKeysMap(state),
    arSetup: getArSetup(state),
    accountingSettings: getAccountingSettings(state),
    centralisedEnabledApps: getCentralisedEnabledApps(state),
    isMultiControlInputDrawerVisible: checkIfMultiControlInputDrawerIsVisible(state),
    loginData: getLoginData(state),
    accountsMap: getAccountsMap(state),
    accountsMapByDealerId: getAccountsMapByDealerId(state, props),
    allAccountsMapByDealerId: getAllAccountsMapByDealerId(state, props),
    journalsMapByDealerId: getJournalsMapByDealerId(state, props),
    setupFieldsByDealerId: getSetupFieldsByDealerId(state, props),
    activeMonthByDealerId: getCurrentActiveMonthByDealerId(state, props),
    activeYearByDealerId: getActiveYearByDealerId(state, props),
    postingAllowedForUserByDealerId: isPostingAllowedForUserByDealerId(state, props),
    apSetupByDealerId: getApSetupByDealerId(state, props),
    arSetupByDealerId: getArSetupByDealerId(state, props),
    accountingSettingsByDealerId: getAccountingSettingsByDealerId(state, props),
    customFieldsByDealerId: getCustomFieldsByDealerId(state),
    ifClearingAccountIds: getIFClearingAccountIds(state, dealerInfo),
    ifClearingAccountIdsByDealerId: getIFClearingAccountIdsByDealerId(state),
    dealerInfoByDealerId: getTenantDealersById(state),
    programFieldMap: getProgramFieldMap(state),
    bootstrapByDealerId: getBootstrapByDealerIdMap(state),
    interFranchiseTransactionEnabledTenantDealersMap: getInterFranchiseTransactionEnabledTenantDealersMap(state),
    distributionKeysMapByDealerId: getDistributionKeysMapByDealerId(state, props),
    salesChainListByDealerId: getSalesChainListByDealerId(state, props),
    lookupAssetSupportMap: getLookupAssetSupportMap(state),
    getBootstrapByDealerIdLoadingData: getBootstrapByDealerIdLoadingData(state),
  };
};

const mapDispatchToProps = {
  fetchAccountingMetaData,
  fetchOffsetAccountIdsByDealerId: fetchOffsetAccountIdsByDealerIdAction,
  fetchTenantDealers: fetchTenantDealersAction,
  getConfigurableEntityMetadata: getConfigurableEntityMetadataAction,
};

DealsJournalEntry.propTypes = {
  entityMetadata: PropTypes.instanceOf(ConfigurableEntityMetadata).isRequired,
  isLoading: PropTypes.bool.isRequired,
  allAccountsMap: PropTypes.object.isRequired,
  journalsMap: PropTypes.object.isRequired,
  setupFields: PropTypes.object.isRequired,
  activeMonth: PropTypes.string.isRequired,
  activeYear: PropTypes.number.isRequired,
  postingAllowedForUser: PropTypes.bool.isRequired,
  apSetup: PropTypes.object.isRequired,
  distributionKeysMap: PropTypes.object.isRequired,
  arSetup: PropTypes.object.isRequired,
  accountingSettings: PropTypes.object.isRequired,
  centralisedEnabledApps: PropTypes.array.isRequired,
  isMultiControlInputDrawerVisible: PropTypes.bool.isRequired,
  loginData: PropTypes.object.isRequired,
  accountsMap: PropTypes.object.isRequired,
  accountsMapByDealerId: PropTypes.object.isRequired,
  allAccountsMapByDealerId: PropTypes.object.isRequired,
  journalsMapByDealerId: PropTypes.object.isRequired,
  setupFieldsByDealerId: PropTypes.object.isRequired,
  activeMonthByDealerId: PropTypes.object.isRequired,
  activeYearByDealerId: PropTypes.object.isRequired,
  postingAllowedForUserByDealerId: PropTypes.object.isRequired,
  apSetupByDealerId: PropTypes.object.isRequired,
  arSetupByDealerId: PropTypes.object.isRequired,
  accountingSettingsByDealerId: PropTypes.object.isRequired,
  customFieldsByDealerId: PropTypes.object.isRequired,
  ifClearingAccountIds: PropTypes.array.isRequired,
  ifClearingAccountIdsByDealerId: PropTypes.object.isRequired,
  dealerInfoByDealerId: PropTypes.object.isRequired,
  programFieldMap: PropTypes.object.isRequired,
  bootstrapByDealerId: PropTypes.object.isRequired,
  interFranchiseTransactionEnabledTenantDealersMap: PropTypes.object.isRequired,
  distributionKeysMapByDealerId: PropTypes.object.isRequired,
  salesChainListByDealerId: PropTypes.object.isRequired,
  lookupAssetSupportMap: PropTypes.object.isRequired,
  getBootstrapByDealerIdLoadingData: PropTypes.object.isRequired,
  fetchAccountingMetaData: PropTypes.func.isRequired,
  fetchOffsetAccountIdsByDealerId: PropTypes.func.isRequired,
  fetchTenantDealers: PropTypes.func.isRequired,
  getConfigurableEntityMetadata: PropTypes.func.isRequired,
};

export default compose(
  withAsyncReducer({ bulkReducers: dealsJournalEntryReducer }),
  withConfigurableEntityMetadata([ASSET_TYPES_BY_MODULE[ACCOUNTING].JOURNAL_ENTRY]),
  withParentSalesChain,
  connect(mapStateToProps, mapDispatchToProps)
)(DealsJournalEntry);
