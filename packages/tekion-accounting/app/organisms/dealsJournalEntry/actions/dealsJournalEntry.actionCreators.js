import { createAction } from 'redux-actions';

// Actions
import { ACTIONS } from '../constants/dealsJournalEntry.actionTypes';

export const createFetchAccountingBootStrapDataAction = createAction(ACTIONS.FETCH_ACCOUNTING_BOOTSTRAP_DATA);

export const createFetchAccountingBootStrapDataSuccessAction = createAction(
  ACTIONS.FETCH_ACCOUNTING_BOOTSTRAP_DATA_SUCCESS
);

export const createFetchAccountingBootStrapDataFailureAction = createAction(
  ACTIONS.FETCH_ACCOUNTING_BOOTSTRAP_DATA_FAILURE
);
