// Lodash
import _keyBy from 'lodash/keyBy';

import { compose } from 'recompose';

// Utils
import getDataFromAllResponse from 'tbusiness/utils/getDataFromAllResponse';

// Middlewares
import { sendException } from 'twidgets/third-party/errorTracker';

// Services
import {
  fetchBootstrapData,
  fetchCustomFieldSettings,
  fetchAdditionalProgramFieldMap,
} from '../services/dealsJournalEntry.services';

// Action Creators
import {
  createFetchAccountingBootStrapDataAction,
  createFetchAccountingBootStrapDataSuccessAction,
  createFetchAccountingBootStrapDataFailureAction,
} from './dealsJournalEntry.actionCreators';

const handleFetchCrucialMetadataSuccess =
  dispatch =>
  ([bootstrapData, setupFields, additionalProgramFieldMap]) => {
    const fieldsMapToId = _keyBy(setupFields, 'id');
    compose(
      dispatch,
      createFetchAccountingBootStrapDataSuccessAction
    )({ bootstrapData, fieldsMapToId, additionalProgramFieldMap });
  };

const handleFetchCrucialMetadataFailure = dispatch => err => {
  compose(dispatch, createFetchAccountingBootStrapDataFailureAction)(err);
  sendException(new Error('Failed to fetch crucial accounting metadata'));
};

export const fetchAccountingMetaData = () => dispatch => {
  compose(dispatch, createFetchAccountingBootStrapDataAction)();
  return Promise.all([fetchBootstrapData(), fetchCustomFieldSettings(), fetchAdditionalProgramFieldMap()])
    .then(getDataFromAllResponse)
    .then(handleFetchCrucialMetadataSuccess(dispatch))
    .catch(handleFetchCrucialMetadataFailure(dispatch));
};
