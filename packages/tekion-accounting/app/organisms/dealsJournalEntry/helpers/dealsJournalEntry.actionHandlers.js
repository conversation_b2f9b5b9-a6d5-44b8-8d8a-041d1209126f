/* eslint-disable import/order */
// Lodash
import _isEmpty from 'lodash/isEmpty';
import _merge from 'lodash/merge';

// Helpers
import {
  makeInitialValues,
  resolveTransaction,
  getTransactionDetailsV2,
  TRANSACTION_FIELD_ID,
} from 'twidgets/appServices/accounting/organisms/configurableTransaction';
import { BASE_ACTION_HANDLERS } from 'twidgets/appServices/accounting/organisms/configurableTransaction/organisms/configurableEditTransaction';
import makeInitialValuesWithValidatedPostings from 'helpers/journalEntry/journalEntry.makeInitialValuesWithValidatedPostings';
import { warnIfAutoPostingEditPermissionRevoked } from 'helpers/journalEntry/journalEntry.warnAutoPostingPermissionRevoke';
import { getDraftPayloads, makePostTransactionDTO } from './dealsJournalEntry.request';
import { getRequestDTOMetadata } from './dealsJournalEntry.general';
import { fetchTargetIFJournalByDealerId } from 'twidgets/appServices/accounting/organisms/interFranchiseJournalEntry/organisms/configurableEditIFJE';
import { hasIFJournalEntryPermission } from 'tbusiness/appServices/accounting/helpers/permissions';

// Components
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';

// Utils
import getDataFromResponse from 'tbase/utils/getDataFromResponse';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { STATUS } from 'tbase/constants/statuses';
import { ACTION_TYPES } from '../constants/dealsJournalEntry.actionTypes';
import BUSINESS_FLOWS from 'twidgets/appServices/accounting/constants/journalEntriesBusinessFlows';
import FORM_ACTION_TYPES from 'tcomponents/organisms/FormBuilder/constants/actionTypes';

// Readers
import dealerInfoReader from 'tbase/readers/DealerInfo';
import transactionDetailsReader from 'tbusiness/appServices/accounting/readers/TransactionDetails';
import transactionReader from 'tbase/readers/Transaction';
import { getDealNumber } from 'tbase/marketScan/readers/deal.reader';

// Service
import { getInitialFormValues } from '../services/dealsJournalEntry.services';

const getDealPostingsDataFromResponse = response => {
  const initialFormData = getDataFromResponse(response);
  const { arInvoice, createDto } = initialFormData || EMPTY_OBJECT;
  const updatedDTO = { ...createDto, [TRANSACTION_FIELD_ID.POSTINGS]: createDto?.postings };
  return { initialFormValues: updatedDTO, arInvoice, initialTransactionDto: createDto };
};

export const getDealPostingsData = async (deal, getAPIError) => {
  try {
    const response = await getInitialFormValues(deal);
    return getDealPostingsDataFromResponse(response);
  } catch (error) {
    const msg = getAPIError(error) || __('Error in getDealPostingsData');
    return {
      error: msg,
    };
  }
};

const setValues = async ({ getState, setState, transaction = EMPTY_OBJECT, initialTransactionDto, arInvoice }) => {
  const {
    dealerInfo,
    featureFlags,
    accountsMapByDealerId,
    accountingSettingsByDealerId,
    journalsMapByDealerId,
    programFieldMap,
  } = getState();
  const postings = transactionDetailsReader.postings(transaction);
  const attachmentDetails = transactionDetailsReader.attachmentDetails(transaction);
  const initialValuesWithValidatedPostings = await makeInitialValuesWithValidatedPostings({
    transaction,
    postings,
    dealerInfo,
    featureFlags,
    attachmentDetails,
    accountsMapByDealerId,
    accountingSettingsByDealerId,
    journalsMapByDealerId,
    programFieldMap,
  });
  setState({
    values: initialValuesWithValidatedPostings,
    isTransactionDetailSecured: false,
    isTransactionFetched: true,
    isViewOnly: false,
    isResetting: false,
    initialTransactionDto,
    arInvoice,
  });
};

const getTransactionDetails = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { accountPostingId } = params;
  const { accountsMapByDealerId = EMPTY_OBJECT, dealerInfo } = getState();
  const sourceDealerId = dealerInfoReader.id(dealerInfo);
  const accountsMap = accountsMapByDealerId[sourceDealerId];
  const transactionDetails = await getTransactionDetailsV2(accountPostingId, sourceDealerId);
  const transaction = transactionDetailsReader.transaction(transactionDetails);
  const postings = transactionDetailsReader.postings(transactionDetails);
  const attachmentDetails = transactionDetailsReader.attachmentDetails(transactionDetails);
  const isSecured = transactionDetailsReader.isSecured(transactionDetails);
  const resolvedTransaction = await resolveTransaction(transaction);
  const initialValues = makeInitialValues({
    transaction: resolvedTransaction,
    postings,
    dealerInfo,
    accountsMap,
    attachmentDetails,
    isEnterpriseJE: true,
  });
  setState({
    values: initialValues,
    isTransactionDetailSecured: isSecured,
    isTransactionFetched: true,
    transaction,
    isViewOnly: true,
    transactionStatus: transactionReader.status(transaction),
  });
};

const handleOnInit = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { isResetting, accountPostingId } = params;
  const {
    deal,
    draftData,
    getAPIError,
    initialTransactionDto: initialTransactionDtoFromState,
    arInvoice: arInvoiceFromState,
  } = getState();

  if (!_isEmpty(draftData)) {
    setValues({
      getState,
      setState,
      transaction: draftData,
      initialTransactionDto: initialTransactionDtoFromState,
      arInvoice: arInvoiceFromState,
    });
  } else if (isResetting || !accountPostingId) {
    const { initialFormValues, initialTransactionDto, arInvoice } = await getDealPostingsData(deal, getAPIError);
    setValues({ getState, setState, transaction: initialFormValues, initialTransactionDto, arInvoice });
  } else {
    await getTransactionDetails({ getState, setState, params });
  }
};

const handleSaveAsDraft = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const {
    handleSaveAsDraft: parentSaveAsDraft,
    values,
    initialTransactionDto,
    draftData,
    targetIFJournalByDealerId,
    deal,
    accountsMapByDealerId,
    journalsMapByDealerId,
    dealerInfo,
    messages,
    permissions,
  } = getState();
  const { isReset } = params;
  const { dealNumber } = deal;

  if (!isReset) setState({ isSavingDraft: true });
  const hasInterFranchiseJEPermission = hasIFJournalEntryPermission(permissions);
  const successMsg = isReset ? __('Refresh Transaction Lines Successfull') : __('Draft saved successfully');
  const payload = getDraftPayloads({
    values,
    initialTransactionDto,
    draftData,
    accountsMapByDealerId,
    journalsMapByDealerId,
    dealerInfo,
    targetIFJournalByDealerId,
    hasInterFranchiseJEPermission,
  });

  const response = await parentSaveAsDraft(dealNumber, payload);
  if (response?.error) {
    setState({ isSavingDraft: false });
    return toaster(TOASTER_TYPE.ERROR, messages?.DEFAULT_ERROR);
  }
  setState({ transactionStatus: STATUS.DRAFT });
  toaster(TOASTER_TYPE.SUCCESS, successMsg);
  return setState({ isSavingDraft: false });
};

export const fetchTargetJournalForDealerIds = async ({ getState, setState, params }) => {
  const { dealerIds, transaction } = params;
  const {
    targetIFJournalByDealerId = EMPTY_OBJECT,
    values = EMPTY_OBJECT,
    initialTransactionDto,
    draftData,
  } = getState();
  const sourceDealerId = values[TRANSACTION_FIELD_ID.FRANCHISE] ?? transactionReader.dealerId(transaction);

  const transactionMetadata = getRequestDTOMetadata({
    initialTransactionDto,
    draftData,
    businessFlow: BUSINESS_FLOWS.DEAL_POSTING_FLOW,
  });

  const newTargetIFJournalByDealerId = await fetchTargetIFJournalByDealerId({
    sourceDealerId,
    dealerIds,
    transactionMetadata,
  });
  const updatedTargetIFJournalByDealerId = _merge({}, targetIFJournalByDealerId, newTargetIFJournalByDealerId);
  setState({ targetIFJournalByDealerId: updatedTargetIFJournalByDealerId });
};

const handleFormSubmit = async ({ getState, params = EMPTY_OBJECT }) => {
  const { submitData } = params;
  const {
    handleOnFormSubmit: parentOnFormSubmit,
    preClose,
    initialTransactionDto,
    draftData,
    deal,
    isMultiControlEnabled,
    preCloseIdempotentKey,
    idempotentKey,
    permissions,
  } = getState();
  const hasInterFranchiseJEPermission = hasIFJournalEntryPermission(permissions);
  const dealNumber = getDealNumber(deal);
  const createDto = makePostTransactionDTO({
    submitData,
    initialTransactionDto,
    draftData,
    dealNumber,
    idempotentKey: preClose ? preCloseIdempotentKey : idempotentKey,
    isMultiControlEnabled,
    hasInterFranchiseJEPermission,
  });
  parentOnFormSubmit({ submitData, preClose, createDto });
};

// eslint-disable-next-line consistent-return
const handleReset = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { shouldReset } = params;
  let { deal } = params;
  const { getModifiedDeal, getAPIError } = getState();
  setState({ isResetting: true });
  if (_isEmpty(deal)) deal = await getModifiedDeal();

  const { initialFormValues, error, initialTransactionDto, arInvoice } = await getDealPostingsData(deal, getAPIError); // fresh posting lines without ids
  if (error) {
    toaster(TOASTER_TYPE.ERROR, error);
    setState({ isResetting: false });
    return { error };
  }
  setValues({ getState, setState, transaction: initialFormValues, initialTransactionDto, arInvoice });
  if (shouldReset) {
    await handleSaveAsDraft({ getState, setState, params: { isReset: true } });
  }
  setState({ isResetting: false });
};

const handleValidationSuccessWithAutoPostingCheck = ({ getState, params = EMPTY_OBJECT, setState }) => {
  const { permissions, transaction } = getState();
  const transactionType = transactionReader.transactionType(transaction);
  const status = transactionReader.status(transaction);
  const { errors } = params;
  setState({ errors });
  warnIfAutoPostingEditPermissionRevoked({
    postingFieldKey: TRANSACTION_FIELD_ID.POSTINGS,
    permissions,
    formErrors: errors,
    transactionType,
    status,
  });
};

const ACTION_HANDLERS = {
  ...BASE_ACTION_HANDLERS,
  [ACTION_TYPES.ON_INIT]: handleOnInit,
  [ACTION_TYPES.SAVE_AS_DRAFT]: handleSaveAsDraft,
  [ACTION_TYPES.FETCH_TARGET_JOURNAL_FOR_DEALER_IDS]: fetchTargetJournalForDealerIds,
  [FORM_ACTION_TYPES.ON_FORM_SUBMIT]: handleFormSubmit,
  [FORM_ACTION_TYPES.VALIDATION_SUCCESS]: handleValidationSuccessWithAutoPostingCheck,
  [ACTION_TYPES.RESET]: handleReset,
};

export default ACTION_HANDLERS;
