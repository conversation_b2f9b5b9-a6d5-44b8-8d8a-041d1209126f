/* eslint-disable import/order */
// Constants
import FEATURE_FLAGS from 'tbusiness/appServices/accounting/constants/featureFlags';
import { TRANSACTION_FIELD_ID } from 'twidgets/appServices/accounting/organisms/configurableTransaction';
import {
  REFERENCE_TYPES_FOR_DEAL_TRANSACTION_POSTING,
  FORM_VIEW_CONFIG_NAME,
} from '../constants/dealsJournalEntry.general';
import { CONFIGURABLE_IF_JE_ASSET_TYPES } from 'constants/journalEntry/journalEntry.general';
import DEALS_IF_POSTING_TABLE_METADATA_US from '../constants/dealsJournalEntry.usIFTableMetadata';

// Helpers
import { makeEditIFJEFields } from 'twidgets/appServices/accounting/organisms/interFranchiseJournalEntry/organisms/configurableEditIFJE';
import { getDealerOptions } from 'tbusiness/appServices/accounting/helpers/dealer';
import { hasIFJournalEntryPermission } from 'tbusiness/appServices/accounting/helpers/permissions';
import { getShouldShowFranchise } from 'helpers/journalEntry/journalEntry.interfranchise';

// Utils
import getReferenceTypeOptions from 'tbase/utils/accounting/getReferenceTypeOptions';

// Readers
import dealerInfoReader from 'tbase/readers/DealerInfo';

const makeFields = (
  entityMetadata,
  transaction,
  dealerInfo,
  journalsMapByDealerId,
  interFranchiseTransactionEnabledTenantDealersMap,
  setupFieldsByDealerId,
  accountsMapByDealerId,
  accountingSettingsByDealerId,
  activeMonthByDealerId,
  activeYearByDealerId,
  postingAllowedForUserByDealerId,
  bootstrapByDealerId,
  targetIFJournalByDealerId,
  fetchTargetJournalForDealerIds,
  featureFlags,
  values,
  programFieldMap,
  isConfigurableBootstrapLoadingStatusByDealerId,
  dealerInfoByDealerId,
  isEditAutoPostedDealTransactionEnabled,
  viewOnly,
  onAction,
  distributionKeysMapByDealerId,
  excludedAccountIdsByDealerId,
  salesChainListByDealerId,
  lookupAssetSupportMap,
  permissions
) => {
  const entityConfig = entityMetadata.getEntityConfig();
  const formViewConfig = entityMetadata.getFormViewConfig(FORM_VIEW_CONFIG_NAME);

  const fields = makeEditIFJEFields({
    entityConfig,
    formViewConfig,
    transaction,
    dealerInfo,
    journalsMapByDealerId,
    interFranchiseTransactionEnabledTenantDealersMap,
    setupFieldsByDealerId,
    accountsMapByDealerId,
    accountingSettingsByDealerId,
    activeMonthByDealerId,
    activeYearByDealerId,
    postingAllowedForUserByDealerId,
    bootstrapByDealerId,
    targetIFJournalByDealerId,
    fetchTargetJournalForDealerIds,
    featureFlags,
    values,
    shouldFilterIFJournals: false,
    programFieldMap,
    isConfigurableBootstrapLoadingStatusByDealerId,
    sourceDealerOverride: dealerInfoReader.id(dealerInfo),
    onAction,
    assetTypes: CONFIGURABLE_IF_JE_ASSET_TYPES,
    distributionKeysMapByDealerId,
    excludedAccountIdsByDealerId,
    lookupAssetSupportMap,
    shouldHideReferenceRedirectionIcon: true,
  });

  const isExternalDealerIdEnabled = featureFlags.getFlag(FEATURE_FLAGS.IS_EXTERNAL_DEALER_ID_ENABLED);
  const customBusinessUnitOptions = getDealerOptions(dealerInfoByDealerId);
  const hasInterFranchiseJournalEntryPermission = hasIFJournalEntryPermission(permissions);
  const sourceDealerId = values?.[TRANSACTION_FIELD_ID.FRANCHISE];
  const shouldShowFranchise =
    hasInterFranchiseJournalEntryPermission &&
    getShouldShowFranchise(interFranchiseTransactionEnabledTenantDealersMap, sourceDealerId);
  const tableMetadata = shouldShowFranchise ? DEALS_IF_POSTING_TABLE_METADATA_US : undefined;

  return {
    ...fields,
    [TRANSACTION_FIELD_ID.JOURNAL_ID]: fields[TRANSACTION_FIELD_ID.JOURNAL_ID].addToRenderOptions({
      disabled: isEditAutoPostedDealTransactionEnabled ? viewOnly : true,
    }),
    [TRANSACTION_FIELD_ID.FRANCHISE]: fields[TRANSACTION_FIELD_ID.FRANCHISE].addToRenderOptions({
      customBusinessUnitOptions,
      showBusinessUnitCode: isExternalDealerIdEnabled,
      isDisabled: true,
    }),
    [TRANSACTION_FIELD_ID.DESCRIPTION]: fields[TRANSACTION_FIELD_ID.DESCRIPTION].addToRenderOptions({
      disabled: isEditAutoPostedDealTransactionEnabled ? viewOnly : true,
    }),
    [TRANSACTION_FIELD_ID.REFERENCE_TYPE]: fields[TRANSACTION_FIELD_ID.REFERENCE_TYPE].addToRenderOptions({
      options: getReferenceTypeOptions(REFERENCE_TYPES_FOR_DEAL_TRANSACTION_POSTING),
      disabled: isEditAutoPostedDealTransactionEnabled ? viewOnly : true,
    }),
    [TRANSACTION_FIELD_ID.REFERENCE_TEXT]: fields[TRANSACTION_FIELD_ID.REFERENCE_TEXT].addToRenderOptions({
      disabled: isEditAutoPostedDealTransactionEnabled ? viewOnly : true,
    }),
    [TRANSACTION_FIELD_ID.REFERENCE_ID]: fields[TRANSACTION_FIELD_ID.REFERENCE_ID].addToRenderOptions({
      isDisabled: isEditAutoPostedDealTransactionEnabled ? viewOnly : true,
      disabled: isEditAutoPostedDealTransactionEnabled ? viewOnly : true,
    }),
    [TRANSACTION_FIELD_ID.ACCOUNTING_DATE]: fields[TRANSACTION_FIELD_ID.ACCOUNTING_DATE].addToRenderOptions({
      disabled: isEditAutoPostedDealTransactionEnabled ? viewOnly : true,
    }),
    [TRANSACTION_FIELD_ID.RECURRING_SCHEDULE]: fields[TRANSACTION_FIELD_ID.RECURRING_SCHEDULE].addToRenderOptions({
      disabled: true,
    }),
    [TRANSACTION_FIELD_ID.POSTINGS]: fields[TRANSACTION_FIELD_ID.POSTINGS].addToRenderOptions({
      disableOtherFranchisePostings: false,
      shouldAddCurrentDealerPosting: true,
      dealerInfoByDealerId,
      salesChainListByDealerId,
      tableMetadata,
      isInterFranchise: shouldShowFranchise,
    }),
  };
};

export default makeFields;
