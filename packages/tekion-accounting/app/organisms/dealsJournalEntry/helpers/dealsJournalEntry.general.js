// Lodash
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';

// Utils
import getIsMultiControlEnabled from 'tbusiness/appServices/accounting/utils/getIsMultiControlEnabled';

export const isEditableDeal = (isDealAcquired, hasTransactionPostingEdit = _noop) =>
  hasTransactionPostingEdit() && isDealAcquired;

export const isViewMode = (isDealAcquired, isViewOnly, hasTransactionPostingEdit) =>
  !isEditableDeal(isDealAcquired, hasTransactionPostingEdit) || isViewOnly;

export const getMetadata = (initialTransactionDto, draftData) => draftData?.metadata || initialTransactionDto?.metadata;

export const getRequestDTOMetadata = ({ initialTransactionDto, draftData, businessFlow }) => {
  const defaultMetadata = {
    businessFlow,
  };
  const initialMetadata = getMetadata(initialTransactionDto, draftData);
  const metadata = initialMetadata && !_isEmpty(initialMetadata) ? initialMetadata : defaultMetadata;
  return metadata;
};

export const getMultiControlProps = props => {
  const { getDealerPropertyValue } = props;
  const isMultiControlEnabled = getIsMultiControlEnabled(getDealerPropertyValue);
  return { isMultiControlEnabled };
};
