/* eslint-disable import/order */
// Helpers
import {
  getPostingsWithoutCount,
  getPostingsDTOWithoutRefType,
  JOURNAL_ENTRY_FIELD_IDS,
} from 'twidgets/appServices/accounting/organisms/transactionPostingForm';
import { createIFPostings } from 'twidgets/appServices/accounting/organisms/interFranchiseJournalEntry';
import { getTransactionType } from 'tbusiness/appServices/accounting/helpers/interFranchise';
import { getMetadata } from './dealsJournalEntry.general';

// Utils
import getArraySafeValue from 'tbase/utils/getArraySafeValue';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { TRANSACTION_FIELD_ID } from 'twidgets/appServices/accounting/organisms/configurableTransaction';

const getResolvedValue = (values, key) => {
  const value = values?.[key];
  return value ? getArraySafeValue(value) : null;
};

const getInterFranchiseTransactionType = hasInterFranchiseJEPermission =>
  getTransactionType(hasInterFranchiseJEPermission);

const getPostingsDto = (submitData, isMultiControlEnabled) => {
  const postings = submitData?.[JOURNAL_ENTRY_FIELD_IDS.POSTINGS]; // TODO: need to updated as per v2 fieldid
  if (isMultiControlEnabled) {
    return getPostingsDTOWithoutRefType(postings);
  }
  return postings;
};

export const makePostTransactionDTO = ({
  submitData,
  initialTransactionDto,
  draftData,
  dealNumber,
  idempotentKey,
  isMultiControlEnabled,
  hasInterFranchiseJEPermission,
}) => {
  const transactionType = getInterFranchiseTransactionType(hasInterFranchiseJEPermission);
  const postings = getPostingsDto(submitData, isMultiControlEnabled);
  return {
    createDto: {
      ...submitData,
      transactionType,
      dealNumber,
      metadata: getMetadata(initialTransactionDto, draftData),
      idempotentKey,
      [JOURNAL_ENTRY_FIELD_IDS.POSTINGS]: postings,
    },
  };
};

export const getDraftPayloads = ({
  values = EMPTY_OBJECT,
  initialTransactionDto,
  draftData,
  accountsMapByDealerId,
  journalsMapByDealerId,
  dealerInfo,
  targetIFJournalByDealerId,
  hasInterFranchiseJEPermission,
}) => {
  const journalId = getResolvedValue(values, TRANSACTION_FIELD_ID.JOURNAL_ID);
  const transactionType = getInterFranchiseTransactionType(hasInterFranchiseJEPermission);

  const commonPayload = {
    ...values,
    postings: values[TRANSACTION_FIELD_ID.POSTINGS],
    [TRANSACTION_FIELD_ID.REFERENCE_ID]: getArraySafeValue(values[TRANSACTION_FIELD_ID.REFERENCE_ID]),
    scheduledTime: values.scheduledTime && +values.scheduledTime,
    transactionType,
    refType: getResolvedValue(values, TRANSACTION_FIELD_ID.REFERENCE_TYPE),
    journalId,
    documentTypeId: getResolvedValue(values, TRANSACTION_FIELD_ID.DOCUMENT_TYPE),
  };

  const postingsWithoutCount = getPostingsWithoutCount(values[TRANSACTION_FIELD_ID.POSTINGS]);
  const glPostingsEntities = createIFPostings(
    postingsWithoutCount,
    accountsMapByDealerId,
    journalsMapByDealerId,
    dealerInfo,
    journalId,
    targetIFJournalByDealerId
  );

  return {
    ...commonPayload,
    postings: glPostingsEntities,
    metadata: getMetadata(initialTransactionDto, draftData),
  };
};
