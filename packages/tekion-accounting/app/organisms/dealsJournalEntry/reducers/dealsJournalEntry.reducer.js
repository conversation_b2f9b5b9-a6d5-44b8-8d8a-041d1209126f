/* eslint-disable import/order */
/* eslint no-param-reassign: 0 */

import produce from 'immer';
import { handleActions } from 'redux-actions';

// Lodash
import _set from 'lodash/set';

// Readers
import bootstrapDataReader from 'tbusiness/appServices/accounting/readers/Bootstrap';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { ACTIONS } from '../constants/dealsJournalEntry.actionTypes';

// Handlers
import CONFIGURABLE_BOOTSTRAP_ACTION_HANDLERS from 'reducers/configurableBootstrap.reducer';

const INITIAL_STATE = {
  isFetching: false,
  bootstrapByDealerId: EMPTY_OBJECT,
  isBootstrapByDealerIdLoading: EMPTY_OBJECT,
  metaData: {
    allAccounts: EMPTY_ARRAY,
    allJournals: EMPTY_ARRAY,
    expenseAllocationKeys: EMPTY_ARRAY,
    activeMonth: undefined,
    activeYear: undefined,
    fieldsMapToId: EMPTY_OBJECT,
    apSetup: EMPTY_OBJECT,
    arSetup: EMPTY_OBJECT,
    accountingSettings: EMPTY_OBJECT,
    userAccess: EMPTY_OBJECT,
    programFieldMap: EMPTY_OBJECT,
    additionalProgramFieldMap: EMPTY_OBJECT,
  },
};

const setLoadingState = produce(state => {
  state.isFetching = true;
});

const addBootstrapToStore = produce((state, action = EMPTY_OBJECT) => {
  const { payload = EMPTY_OBJECT } = action;
  const {
    bootstrapData = EMPTY_OBJECT,
    fieldsMapToId = EMPTY_OBJECT,
    additionalProgramFieldMap = EMPTY_OBJECT,
  } = payload;
  _set(state, 'metaData.allAccounts', bootstrapDataReader.allAccounts(bootstrapData));
  _set(state, 'metaData.allJournals', bootstrapDataReader.allJournals(bootstrapData));
  _set(state, 'metaData.expenseAllocationKeys', bootstrapDataReader.expenseAllocationKeys(bootstrapData));
  _set(state, 'metaData.activeMonth', bootstrapDataReader.activeMonth(bootstrapData));
  _set(state, 'metaData.activeYear', bootstrapDataReader.activeYear(bootstrapData));
  _set(state, 'metaData.fieldsMapToId', fieldsMapToId);
  _set(state, 'metaData.apSetup', bootstrapDataReader.apSetup(bootstrapData));
  _set(state, 'metaData.arSetup', bootstrapDataReader.arSetup(bootstrapData));
  _set(state, 'metaData.accountingSettings', bootstrapDataReader.accountingSettings(bootstrapData));
  _set(state, 'metaData.userAccess', bootstrapDataReader.userAccess(bootstrapData));
  _set(state, 'metaData.priorPeriodDateRange', bootstrapDataReader.priorPeriodDateRange(bootstrapData));
  _set(state, 'metaData.programFieldMap', bootstrapDataReader.programFieldMap(bootstrapData));
  _set(state, 'metaData.lookupAssetSupport', bootstrapDataReader.lookupAssetSupportMap(bootstrapData));
  _set(state, 'metaData.additionalProgramFieldMap', additionalProgramFieldMap);
  state.isFetching = false;
});

const setLoadedState = produce(state => {
  state.isFetching = false;
});

const ACTION_HANDLERS = {
  [ACTIONS.FETCH_ACCOUNTING_BOOTSTRAP_DATA]: setLoadingState,
  [ACTIONS.FETCH_ACCOUNTING_BOOTSTRAP_DATA_SUCCESS]: addBootstrapToStore,
  [ACTIONS.FETCH_ACCOUNTING_BOOTSTRAP_DATA_FAILURE]: setLoadedState,
};

export default handleActions({ ...ACTION_HANDLERS, ...CONFIGURABLE_BOOTSTRAP_ACTION_HANDLERS }, INITIAL_STATE);
