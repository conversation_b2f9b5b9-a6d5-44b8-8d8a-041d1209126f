import { combineReducers } from 'redux';

// Reducers
import { multiControlInputDrawerReducer } from 'twidgets/appServices/accounting/organisms/transactionPostingForm';
import dealsJournalEntryReducer from './dealsJournalEntry.reducer';

const accountingReportsMetadataReducer = combineReducers({
  bootstrap: dealsJournalEntryReducer,
  multiControlInputDrawer: multiControlInputDrawerReducer,
});

export default accountingReportsMetadataReducer;
