/* eslint-disable import/order */
// Constants
import { DEALS_JOURNAL_ENTRY_REDUCER } from '../constants/dealsJournalEntry.general';

// Reducers
import accountingReportsMetadataReducers from './dealsJournalEntryMetadata.reducer';
import appCacheReducer from 'reducers/appCache';
import configurableEntityReducer, { CONFIGURABLE_ENTITY_STORE_KEY } from 'tbusiness/reducers/configurableEntity';

const accountingReportsReducer = {
  appCache: appCacheReducer,
  [DEALS_JOURNAL_ENTRY_REDUCER]: accountingReportsMetadataReducers,
  [CONFIGURABLE_ENTITY_STORE_KEY]: configurableEntityReducer,
};

export default accountingReportsReducer;
