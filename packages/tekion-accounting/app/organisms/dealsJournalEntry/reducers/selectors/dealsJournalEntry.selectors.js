import { compose } from 'recompose';
import { createSelector } from 'reselect';

// Lodash
import _property from 'lodash/property';
import _keyBy from 'lodash/keyBy';
import _castArray from 'lodash/castArray';
import _sortBy from 'lodash/sortBy';
import _negate from 'lodash/negate';
import _head from 'lodash/head';
import _reduce from 'lodash/reduce';
import _nthArg from 'lodash/nthArg';

// Readers
import journalReader from 'tbase/readers/Journal';
import accountReader from 'tbase/readers/Account';
import distributionAllocationReader from 'tbase/readers/ExpenseAllocation';
import bootstrapReader from 'tbusiness/appServices/accounting/readers/Bootstrap';
import dealerInfoReader from 'tbase/readers/DealerInfo';
import setupFieldReader from 'tbase/readers/SetupField';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import { RESOURCE_TYPE } from 'tbase/bulkResolvers/constants/resourceType';
import SETUP_FIELDS_KEYS from 'tbase/constants/accounting/setupFieldKeys';
import BOOTSTRAP_ASSET_KEY from 'tbusiness/appServices/accounting/constants/configurableBootstrap.bootstrapAssetKeys';

// Utils
import { makeSetupFieldsLookup } from 'tbase/utils/accounting/setupFields';

// Helpers
import { filterActiveJournals } from 'tbase/helpers/accounting/journals';
import {
  createAccountsMap,
  createDistributionKeysMap,
  getActiveAccounts,
  getEnabledAccounts,
  getNonMemoAccounts,
} from 'tbusiness/appServices/accounting/helpers/accounts';
import { getNonIFGroupDistributionKeys } from 'tbusiness/helpers/distributionAccounts';
import { createActiveJournalsMap } from 'tbusiness/appServices/accounting/helpers/journals';

// Selectors
import { createInfoMapByDealerId } from 'reducers/selectors/selectors.helper';
import { checkIfMultiControlInputDrawerIsVisible as checkIfMultiControlInputDrawerIsVisibleSelector } from 'twidgets/appServices/accounting/organisms/transactionPostingForm';

const LOOKUP_DATA_SETUP_FIELDS = [
  SETUP_FIELDS_KEYS.DEPARTMENT,
  SETUP_FIELDS_KEYS.ACCOUNT_SUBTYPE,
  SETUP_FIELDS_KEYS.FINANCIAL_STATEMENT_GROUP,
  SETUP_FIELDS_KEYS.FINANCIAL_STATEMENT_SUB_GROUP,
];

const SORT_KEYS = {
  ACCOUNT: ['accountNumber'],
  JOURNAL: ['journalNumber'],
  DISTRIBUTION_KEYS: [distributionAllocationReader.distributionKey],
};

export const getDealsJournalEntryState = _property('dealsJournalEntry');
const getBootstrapState = _property('bootstrap');
export const getBootstrapByDealerIdMap = compose(
  _property('bootstrapByDealerId'),
  getBootstrapState,
  getDealsJournalEntryState
);
const getBootstrapMetadata = _property('metaData');
const getBootstrapData = compose(_property('metaData'), getBootstrapState, getDealsJournalEntryState);

export const getBootstrapByDealerIdLoadingData = compose(
  _property('isBootstrapByDealerIdLoading'),
  getBootstrapState,
  getDealsJournalEntryState
);

const getDealerInfo = compose(_property('dealerInfo'), _nthArg(1));

const sortByKeys = keys => entity => {
  const keysArray = _castArray(keys);
  const sortedEntity = _sortBy(entity, keysArray);
  return sortedEntity;
};

export const getIsBootstrapLoading = compose(_property('isFetching'), getBootstrapState, getDealsJournalEntryState);

export const getAllUnsortedAccounts = compose(
  _property('allAccounts'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

export const getAllAccounts = createSelector([getAllUnsortedAccounts], sortByKeys(SORT_KEYS.ACCOUNT));

const getAllJournals = compose(
  _property('allJournals'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

const getUnsortedJournals = compose(filterActiveJournals, getAllJournals);

export const getJournals = createSelector([getUnsortedJournals], sortByKeys(SORT_KEYS.JOURNAL));

export const getActiveMonth = compose(
  _property('activeMonth'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

export const getCurrentActiveMonth = createSelector([getActiveMonth], activeMonth => _head(activeMonth) - 1);

export const getActiveYear = compose(
  Number,
  _head,
  _property('activeYear'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

export const getPriorPeriodDateRange = compose(
  _property('priorPeriodDateRange'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

export const getSetupFields = compose(
  _property('fieldsMapToId'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

const createAllAccountsMap = accounts => _keyBy(accounts, accountReader.id);

const createJournalsMap = journals => _keyBy(journals, journalReader.id);

export const getAllAccountsMap = createSelector([getAllAccounts], createAllAccountsMap);

export const getJournalsMap = createSelector([getJournals], createJournalsMap);

export const getAllJournalsMap = createSelector([getAllJournals], createJournalsMap);

const createLocalLookup = (journalsMap, accountsMap, fields) => {
  const setupFieldsLookup = makeSetupFieldsLookup(LOOKUP_DATA_SETUP_FIELDS, fields);
  return {
    [RESOURCE_TYPE.JOURNAL_ASSET]: journalsMap,
    [RESOURCE_TYPE.GL_ACCOUNT_ASSET]: accountsMap,
    ...setupFieldsLookup,
  };
};

export const getLocalLookup = createSelector([getAllJournalsMap, getAllAccountsMap, getSetupFields], createLocalLookup);

export const isPostingAllowedForUser = compose(
  _negate(_property('activeMonthPostingRestricted')),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

export const getApSetup = compose(
  _property('apSetup'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

const getUnsortedDistributionKeys = compose(
  getNonIFGroupDistributionKeys,
  _property('expenseAllocationKeys'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

export const getDistributionKeys = createSelector(
  [getUnsortedDistributionKeys],
  sortByKeys(SORT_KEYS.DISTRIBUTION_KEYS)
);

export const getDistributionKeysMap = createSelector([getDistributionKeys], createDistributionKeysMap);

const makeDistributionKeysMapByDealerId = (bootstrapByDealerId, dealerInfo, bootstrapData) => {
  const distributionKeysMapByDealerId = _reduce(
    bootstrapByDealerId,
    createInfoMapByDealerId('expenseAllocationKeys', createDistributionKeysMap),
    {}
  );
  const dealerId = dealerInfoReader.id(dealerInfo);
  return createInfoMapByDealerId(
    'expenseAllocationKeys',
    createDistributionKeysMap,
    distributionKeysMapByDealerId,
    bootstrapData,
    dealerId
  );
};

export const getDistributionKeysMapByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getBootstrapData],
  makeDistributionKeysMapByDealerId
);

export const getArSetup = compose(
  _property('arSetup'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

export const getAccountingSettings = compose(
  _property('accountingSettings'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

export const getUserAccessSettings = compose(
  _property('userAccess'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

export const checkIfMultiControlInputDrawerIsVisible = compose(
  checkIfMultiControlInputDrawerIsVisibleSelector,
  getDealsJournalEntryState
);

export const getProgramFieldMap = compose(
  _property('programFieldMap'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

export const getAdditionalProgramFieldMap = compose(
  _property('additionalProgramFieldMap'),
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

const getActiveNonMemoAccountsFromAllAccounts = compose(getNonMemoAccounts, getActiveAccounts, getEnabledAccounts);

const getAccounts = createSelector([getAllAccounts], getActiveNonMemoAccountsFromAllAccounts);

export const getAccountsMap = createSelector([getAccounts], createAccountsMap);

const createActiveNonMemoAccountsMap = compose(createAccountsMap, getActiveNonMemoAccountsFromAllAccounts);

const makeAccountsMapByDealerId = (bootstrapByDealerId, parentDealerInfo, parentAccountsMap) => {
  const accountsMapByDealerId = _reduce(
    bootstrapByDealerId,
    createInfoMapByDealerId('allAccounts', createActiveNonMemoAccountsMap),
    {}
  );
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  const updatedAccountsMapByDealerId = {
    ...accountsMapByDealerId,
    [parentDealerId]: parentAccountsMap,
  };
  return updatedAccountsMapByDealerId;
};

export const getAccountsMapByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getAccountsMap],
  makeAccountsMapByDealerId
);

const createAccountingSettingsByDealerId = (accountingSettingsByDealerId, bootstrapForDealer, dealerId) => {
  const accountingSettings = bootstrapReader.accountingSettings(bootstrapForDealer);
  accountingSettingsByDealerId[dealerId] = accountingSettings; // eslint-disable-line  no-param-reassign
  return accountingSettingsByDealerId;
};

const makeAccountingSettingsByDealerId = (bootstrapByDealerId, parentDealerInfo, parentBootstrapData) => {
  const accountingSettingsMap = _reduce(bootstrapByDealerId, createAccountingSettingsByDealerId, {});
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  return createAccountingSettingsByDealerId(accountingSettingsMap, parentBootstrapData, parentDealerId);
};

export const getAccountingSettingsByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getBootstrapData],
  makeAccountingSettingsByDealerId
);

const createApSetupByDealerId = (apSetupByDealerId, bootstrapForDealer, dealerId) => {
  const apSetup = bootstrapReader.apSetup(bootstrapForDealer);
  apSetupByDealerId[dealerId] = apSetup; // eslint-disable-line  no-param-reassign
  return apSetupByDealerId;
};

const makeApSetupByDealerId = (bootstrapByDealerId, parentDealerInfo, parentBootstrapData) => {
  const apSetupMap = _reduce(bootstrapByDealerId, createApSetupByDealerId, {});
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  return createApSetupByDealerId(apSetupMap, parentBootstrapData, parentDealerId);
};

export const getApSetupByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getBootstrapData],
  makeApSetupByDealerId
);

const createArSetupByDealerId = (arSetupByDealerId, bootstrapForDealer, dealerId) => {
  const arSetup = bootstrapReader.arSetup(bootstrapForDealer);
  arSetupByDealerId[dealerId] = arSetup; // eslint-disable-line  no-param-reassign
  return arSetupByDealerId;
};

const makeArSetupByDealerId = (bootstrapByDealerId, parentDealerInfo, parentBootstrapData) => {
  const arSetupMap = _reduce(bootstrapByDealerId, createArSetupByDealerId, {});
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  return createArSetupByDealerId(arSetupMap, parentBootstrapData, parentDealerId);
};

export const getArSetupByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getBootstrapData],
  makeArSetupByDealerId
);

const createIsPostingAllowedForUserByDealerId = (isPostingAllowedForUserByDealerId, bootstrapForDealer, dealerId) => {
  const isActiveMonthPostingRestricted = bootstrapReader.activeMonthPostingRestricted(bootstrapForDealer);
  return {
    ...isPostingAllowedForUserByDealerId,
    [dealerId]: !isActiveMonthPostingRestricted,
  };
};

const makeIsPostingAllowedForUserByDealerId = (bootstrapByDealerId, parentDealerInfo, parentBootstrapData) => {
  const isPostingAllowedForUserMap = _reduce(bootstrapByDealerId, createIsPostingAllowedForUserByDealerId, {});
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  return createIsPostingAllowedForUserByDealerId(isPostingAllowedForUserMap, parentBootstrapData, parentDealerId);
};

export const isPostingAllowedForUserByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getBootstrapData],
  makeIsPostingAllowedForUserByDealerId
);

const getActiveYearFromBootstrapForDealer = compose(_head, bootstrapReader.activeYear);

const createActiveYearByDealerId = (activeYearByDealerId, bootstrapForDealer, dealerId) => {
  const activeYear = getActiveYearFromBootstrapForDealer(bootstrapForDealer);
  return {
    ...activeYearByDealerId,
    [dealerId]: activeYear,
  };
};

const makeActiveYearByDealerId = (bootstrapByDealerId, parentDealerInfo, parentBootstrapData) => {
  const activeYearByDealerId = _reduce(bootstrapByDealerId, createActiveYearByDealerId, {});
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  return createActiveYearByDealerId(activeYearByDealerId, parentBootstrapData, parentDealerId);
};

export const getActiveYearByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getBootstrapData],
  makeActiveYearByDealerId
);

const getActiveMonthFromBootstrapForDealer = compose(_head, bootstrapReader.activeMonth);

const createCurrentActiveMonthByDealerId = (activeMonthByDealerId, bootstrapForDealer, dealerId) => {
  const activeMonth = getActiveMonthFromBootstrapForDealer(bootstrapForDealer);
  return {
    ...activeMonthByDealerId,
    [dealerId]: activeMonth - 1,
  };
};

const makeCurrentActiveMonthByDealerId = (bootstrapByDealerId, parentDealerInfo, parentBootstrapData) => {
  const activeMonthByDealerId = _reduce(bootstrapByDealerId, createCurrentActiveMonthByDealerId, {});
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  return createCurrentActiveMonthByDealerId(activeMonthByDealerId, parentBootstrapData, parentDealerId);
};

export const getCurrentActiveMonthByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getBootstrapData],
  makeCurrentActiveMonthByDealerId
);

const createCustomFieldsByDealerId = (customFieldsByDealerId, bootstrapForDealer, dealerId) => {
  const customField = bootstrapForDealer?.[BOOTSTRAP_ASSET_KEY.CUSTOM_FIELD] || EMPTY_ARRAY;
  customFieldsByDealerId[dealerId] = _keyBy(customField, setupFieldReader.id); // eslint-disable-line  no-param-reassign
  return customFieldsByDealerId;
};

const makeCustomFieldsByDealerId = (bootstrapByDealerId, parentDealerInfo, customFields) => {
  const customFieldsMap = _reduce(bootstrapByDealerId, createCustomFieldsByDealerId, {});
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  customFieldsMap[parentDealerId] = customFields;
  return customFieldsMap;
};

export const getSetupFieldsByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getSetupFields],
  makeCustomFieldsByDealerId
);

const makeJournalsMapByDealerId = (bootstrapByDealerId, parentDealerInfo, parentBootstrapData) => {
  const journalsMapByDealerId = _reduce(
    bootstrapByDealerId,
    createInfoMapByDealerId('allJournals', createActiveJournalsMap),
    {}
  );
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  return createInfoMapByDealerId(
    'allJournals',
    createActiveJournalsMap,
    journalsMapByDealerId,
    parentBootstrapData,
    parentDealerId
  );
};

export const getJournalsMapByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getBootstrapData],
  makeJournalsMapByDealerId
);

const makeAllAccountsMapByDealerId = (bootstrapByDealerId, parentDealerInfo, parentAllAccountsMap) => {
  const accountsMapByDealerId = _reduce(
    bootstrapByDealerId,
    createInfoMapByDealerId('allAccounts', createAccountsMap),
    {}
  );
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  const updatedAccountsMapByDealerId = {
    ...accountsMapByDealerId,
    [parentDealerId]: parentAllAccountsMap,
  };
  return updatedAccountsMapByDealerId;
};

export const getAllAccountsMapByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getAllAccountsMap],
  makeAllAccountsMapByDealerId
);

export const getLookupAssetSupportMap = compose(
  bootstrapReader.lookupAssetSupportMap,
  getBootstrapMetadata,
  getBootstrapState,
  getDealsJournalEntryState
);

const createSalesChainByDealerId = (salesChainByDealerId, bootstrapForDealer, dealerId) => {
  const salesChains = bootstrapForDealer?.[BOOTSTRAP_ASSET_KEY.SALES_CHAIN] || EMPTY_ARRAY;
  salesChainByDealerId[dealerId] = salesChains; // eslint-disable-line  no-param-reassign
  return salesChainByDealerId;
};

const makeSalesChainListByDealerId = (bootstrapByDealerId, parentDealerInfo, parentSaleChainList) => {
  const salesChainByDealerId = _reduce(bootstrapByDealerId, createSalesChainByDealerId, {});
  const parentDealerId = dealerInfoReader.id(parentDealerInfo);
  salesChainByDealerId[parentDealerId] = parentSaleChainList;
  return salesChainByDealerId;
};

const getParentSalesChainList = compose(_property('parentSalesChainList'), _nthArg(1));

export const getSalesChainListByDealerId = createSelector(
  [getBootstrapByDealerIdMap, getDealerInfo, getParentSalesChainList],
  makeSalesChainListByDealerId
);
