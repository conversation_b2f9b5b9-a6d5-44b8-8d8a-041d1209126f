import Http from 'tbase/services/apiService/httpClient';

// Constants
import { URL_TYPES } from 'tbase/constants/api';

export const fetchBootstrapData = () => Http.get(URL_TYPES.CDMS, '/accounting/u/bootstrap');

export const fetchCustomFieldSettings = () => Http.get(URL_TYPES.CDMS, 'settings/u/customField/accounts');

export const fetchAdditionalProgramFieldMap = () =>
  Http.get(URL_TYPES.CDMS, 'accounting-module/u/program-configs/configurable-options');

export const getInitialFormValues = payload => Http.post(URL_TYPES.CDMS, 'accounting/u/posting/create/deal', payload);
