// Lodash
import _map from 'lodash/map';
import _get from 'lodash/get';

// Utils
import getDataFromResponse from 'utils/getDataFromResponse';
import { formatDecimalByNumber } from 'tbase/formatters/number';

// Readers
import arAgingResponseReader from 'readers/arAging/ArAgingResponse';
import arAgingReportReader from 'readers/ArAgingReport';

// constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import MAPPER from '../constants/arAgingReport.mapper';

const getPercent = share => formatDecimalByNumber(share * 100, 1);

const getSummaryData = ({
  agingReportBalanceSummary = EMPTY_OBJECT,
  agingReportInvoiceCountSummary = EMPTY_OBJECT,
}) => {
  const summary = _map(MAPPER, (value, key) => ({
    key,
    amount: _get(agingReportBalanceSummary, `${value}balance`, 0),
    count: _get(agingReportInvoiceCountSummary, `${value}count`, 0),
    percentage: getPercent(_get(agingReportBalanceSummary, `${value}share`, 0)),
  }));
  return {
    outstandingBalance: _get(agingReportBalanceSummary, 'outstandingAmount', 0),
    summary,
    totalInvoiceCount: _get(agingReportInvoiceCountSummary, 'invoiceCount', 0),
  };
};

export const getARData = response => {
  const data = getDataFromResponse(response) || EMPTY_OBJECT;
  const arAgingReportItems = arAgingResponseReader.arAgingReportItems(data);
  const agingReportBalanceSummary = arAgingResponseReader.agingReportBalanceSummary(data);
  const agingReportInvoiceCountSummary = arAgingResponseReader.agingReportInvoiceCountSummary(data);
  const arAgingReportList = _map(arAgingReportItems, item => ({ ...item, id: arAgingReportReader.customerId(item) }));
  const arSummary = getSummaryData({ agingReportBalanceSummary, agingReportInvoiceCountSummary });
  return { arAgingReportList, arSummary };
};
