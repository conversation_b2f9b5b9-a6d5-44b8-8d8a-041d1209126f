/* eslint-disable rulesdir/no-inline-functions */
/* eslint-disable import/order */
import _curry from 'lodash/curry';
import _map from 'lodash/map';
import _flatMap from 'lodash/flatMap';

import getDataFromResponse from 'utils/getDataFromResponse';
import resolver from 'tbase/bulkResolvers';
import { ENTITIES } from 'tbase/bulkResolvers/entities';

// Readers
import arAgingReportReader from 'readers/ArAgingReport';
import arInvoicesPerCustomerReader from '../readers/ArInvoicesPerCustomer';

// Helpers
import { getEndTimeFromMonthFilter } from '../helpers/exports/arAgingReportExport.general';
import getCustomerReportList from 'helpers/arAging/arAging.getCustomerReportList';
import { getARData } from './arAgingReport.exportActionHelpers';
import getCustomerInvoiceList from '../helpers/arAgingReport.getCustomerInvoiceList';

const handleResolvedAgingReportListSuccess = _curry((arSummary, resolvedArAgingReportList) => ({
  resolvedArAgingReportList,
  arSummary,
}));

const handlePostingDataFetch = arAgingReportResponse => {
  const { arAgingReportList, arSummary } = getARData(arAgingReportResponse);
  return resolver
    .getResolvedData(ENTITIES.AR_AGING_REPORT, arAgingReportList)
    .then(handleResolvedAgingReportListSuccess(arSummary));
};

const handleResolvedInvoiceListFetchSuccess = (arSummary, resolvedArAgingReportList) => resolvedArInvoiceList => ({
  arSummary,
  resolvedArAgingReportList,
  arInvoiceList: resolvedArInvoiceList,
});

export const resolveInvoiceDependencies = ({ invoices, localLookup, arSummary, resolvedArAgingReportList }) =>
  resolver
    .getResolvedData(ENTITIES.CUSTOMER_INVOICE, invoices, {
      localLookup,
    })
    .then(handleResolvedInvoiceListFetchSuccess(arSummary, resolvedArAgingReportList));

const handleInvoiceListFetchSuccess = (localLookup, arSummary, resolvedArAgingReportList) => response => {
  const invoicesByCustomerIdData = getDataFromResponse(response);
  const invoicesByCustomerId = arInvoicesPerCustomerReader.invoicesPerCustomer(invoicesByCustomerIdData);
  const invoices = _flatMap(invoicesByCustomerId);
  return resolveInvoiceDependencies({ localLookup, invoices, arSummary, resolvedArAgingReportList });
};

export const getInvoiceDetailsForAgingReport =
  ({
    selectedFilters,
    localLookup,
    arSetup,
    includeZeroAmountInvoices,
    otherReceivableSetupId,
    accessibleEnterpriseWorkspaceIds,
  }) =>
    ({ arSummary, resolvedArAgingReportList }) =>
      getCustomerInvoiceList({
        arSetup,
        selectedFilters,
        includeZeroAmountInvoices,
        otherReceivableSetupId,
        customerIds: _map(resolvedArAgingReportList, arAgingReportReader.customerId),
        dealerIds: accessibleEnterpriseWorkspaceIds,
      }).then(handleInvoiceListFetchSuccess(localLookup, arSummary, resolvedArAgingReportList));

export const getArAgingReportList = ({ filters, includeZeroAmountInvoices, accessibleEnterpriseWorkspaceIds }) => {
  const endTime = getEndTimeFromMonthFilter(filters);
  return getCustomerReportList({
    selectedFilters: filters,
    endTime,
    includeZeroAmountInvoices,
    dealerIds: accessibleEnterpriseWorkspaceIds,
  }).then(handlePostingDataFetch);
};
