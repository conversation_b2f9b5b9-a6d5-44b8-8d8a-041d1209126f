// lodash
import _reject from 'lodash/reject';

// utils
import Process from 'tbase/utils/Process';
import getEnabledActions from 'tbase/utils/getEnabledActions';

// constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import ACTION_TYPES from 'constants/groupedArInvoiceList.actionTypes';

// helpers
import { getIsCustomerDeletedInRow } from '../helpers/arAgingReport.general';
import { checkIfRestrictedCustomer } from '../helpers/arAgingReport.columns';

const CASH_RECEIPT_ACTIONS = [ACTION_TYPES.CREATE_ADVANCE_CASH_RECEIPT, ACTION_TYPES.CREATE_AR_CASH_RECEIPT];

const isCashReceiptAction = action => CASH_RECEIPT_ACTIONS.includes(action.id);

const handleEnabledActions = (next, params) => {
  const { customerRowActions, permissions, rowData, shouldDisableNotesFlow } = params;
  return getEnabledActions(customerRowActions, { permissions, rowData, shouldDisableNotesFlow });
};

const handleRestrictedCustomer = (next, params) => {
  const { rowData, customerRowActions } = params;
  const isRestrictedCustomer = checkIfRestrictedCustomer(rowData);
  if (isRestrictedCustomer) {
    const filteredRowActions = _reject(customerRowActions, isCashReceiptAction);
    return next({ ...params, customerRowActions: filteredRowActions });
  }
  return next(params);
};

const checkIfCustomerDeleted = (next, params) => {
  const { rowData } = params;
  const isCustomerDeleted = getIsCustomerDeletedInRow(rowData);
  if (isCustomerDeleted) {
    return EMPTY_ARRAY;
  }
  return next(params);
};

const ROW_ACTIONS_PROCESS = new Process()
  .addHandler(checkIfCustomerDeleted)
  .addHandler(handleRestrictedCustomer)
  .addHandler(handleEnabledActions);

const getRowActions = customerRowActions => (permissions, shouldDisableNotesFlow) => rowData =>
  ROW_ACTIONS_PROCESS.run({ permissions, rowData, shouldDisableNotesFlow, customerRowActions });

export default getRowActions;
