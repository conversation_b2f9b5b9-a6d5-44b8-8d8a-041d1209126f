import React from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';
import _identity from 'lodash/identity';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

// Component
import HeaderAmount from 'pages/export/components/headerAmount';

// Utils
import { toMoment } from 'tbase/utils/dateUtils';
import { getFormattedAmountWithPrecisionV2 } from 'utils/moneyUtils';

import style from './rightHeaderPanel.module.scss';

const INVOICE_COUNT_LABEL = __('Invoice Count');

const RightHeaderPanel = props => {
  const { arSummary, selectedMonthEndTime, getFormattedDateAndTime, getFormattedCurrency, getFormattedNumber } = props;
  const formattedDate = getFormattedDateAndTime({
    value: toMoment(),
    formatType: DATE_TIME_FORMAT.ABBREVIATED_BASE,
  });
  const formattedTime = getFormattedDateAndTime({
    value: toMoment(),
    formatType: DATE_TIME_FORMAT.HOUR_MINUTE,
  });
  const formattedEndDate = getFormattedDateAndTime({
    value: selectedMonthEndTime,
    formatType: DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR,
  });
  const formattedTotalInvoiceCount = getFormattedNumber(arSummary.totalInvoiceCount);
  const getFormattedOutstandingBalance = getFormattedAmountWithPrecisionV2(getFormattedCurrency);

  return (
    <div className={style.rightPanel} sectiontype="container-section">
      <div className={`${style.rightHeader} text-right-align`}>
        <HeaderAmount
          label={__('Outstanding AR Balance:')}
          outstandingBalance={getFormattedOutstandingBalance(arSummary.outstandingBalance)}
        />
        <div className={`${style.selectedMonthYear} font-medium`}>{formattedEndDate}</div>
        <div className={style.paymentModeAndDate}>
          <div className={style.paymentMethod}>
            {INVOICE_COUNT_LABEL}: {formattedTotalInvoiceCount}
          </div>
          <div className={style.date}>
            <div>{formattedDate}</div>
            <div className={style.seperator}></div>
            <div>{formattedTime}</div>
          </div>
        </div>
      </div>
    </div>
  );
};
RightHeaderPanel.propTypes = {
  arSummary: PropTypes.object,
  selectedMonthEndTime: PropTypes.number,
  getFormattedDateAndTime: PropTypes.func,
  getFormattedCurrency: PropTypes.func,
  getFormattedNumber: PropTypes.func,
};
RightHeaderPanel.defaultProps = {
  arSummary: EMPTY_OBJECT,
  selectedMonthEndTime: undefined,
  getFormattedDateAndTime: _noop,
  getFormattedCurrency: _noop,
  getFormattedNumber: _identity,
};
export default RightHeaderPanel;
