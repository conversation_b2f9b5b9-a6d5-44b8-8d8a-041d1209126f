@import 'tstyles/component.scss';

.rightPanel {
    @include flex($justify-content: flex-end);
    flex-grow: 1;
}

.rightHeader {
    @include flex($justify-content: space-between);
    flex-direction: column;
}
.paymentModeAndDate {
    margin-top: 1.2rem;
}
.date {
    @include flex($justify-content: flex-end);
    font-size: $font-size-small;
    line-height: $line-height-xxnormal;
}
.paymentMethod {
    font-size: $font-size-small;
    line-height: $line-height-small;
}
.dealerInfo {
    @include flex();
    flex-direction: column;
    font-size: $font-size-small;
}
.dealerContact {
    line-height: $line-height-xxnormal;
}
.seperator {
    width: 0.1rem;
    margin: 0.2rem 0.6rem;
    height: 1rem;
    background: $stormGray;
}
.selectedMonthYear {
    font-size: $font-size-small;
    line-height: $line-height-xsmall;
}
