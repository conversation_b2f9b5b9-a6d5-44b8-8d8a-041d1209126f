// Constants
import PRINT_MODES from 'tbase/constants/printModes';
import PRINT_VIEWS from 'tbase/constants/pdfViews';
import COLUMN_IDS from '../arAgingReport.listColumnIds';

export const AR_AGING_REPORT_SUMMARY_PDF_ASSET_TYPE = 'AR_AGING_REPORT_SUMMARY_PDF_ASSET_TYPE';
export const AR_AGING_REPORT_DETAIL_PDF_ASSET_TYPE = 'AR_AGING_REPORT_DETAIL_PDF_ASSET_TYPE';
export const AR_AGING_REPORT_PDF_ASSET_ID = 'AR_AGING_REPORT_PDF_ASSET_ID';

export const AR_AGING_DETAIL_PDF_PIVOT = [COLUMN_IDS.CUSTOMER_NUMBER_COLUMN];

export const AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID = 'arAgingReportSummaryColumns';
export const AR_AGING_REPORT_DETAIL_COLUMN_CONFIGURATION_ID = 'arAgingReportDetailColumns';

export const DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION = {
  printMode: PRINT_MODES.PORTRAIT,
  printView: PRINT_VIEWS.COMPACT,
  wordWrap: false,
};

export const AR_AGING_SUMMARY_PDF_PREVIEW_TITLE = __('AR Aging Report Preview');
export const AR_AGING_SUMMARY_PDF_TITLE = __('AR Aging Report');
export const AR_AGING_SUMMARY_SECTION_CONFIG_TITLE = __('Summary Columns');
export const AR_AGING_SUMMARY_DATA_KEY = '_arAgingSummaryData';

export const HIDDEN_COLUMN_STYLE = { display: 'none' };

export const OVERRIDE_KEYS_FROM_TEKION_DEFAULTS = ['configurationDisabled', 'displayName'];

export const CUSTOMER_PERSONAL_INFO_COLUMN_IDS = [
  COLUMN_IDS.CUSTOMER_NAME_COLUMN,
  COLUMN_IDS.CUSTOMER_NUMBER_COLUMN,
  COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS,
  COLUMN_IDS.CUSTOMER_PHONE_NUMBER,
];

export const CUSTOMER_PII_COLUMNS_SET = new Set([COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS, COLUMN_IDS.CUSTOMER_PHONE_NUMBER]);

export const COLUMN_TYPES = {
  SUMMARY: 'SUMMARY',
  DETAIL: 'DETAIL',
};
