// Readers
import arAgingReportReader from 'readers/ArAgingReport';

// Helpers
import { getCustomerNameFromData, getCustomerNumber } from '../../helpers/arAgingReport.columns';

// Constants
import SUMMARY_COLUMN_IDS from '../arAgingReport.listColumnIds';

const BASE_SUMMARY_COLUMN_CONFIG_BY_KEY = {
  [SUMMARY_COLUMN_IDS.CUSTOMER_NAME_COLUMN]: {
    key: SUMMARY_COLUMN_IDS.CUSTOMER_NAME_COLUMN,
    order: 0,
    configurationDisabled: true,
    sortAccessor: getCustomerNameFromData,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_NUMBER_COLUMN]: {
    key: SUMMARY_COLUMN_IDS.CUSTOMER_NUMBER_COLUMN,
    order: 1,
    configurationDisabled: true,
    sortAccessor: getCustomerNumber,
  },
  [SUMMARY_COLUMN_IDS.FINANCE_CHARGE]: {
    key: SUMMARY_COLUMN_IDS.FINANCE_CHARGE,
    order: 2,
    sortAccessor: arAgingReportReader.financeCharge,
  },
  [SUMMARY_COLUMN_IDS.OUTSTANDING_BAL_COLUMN]: {
    key: SUMMARY_COLUMN_IDS.OUTSTANDING_BAL_COLUMN,
    order: 3,
    sortAccessor: arAgingReportReader.outstandingBal,
  },
  [SUMMARY_COLUMN_IDS.FIRST_MONTH_COLUMN]: {
    key: SUMMARY_COLUMN_IDS.FIRST_MONTH_COLUMN,
    order: 4,
    sortAccessor: arAgingReportReader.firstMonth,
  },
  [SUMMARY_COLUMN_IDS.SECOND_MONTH_COLUMN]: {
    key: SUMMARY_COLUMN_IDS.SECOND_MONTH_COLUMN,
    order: 5,
    sortAccessor: arAgingReportReader.secondMonth,
  },
  [SUMMARY_COLUMN_IDS.THIRD_MONTH_COLUMN]: {
    key: SUMMARY_COLUMN_IDS.THIRD_MONTH_COLUMN,
    order: 6,
    sortAccessor: arAgingReportReader.thirdMonth,
  },
  [SUMMARY_COLUMN_IDS.FOURTH_MONTH_COLUMN]: {
    key: SUMMARY_COLUMN_IDS.FOURTH_MONTH_COLUMN,
    order: 7,
    sortAccessor: arAgingReportReader.fourthMonth,
  },
  [SUMMARY_COLUMN_IDS.FIFTH_MONTH_COLUMN]: {
    key: SUMMARY_COLUMN_IDS.FIFTH_MONTH_COLUMN,
    order: 8,
    sortAccessor: arAgingReportReader.fifthMonth,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS]: {
    key: SUMMARY_COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS,
    order: 9,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_PHONE_NUMBER]: {
    key: SUMMARY_COLUMN_IDS.CUSTOMER_PHONE_NUMBER,
    order: 10,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_GROUP]: {
    key: SUMMARY_COLUMN_IDS.CUSTOMER_GROUP,
    order: 11,
  },
};

export default BASE_SUMMARY_COLUMN_CONFIG_BY_KEY;
