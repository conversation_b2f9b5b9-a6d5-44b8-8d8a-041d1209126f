// Constants
import { FIXED, AUTO } from 'twidgets/organisms/pdfGenerator';
import BASE_SUMMARY_COLUMN_CONFIG_BY_KEY from './arAgingReportExport.summaryColumns';
import SUMMARY_COLUMN_IDS from '../arAgingReport.listColumnIds';

const SUMMARY_COLUMN_CONFIG_BY_KEY = {
  [SUMMARY_COLUMN_IDS.CUSTOMER_NAME_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.CUSTOMER_NAME_COLUMN],
    displayName: __('Customer Name'),
    printWidth: [FIXED.id],
    characters: 23,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_NUMBER_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.CUSTOMER_NUMBER_COLUMN],
    displayName: __('Customer Number'),
    printWidth: [FIXED.id],
    characters: 18,
  },
  [SUMMARY_COLUMN_IDS.OUTSTANDING_BAL_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.OUTSTANDING_BAL_COLUMN],
    displayName: __('Outstanding Bal'),
    configurationDisabled: false,
    printWidth: [AUTO.id],
  },
  [SUMMARY_COLUMN_IDS.FINANCE_CHARGE]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.FINANCE_CHARGE],
    displayName: __('Finance Charge'),
    configurationDisabled: false,
    printWidth: [AUTO.id],
  },
  [SUMMARY_COLUMN_IDS.FIRST_MONTH_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.FIRST_MONTH_COLUMN],
    displayName: __('0-30 days'),
    configurationDisabled: false,
    printWidth: [AUTO.id],
  },
  [SUMMARY_COLUMN_IDS.SECOND_MONTH_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.SECOND_MONTH_COLUMN],
    displayName: __('31-60 days'),
    configurationDisabled: false,
    printWidth: [AUTO.id],
  },
  [SUMMARY_COLUMN_IDS.THIRD_MONTH_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.THIRD_MONTH_COLUMN],
    displayName: __('61-90 days'),
    configurationDisabled: false,
    printWidth: [AUTO.id],
  },
  [SUMMARY_COLUMN_IDS.FOURTH_MONTH_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.FOURTH_MONTH_COLUMN],
    displayName: __('91-120 days'),
    configurationDisabled: false,
    printWidth: [AUTO.id],
  },
  [SUMMARY_COLUMN_IDS.FIFTH_MONTH_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.FIFTH_MONTH_COLUMN],
    displayName: __('120+ days'),
    configurationDisabled: false,
    printWidth: [AUTO.id],
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS],
    displayName: __('Customer Email Address'),
    configurationDisabled: false,
    printWidth: [AUTO.id],
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_PHONE_NUMBER]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.CUSTOMER_PHONE_NUMBER],
    displayName: __('Customer Phone Number'),
    configurationDisabled: false,
    printWidth: [AUTO.id],
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_GROUP]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.CUSTOMER_GROUP],
    displayName: __('Customer Group'),
    configurationDisabled: false,
    printWidth: [AUTO.id],
  },
};

export default SUMMARY_COLUMN_CONFIG_BY_KEY;
