import { compose } from 'recompose';

// Lodash
import _sortBy from 'lodash/sortBy';
import _map from 'lodash/map';
import _toLower from 'lodash/toLower';

// utils
import getValuesFromById from 'tbase/utils/getValuesFromById';

// Reader
import accountReader from 'tbase/readers/Account';
import customerInvoiceReader from 'readers/CustomerInvoice';

export const getArGlAccountsFromCustomerInvoice = (accountsMapByDealerId, customerInvoice) => {
  const arGlAccountIds = customerInvoiceReader.receivableGlAccountIds(customerInvoice);
  const dealerId = customerInvoiceReader.dealerId(customerInvoice);
  const dealerAccounts = accountsMapByDealerId[dealerId];
  const arGlAccounts = getValuesFromById(dealerAccounts, arGlAccountIds);
  return arGlAccounts;
};

const getLowerCaseAccountNumber = compose(_toLower, accountReader.accountNumber);

const getSortedArGlAccounts = (accountsMapByDealerId, customerInvoice) => {
  const arGlAccounts = getArGlAccountsFromCustomerInvoice(accountsMapByDealerId, customerInvoice);

  const sortedArGlAccounts = _sortBy(arGlAccounts, getLowerCaseAccountNumber);
  return sortedArGlAccounts;
};

export const getArGlAccountsForPdf = accountsMapByDealerId => customerInvoice => {
  const sortedArGlAccounts = getSortedArGlAccounts(accountsMapByDealerId, customerInvoice);
  return _map(sortedArGlAccounts, accountReader.accountNumber);
};
