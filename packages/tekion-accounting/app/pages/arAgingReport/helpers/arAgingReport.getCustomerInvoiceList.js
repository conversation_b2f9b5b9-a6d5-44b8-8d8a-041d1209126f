// Services
import { fetchCustomerInvoiceListForAgingReport } from 'services/customerInvoiceService';

// Helpers
import { getFiltersForRequest } from './arAgingReport.invoiceDetailsFilter';
import { getMonthAndYearFromFilters } from './arAgingReport.general';
import { createInvoiceListRequest } from './arAgingReport.invoiceDetailsRequest';

const makeCustomerInvoiceRequestDto = ({
  arSetup,
  selectedFilters,
  includeZeroAmountInvoices,
  otherReceivableSetupId,
  customerIds,
  dealerIds,
}) => {
  const filtersForRequest = getFiltersForRequest({
    customerIds,
    includeZeroAmountInvoices,
    selectedFilters,
    otherReceivableSetupId,
    arSetup,
  });
  const { month, year } = getMonthAndYearFromFilters(selectedFilters);
  const request = createInvoiceListRequest(filtersForRequest);
  return {
    request: { ...request, dealerIds },
    month,
    year,
  };
};

const getCustomerInvoiceList = ({
  arSetup,
  selectedFilters,
  includeZeroAmountInvoices,
  otherReceivableSetupId,
  customerIds,
  dealerIds,
}) => {
  const requestDto = makeCustomerInvoiceRequestDto({
    arSetup,
    selectedFilters,
    includeZeroAmountInvoices,
    otherReceivableSetupId,
    customerIds,
    dealerIds,
  });
  return fetchCustomerInvoiceListForAgingReport(requestDto);
};

export default getCustomerInvoiceList;
