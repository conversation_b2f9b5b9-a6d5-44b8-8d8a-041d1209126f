// Constants
import OPERATORS from 'tbase/constants/filterOperators';

// Constants
import { STATUS } from 'tbase/constants/statuses';
import FILTER_IDS from '../constants/arAgingReport.invoiceDetailsFilterIds';

const getArStatusFilterValues = includeZeroAmountInvoices => {
  const filterValues = [STATUS.OPEN];
  if (includeZeroAmountInvoices) {
    filterValues.push(STATUS.CLOSED);
  }
  return filterValues;
};

const getArStatusFilter = (includeZeroAmountInvoices = false) => {
  const arStatuses = getArStatusFilterValues(includeZeroAmountInvoices);

  return {
    type: FILTER_IDS.STATUS,
    operator: OPERATORS.IN,
    values: arStatuses,
  };
};

export default getArStatusFilter;
