/* eslint-disable import/order */
import _isEmpty from 'lodash/isEmpty';
import _filter from 'lodash/filter';

// Constants
import OPERATORS from 'tbase/constants/filterOperators';
import AR_INVOICE_FILTER_IDS from '../constants/arAgingReport.invoiceDetailsFilterIds';
import { CUSTOMER_GROUP_FILTER } from 'constants/arCustomerFilter.js';
import MONTH_FILTER from 'tbusiness/appServices/accounting/utils/monthFilterUtils';

// Helpers
import { makeAdditionalFiltersForOtherReceivables } from 'helpers/arInvoice/arInvoice.filters';
import getArStatusFilter from './arAgingReport.invoiceDetailsARStatusFilter';

const getCustomerFilter = customerIds => ({
  operator: OPERATORS.IN,
  type: AR_INVOICE_FILTER_IDS.CUSTOMER_ID,
  values: customerIds,
});

const EXCLUDED_FILTER_TYPES = new Set([MONTH_FILTER.id, CUSTOMER_GROUP_FILTER.id, AR_INVOICE_FILTER_IDS.CUSTOMER_ID]);

const getSelectedFiltersForInvoices = filter => !EXCLUDED_FILTER_TYPES.has(filter.type);

export const getFiltersForRequest = ({
  customerIds,
  includeZeroAmountInvoices,
  selectedFilters,
  otherReceivableSetupId,
  arSetup,
}) => {
  const filtersForRequest = _filter(selectedFilters, getSelectedFiltersForInvoices);

  const arStatusFilter = getArStatusFilter(includeZeroAmountInvoices);
  filtersForRequest.push(arStatusFilter);

  if (otherReceivableSetupId) {
    const additionalFilters = makeAdditionalFiltersForOtherReceivables(arSetup, otherReceivableSetupId);
    filtersForRequest.push(...additionalFilters);
  }

  if (!_isEmpty(customerIds)) {
    const customerFilter = getCustomerFilter(customerIds);
    filtersForRequest.push(customerFilter);
  }

  return filtersForRequest;
};
