// Lodash
import _map from 'lodash/map';

// Readers
import accountReader from 'tbase/readers/Account';
import journalReader from 'tbase/readers/Journal';

// Helpers
import { getSortedArGlAccounts } from 'helpers/customerInvoice/customerInvoice.agingArGlAccount';
import { getJournalDetailsList } from 'helpers/customerInvoice/arInvoiceList.agingColumnAccessors';

export const getSortedArGlAccountNumbers = customerInvoice => {
  const sortedArGlAccounts = getSortedArGlAccounts(customerInvoice);

  const sortedArGlAccountNumbers = _map(sortedArGlAccounts, accountReader.accountNumber);
  return sortedArGlAccountNumbers;
};

export const getJournalNumbers = customerInvoice => {
  const journals = getJournalDetailsList(customerInvoice);
  const journalNumbers = _map(journals, journalReader.journalNumber);
  return journalNumbers;
};
