/* eslint-disable import/order */
// Lodash
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _keyBy from 'lodash/keyBy';
import _sortBy from 'lodash/sortBy';
import _map from 'lodash/map';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import {
  AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID,
  AR_AGING_REPORT_DETAIL_COLUMN_CONFIGURATION_ID,
  COLUMN_TYPES,
} from '../../constants/exports/arAgingReportExport.general';
import FEATURE_FLAGS from 'tbusiness/appServices/accounting/constants/featureFlags';

// Helpers
import { createColumnsFromPdfConfiguration } from 'twidgets/organisms/pdfGenerator';
import { updateAgingDetailsColumns } from './arAgingReportExport.invoiceDetailsCentralColumns';
import { getColumnConfigMapByColumnType } from './arAgingReportExport.detailedCentralColumns';
import { getUpdatedColumn } from './arAgingReportExport.general';

const getArPDFColumns = ({
  columnsType,
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber,
  selectedMonth,
  shouldShowBusinessUnitColumn,
}) => {
  const COLUMN_CONFIG_MAP_BY_COLUMN_TYPE = getColumnConfigMapByColumnType({
    getFormattedCurrency,
    getFormattedNumber,
    getFormattedPhoneNumber,
    selectedMonth,
    shouldShowBusinessUnitColumn,
  });
  const columns = _map(COLUMN_CONFIG_MAP_BY_COLUMN_TYPE[columnsType], getUpdatedColumn);
  return _sortBy(columns, 'order');
};

const makeArAgingColumns = ({
  columnsType,
  arAgingReportColumnConfigurations,
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber,
  selectedMonth,
  shouldShowBusinessUnitColumn,
}) => {
  let columns = getArPDFColumns({
    columnsType,
    getFormattedCurrency,
    getFormattedNumber,
    getFormattedPhoneNumber,
    selectedMonth,
    shouldShowBusinessUnitColumn,
  });
  if (!_isEmpty(arAgingReportColumnConfigurations)) {
    const defaultColumnsById = _keyBy(columns, 'id');
    columns = createColumnsFromPdfConfiguration(defaultColumnsById, arAgingReportColumnConfigurations);
  }
  return columns;
};

export const getArAgingSummaryReportColumnConfigurations = (
  pdfConfiguration = EMPTY_OBJECT,
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber
) => {
  const { columnConfigurations } = pdfConfiguration;
  const arAgingSummaryColumnConfiguration = getArAgingSummaryColumnFromConfigurations(columnConfigurations);
  const arAgingSummaryColumns = makeArAgingColumns({
    columnsType: COLUMN_TYPES.SUMMARY,
    arAgingReportColumnConfigurations: arAgingSummaryColumnConfiguration,
    getFormattedCurrency,
    getFormattedNumber,
    getFormattedPhoneNumber,
  });
  return [
    {
      ...arAgingSummaryColumnConfiguration,
      id: AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID,
      title: __('Summary Columns'),
      columns: arAgingSummaryColumns,
    },
  ];
};

export const getCentralArAgingDetailReportColumnConfigurations = (
  pdfConfiguration = EMPTY_OBJECT,
  dealersById = EMPTY_OBJECT,
  journalsMapByDealerId = EMPTY_OBJECT,
  accountsMapByDealerId = EMPTY_OBJECT,
  getFormattedDateAndTime,
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber,
  selectedMonthEndTime,
  shouldShowBusinessUnitColumn,
  dealerDetailsById,
  featureFlags
) => {
  const { columnConfigurations } = pdfConfiguration;
  const isExternalDealerIdEnabled = featureFlags.getFlag(FEATURE_FLAGS.IS_EXTERNAL_DEALER_ID_ENABLED);
  const arAgingColumnConfigurations = getArAgingSummaryReportColumnConfigurations(
    pdfConfiguration,
    getFormattedCurrency,
    getFormattedNumber,
    getFormattedPhoneNumber
  );
  const arAgingDetailColumnConfiguration = getArAgingDetailColumnFromConfigurations(columnConfigurations);
  const arAgingDetailColumns = makeArAgingColumns({
    columnsType: COLUMN_TYPES.DETAIL,
    arAgingReportColumnConfigurations: arAgingDetailColumnConfiguration,
    getFormattedCurrency,
    getFormattedNumber,
    selectedMonth: selectedMonthEndTime,
    shouldShowBusinessUnitColumn,
  });

  const updatedAgingDetailsColumns = updateAgingDetailsColumns({
    arAgingDetailColumns,
    dealersById,
    journalsMapByDealerId,
    accountsMapByDealerId,
    isExternalDealerIdEnabled,
    getFormattedDateAndTime,
    dealerDetailsById,
  });
  arAgingColumnConfigurations.push({
    ...arAgingDetailColumnConfiguration,
    id: AR_AGING_REPORT_DETAIL_COLUMN_CONFIGURATION_ID,
    title: __('Detail Columns'),
    columns: updatedAgingDetailsColumns,
  });
  return arAgingColumnConfigurations;
};

export const getArAgingSummaryColumnFromConfigurations = columnConfigurations =>
  _find(columnConfigurations, ['id', AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID]);

export const getArAgingDetailColumnFromConfigurations = columnConfigurations =>
  _find(columnConfigurations, ['id', AR_AGING_REPORT_DETAIL_COLUMN_CONFIGURATION_ID]);
