/* eslint-disable import/order */
// Lodash
import _constant from 'lodash/constant';
import _assign from 'lodash/assign';

// Readers
import arAgingReportReader from 'readers/ArAgingReport';

// Components
import TextRenderer from 'tcomponents/molecules/CellRenderers/TextRenderer';

// Utils
import readAndGetCommaSeparatedValues from 'utils/readAndGetCommaSeparatedValues';

// Constants
import BASE_SUMMARY_COLUMN_CONFIG_BY_KEY from '../../constants/exports/arAgingReportExport.summaryColumns';
import DETAIL_COLUMN_IDS from 'constants/arInvoiceList.columnIds';
import SUMMARY_COLUMN_IDS from '../../constants/arAgingReport.listColumnIds';
import { COLUMN_TYPES } from '../../constants/exports/arAgingReportExport.general';

// Helpers
import {
  getCustomerNameFromData,
  getCustomerNumber,
  getAgingBalanceAmount,
  getCustomerEmail,
  getCustomerPhoneNumber,
  getCustomerGroup,
} from '../arAgingReport.columns';
import { getJournalNumbers, getSortedArGlAccountNumbers } from './arAgingReportExport.accessors';
import { getBaseColumnConfigWithIds } from 'helpers/arInvoiceList.baseColumns';
import getCustomerDeletionComplianceRenderer from 'twidgets/appServices/accounting/helpers/getCustomerDeletionComplianceRenderer';
import { getDeleteCustomerCompliancePdfProps } from 'twidgets/appServices/accounting/helpers/customerDeleteCompliance';

// Styles
import styles from '../../arAgingReportExport.module.scss';

const getBaseCellProps = _constant({ contentClassName: styles.cell });

const getCustomerDeletionProps = _constant({ ...getBaseCellProps(), ...getDeleteCustomerCompliancePdfProps() });

const WithCustomerDeletionComplianceRenderer = getCustomerDeletionComplianceRenderer();

export const getSummaryColumnConfigByKey = (getFormattedCurrency, getFormattedPhoneNumber) => ({
  [SUMMARY_COLUMN_IDS.FIFTH_MONTH_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.FIFTH_MONTH_COLUMN],
    Header: __('120+ days'),
    id: SUMMARY_COLUMN_IDS.FIFTH_MONTH_COLUMN,
    accessor: getAgingBalanceAmount(arAgingReportReader.fifthMonth, getFormattedCurrency),
    headerClassName: `${styles.listHeader} tk-table-header-right-align`,
    className: `${styles.cell} tk-table-cell-right-align`,
    resizable: false,
  },
  [SUMMARY_COLUMN_IDS.FIRST_MONTH_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.FIRST_MONTH_COLUMN],
    Header: __('0-30 days'),
    id: SUMMARY_COLUMN_IDS.FIRST_MONTH_COLUMN,
    accessor: getAgingBalanceAmount(arAgingReportReader.firstMonth, getFormattedCurrency),
    headerClassName: `${styles.listHeader} tk-table-header-right-align`,
    className: `${styles.cell} tk-table-cell-right-align`,
    resizable: false,
  },
  [SUMMARY_COLUMN_IDS.FOURTH_MONTH_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.FOURTH_MONTH_COLUMN],
    Header: __('91-120 days'),
    id: SUMMARY_COLUMN_IDS.FOURTH_MONTH_COLUMN,
    accessor: getAgingBalanceAmount(arAgingReportReader.fourthMonth, getFormattedCurrency),
    headerClassName: `${styles.listHeader} tk-table-header-right-align`,
    className: `${styles.cell} tk-table-cell-right-align`,
    resizable: false,
  },
  [SUMMARY_COLUMN_IDS.OUTSTANDING_BAL_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.OUTSTANDING_BAL_COLUMN],
    Header: __('Outstanding Bal'),
    id: SUMMARY_COLUMN_IDS.OUTSTANDING_BAL_COLUMN,
    accessor: getAgingBalanceAmount(arAgingReportReader.outstandingBal, getFormattedCurrency),
    headerClassName: `${styles.listHeader} tk-table-header-right-align`,
    className: `${styles.cell} tk-table-cell-right-align`,
    resizable: false,
  },
  [SUMMARY_COLUMN_IDS.SECOND_MONTH_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.SECOND_MONTH_COLUMN],
    Header: __('31-60 days'),
    id: SUMMARY_COLUMN_IDS.SECOND_MONTH_COLUMN,
    accessor: getAgingBalanceAmount(arAgingReportReader.secondMonth, getFormattedCurrency),
    headerClassName: `${styles.listHeader} tk-table-header-right-align`,
    className: `${styles.cell} tk-table-cell-right-align`,
    resizable: false,
  },
  [SUMMARY_COLUMN_IDS.THIRD_MONTH_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.THIRD_MONTH_COLUMN],
    Header: __('61-90 days'),
    id: SUMMARY_COLUMN_IDS.THIRD_MONTH_COLUMN,
    accessor: getAgingBalanceAmount(arAgingReportReader.thirdMonth, getFormattedCurrency),
    headerClassName: `${styles.listHeader} tk-table-header-right-align`,
    className: `${styles.cell} tk-table-cell-right-align`,
    resizable: false,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_NAME_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.CUSTOMER_NAME_COLUMN],
    Header: __('Customer Name'),
    id: SUMMARY_COLUMN_IDS.CUSTOMER_NAME_COLUMN,
    accessor: getCustomerNameFromData,
    headerClassName: styles.listHeader,
    getProps: getCustomerDeletionProps,
    resizable: false,
    Cell: WithCustomerDeletionComplianceRenderer,
    width: 150,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_NUMBER_COLUMN]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.CUSTOMER_NUMBER_COLUMN],
    Header: __('Customer Number'),
    id: SUMMARY_COLUMN_IDS.CUSTOMER_NUMBER_COLUMN,
    accessor: getCustomerNumber,
    headerClassName: styles.listHeader,
    getProps: getCustomerDeletionProps,
    resizable: false,
    width: 120,
    Cell: WithCustomerDeletionComplianceRenderer,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS],
    Header: __('Customer Email Address'),
    id: SUMMARY_COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS,
    accessor: getCustomerEmail,
    headerClassName: styles.listHeader,
    getProps: getCustomerDeletionProps,
    resizable: false,
    Cell: WithCustomerDeletionComplianceRenderer,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_PHONE_NUMBER]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.CUSTOMER_PHONE_NUMBER],
    Header: __('Customer Phone Number'),
    id: SUMMARY_COLUMN_IDS.CUSTOMER_PHONE_NUMBER,
    accessor: getCustomerPhoneNumber(getFormattedPhoneNumber),
    headerClassName: styles.listHeader,
    getProps: getCustomerDeletionProps,
    resizable: false,
    Cell: WithCustomerDeletionComplianceRenderer,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_GROUP]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.CUSTOMER_GROUP],
    Header: __('Customer Group'),
    id: SUMMARY_COLUMN_IDS.CUSTOMER_GROUP,
    accessor: getCustomerGroup,
    headerClassName: styles.listHeader,
    getProps: getBaseCellProps,
    resizable: false,
    Cell: TextRenderer,
  },
  [SUMMARY_COLUMN_IDS.FINANCE_CHARGE]: {
    ...BASE_SUMMARY_COLUMN_CONFIG_BY_KEY[SUMMARY_COLUMN_IDS.FINANCE_CHARGE],
    Header: __('Finance Charge'),
    id: SUMMARY_COLUMN_IDS.FINANCE_CHARGE,
    getProps: getBaseCellProps,
    accessor: getAgingBalanceAmount(arAgingReportReader.financeCharge, getFormattedCurrency),
    headerClassName: `${styles.listHeader} tk-table-header-right-align`,
    className: `${styles.cell} tk-table-cell-right-align`,
    resizable: false,
    Cell: TextRenderer,
  },
});

export const withAdditionalConfig = (columnConfig, additional) => _assign({}, columnConfig, additional);

const BASE_ADDITIONAL_CONFIG = {
  getProps: getBaseCellProps,
};

const AR_GL_ACCOUNT_ADDITIONAL_CONFIG = {
  ...BASE_ADDITIONAL_CONFIG,
  accessor: readAndGetCommaSeparatedValues(getSortedArGlAccountNumbers),
  Cell: TextRenderer,
};

const JOURNAL_NUMBER_ADDITIONAL_CONFIG = {
  ...BASE_ADDITIONAL_CONFIG,
  accessor: readAndGetCommaSeparatedValues(getJournalNumbers),
  Cell: TextRenderer,
};

const getDetailsColumnConfigByKey = (getFormattedCurrency, getFormattedNumber, selectedMonth) => {
  const DETAIL_COLUMN_CONFIG = getBaseColumnConfigWithIds(getFormattedCurrency, getFormattedNumber, selectedMonth);
  return {
    [DETAIL_COLUMN_IDS.INVOICE_NUMBER]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.INVOICE_NUMBER],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.INVOICE_TYPE]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.INVOICE_TYPE],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.INVOICE_DATE]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.INVOICE_DATE],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.ORIGINAL_AMOUNT]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.ORIGINAL_AMOUNT],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.DUE_AMOUNT]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.DUE_AMOUNT],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.RECEIVED_AMOUNT]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.RECEIVED_AMOUNT],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.DUE_DATE]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.DUE_DATE],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.INVOICE_AGE]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.INVOICE_AGE],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.AR_GL_ACCOUNT]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.AR_GL_ACCOUNT],
      AR_GL_ACCOUNT_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.LAST_RECEIPT_DATE]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.LAST_RECEIPT_DATE],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.JOURNAL]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.JOURNAL],
      JOURNAL_NUMBER_ADDITIONAL_CONFIG
    ),
  };
};

export const getColumnConfigMapByColumnType = (
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber,
  selectedMonth
) => ({
  [COLUMN_TYPES.SUMMARY]: getSummaryColumnConfigByKey(getFormattedCurrency, getFormattedPhoneNumber),
  [COLUMN_TYPES.DETAIL]: getDetailsColumnConfigByKey(getFormattedCurrency, getFormattedNumber, selectedMonth),
});
