/* eslint-disable import/order */
// Lodash
import _constant from 'lodash/constant';

// Components
import Text<PERSON>ender<PERSON> from 'tcomponents/molecules/CellRenderers/TextRenderer';

// Constants
import DETAIL_COLUMN_IDS from 'constants/arInvoiceList.columnIds';
import { FRANCHISE_COLUMN, BUSINESS_UNIT_COLUMN } from 'constants/arInvoiceList.baseAgingColumns';
import { COLUMN_TYPES } from '../../constants/exports/arAgingReportExport.general';

// Helpers
import { withAdditionalConfig, getSummaryColumnConfigByKey } from './arAgingReportExport.columns';
import { getBaseColumnConfigWithIds } from 'helpers/arInvoiceList.baseColumns';

// Styles
import styles from '../../arAgingReport.module.scss';

const getBaseCellProps = _constant({ contentClassName: styles.cell });

const BASE_ADDITIONAL_CONFIG = {
  getProps: getBaseCellProps,
  Cell: TextRenderer,
};

const getDetailColumnConfigByKey = (
  getFormattedCurrency,
  getFormattedNumber,
  selectedMonth,
  shouldShowBusinessUnitColumn
) => {
  const DETAIL_COLUMN_CONFIG = getBaseColumnConfigWithIds(getFormattedCurrency, getFormattedNumber, selectedMonth);
  return {
    ...(shouldShowBusinessUnitColumn
      ? { [DETAIL_COLUMN_IDS.BUSINESS_UNIT]: withAdditionalConfig(BUSINESS_UNIT_COLUMN, BASE_ADDITIONAL_CONFIG) }
      : { [DETAIL_COLUMN_IDS.FRANCHISE]: withAdditionalConfig(FRANCHISE_COLUMN, BASE_ADDITIONAL_CONFIG) }),
    [DETAIL_COLUMN_IDS.INVOICE_NUMBER]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.INVOICE_NUMBER],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.INVOICE_TYPE]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.INVOICE_TYPE],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.INVOICE_DATE]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.INVOICE_DATE],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.ORIGINAL_AMOUNT]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.ORIGINAL_AMOUNT],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.DUE_AMOUNT]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.DUE_AMOUNT],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.RECEIVED_AMOUNT]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.RECEIVED_AMOUNT],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.DUE_DATE]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.DUE_DATE],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.INVOICE_AGE]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.INVOICE_AGE],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.AR_GL_ACCOUNT]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.AR_GL_ACCOUNT],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.LAST_RECEIPT_DATE]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.LAST_RECEIPT_DATE],
      BASE_ADDITIONAL_CONFIG
    ),
    [DETAIL_COLUMN_IDS.JOURNAL]: withAdditionalConfig(
      DETAIL_COLUMN_CONFIG[DETAIL_COLUMN_IDS.JOURNAL],
      BASE_ADDITIONAL_CONFIG
    ),
  };
};

export const getColumnConfigMapByColumnType = ({
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber,
  selectedMonth,
  shouldShowBusinessUnitColumn,
}) => ({
  [COLUMN_TYPES.SUMMARY]: getSummaryColumnConfigByKey(getFormattedCurrency, getFormattedPhoneNumber),
  [COLUMN_TYPES.DETAIL]: getDetailColumnConfigByKey(
    getFormattedCurrency,
    getFormattedNumber,
    selectedMonth,
    shouldShowBusinessUnitColumn
  ),
});
