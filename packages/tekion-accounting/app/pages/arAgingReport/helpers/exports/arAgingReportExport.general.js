/* eslint-disable import/order */
import { compose } from 'recompose';

// Lodash
import _map from 'lodash/map';
import _sortBy from 'lodash/sortBy';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _property from 'lodash/property';
import _filter from 'lodash/filter';

// Readers
import dealerInfoReader from 'tbase/readers/DealerInfo';

// Helpers
import { getColumnConfigMapByColumnType } from './arAgingReportExport.columns';

// Utils
import { tget } from 'tbase/utils/general';
import { getMonthFilterFromFilters } from 'tbusiness/appServices/accounting/utils/monthFilterUtils';

// Constants
import { CUSTOMER_PII_COLUMNS_SET } from '../../constants/exports/arAgingReportExport.general';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import FEATURE_FLAGS from 'tbusiness/appServices/accounting/constants/featureFlags';

// Builders
import { FeatureFlags } from 'twidgets/context/featureFlags';

export const getDealerAddress = dealerInfo => {
  const dealerContact = dealerInfoReader.dealerAddress(dealerInfo);
  const dealerAddress = _head(dealerContact);
  if (!_isEmpty(dealerAddress)) {
    const streetAddress1 = tget(dealerAddress, 'streetAddress1', '');
    const state = tget(dealerAddress, 'state', '');
    const city = tget(dealerAddress, 'city', '');
    const zipCode = tget(dealerAddress, 'zipCode', '');
    return `${streetAddress1}, ${city}, ${state}, ${zipCode}`;
  }
  return undefined;
};

const getUpdatedColumn = column => ({
  ...column,
  resizable: false,
  checked: true,
});

export const getArPDFColumns = ({
  columnsType,
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber,
  selectedMonth,
}) => {
  const columnConfigMapByColumnType = getColumnConfigMapByColumnType(
    getFormattedCurrency,
    getFormattedNumber,
    getFormattedPhoneNumber,
    selectedMonth
  );
  const columns = _map(columnConfigMapByColumnType[columnsType], getUpdatedColumn);
  return _sortBy(columns, 'order');
};

const getUntilTimeFromMonthFilter = _property('values');

export const getEndTimeFromMonthFilter = compose(getUntilTimeFromMonthFilter, getMonthFilterFromFilters);

const shouldAddSummaryColumn =
  shouldMaskCustomerPIIColumns =>
    (column = EMPTY_OBJECT) => {
      const { id: columnId, key: columnKey } = column;

      if (
        shouldMaskCustomerPIIColumns &&
        (CUSTOMER_PII_COLUMNS_SET.has(columnId) || CUSTOMER_PII_COLUMNS_SET.has(columnKey))
      ) {
        return false;
      }
      return true;
    };

export const getFilteredArAgingSummaryColumns = (columns, featureFlags = new FeatureFlags()) => {
  const shouldMaskCustomerPIIColumns = featureFlags.getFlag(FEATURE_FLAGS.SHOULD_MASK_CUSTOMER_PII);

  return _filter(columns, shouldAddSummaryColumn(shouldMaskCustomerPIIColumns));
};