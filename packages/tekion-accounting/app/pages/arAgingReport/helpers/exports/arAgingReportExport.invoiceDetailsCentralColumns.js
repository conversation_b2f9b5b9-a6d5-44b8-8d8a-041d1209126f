/* eslint-disable import/order */
// Lodash
import _findIndex from 'lodash/findIndex';

// Constants
import COLUMN_IDS from 'constants/arInvoiceList.columnIds';

// Readers
import customerInvoiceReader from 'readers/CustomerInvoice';
import dealerInfoReader from 'tbase/readers/DealerInfo';

// Helpers
import { getArGlAccountsForPdf } from '../arAgingReport.arGlAccount';
import { journalAccessorForPdf } from 'helpers/customerInvoice/arInvoiceList.centralisedJournal';
import makeBusinessUnitLabel from 'tbusiness/appServices/accounting/formatters/businessUnitLabel';
import { getColumnKey } from 'helpers/columnHelper/getColumnProperties';

// Utils
import updateElementAtIndex from 'utils/updateElementAtIndex';
import Process from 'tbase/utils/Process';
import readAndGetCommaSeparatedValues from 'utils/readAndGetCommaSeparatedValues';
import makeFormattedDateAccessor from 'tbusiness/utils/makeFormattedDateAccessor';
import { updateListItems } from 'utils/list';

const getColumnIndex = (columns, id) => _findIndex(columns, { id });

const getArGlAccountColumn = (baseColumn, accountsMapByDealerId) => ({
  ...baseColumn,
  accessor: readAndGetCommaSeparatedValues(getArGlAccountsForPdf(accountsMapByDealerId)),
});

const getBusinessUnitColumnAccessor = (dealerDetailsById, isExternalDealerIdEnabled) => rowData => {
  const dealerId = customerInvoiceReader.dealerId(rowData);
  if (isExternalDealerIdEnabled) {
    return makeBusinessUnitLabel(dealerDetailsById, dealerId);
  }
  const dealerInfo = dealerDetailsById[dealerId];
  return dealerInfoReader.dealerName(dealerInfo);
};

const updateColumnsAccessor = (next, params) => {
  const { columns, getFormattedDateAndTime, dealerDetailsById, isExternalDealerIdEnabled } = params;
  const columnsToUpdate = {
    [COLUMN_IDS.INVOICE_DATE]: {
      accessor: makeFormattedDateAccessor(customerInvoiceReader.invoiceDate, undefined, getFormattedDateAndTime),
    },
    [COLUMN_IDS.DUE_DATE]: {
      accessor: makeFormattedDateAccessor(customerInvoiceReader.dueDate, undefined, getFormattedDateAndTime),
    },
    [COLUMN_IDS.LAST_RECEIPT_DATE]: {
      accessor: makeFormattedDateAccessor(customerInvoiceReader.lastReceiptDate, undefined, getFormattedDateAndTime),
    },
    [COLUMN_IDS.BUSINESS_UNIT]: {
      accessor: getBusinessUnitColumnAccessor(dealerDetailsById, isExternalDealerIdEnabled),
    },
  };

  return next({
    ...params,
    columns: updateListItems(columns, columnsToUpdate, getColumnKey),
  });
};

const updateArGlAccountColumn = (next, params) => {
  const { columns, accountsMapByDealerId } = params;
  const arGlAccountIndex = getColumnIndex(columns, COLUMN_IDS.AR_GL_ACCOUNT);

  if (arGlAccountIndex >= 0) {
    const arGlAccountColumn = getArGlAccountColumn(columns[arGlAccountIndex], accountsMapByDealerId);
    const updatedColumns = updateElementAtIndex(columns, arGlAccountColumn, arGlAccountIndex);
    return next({
      ...params,
      columns: updatedColumns,
    });
  }
  return next(params);
};

const getFranchiseAccessor = (dealersById, isExternalDealerIdEnabled) => invoice => {
  const dealerId = customerInvoiceReader.dealerId(invoice);
  if (isExternalDealerIdEnabled) {
    return makeBusinessUnitLabel(dealersById, dealerId);
  }
  const dealerInfo = dealersById[dealerId];
  return dealerInfoReader.dealerName(dealerInfo);
};

const getFranchiseColumn = (baseColumn, dealersById, isExternalDealerIdEnabled) => ({
  ...baseColumn,
  accessor: getFranchiseAccessor(dealersById, isExternalDealerIdEnabled),
});

const updateFranchiseColumn = (next, params) => {
  const { columns, dealersById, isExternalDealerIdEnabled } = params;
  const franchiseIndex = getColumnIndex(columns, COLUMN_IDS.FRANCHISE);

  if (franchiseIndex >= 0) {
    const franchiseColumn = getFranchiseColumn(columns[franchiseIndex], dealersById, isExternalDealerIdEnabled);
    const updatedColumns = updateElementAtIndex(columns, franchiseColumn, franchiseIndex);
    return next({
      ...params,
      columns: updatedColumns,
    });
  }
  return next(params);
};

const getJournalColumn = (baseColumn, journalsMapByDealerId) => ({
  ...baseColumn,
  accessor: readAndGetCommaSeparatedValues(journalAccessorForPdf(journalsMapByDealerId)),
});

const updateJournalColumn = (next, params) => {
  const { columns, journalsMapByDealerId } = params;
  const journalIndex = getColumnIndex(columns, COLUMN_IDS.JOURNAL);

  if (journalIndex >= 0) {
    const journalColumn = getJournalColumn(columns[journalIndex], journalsMapByDealerId);
    return updateElementAtIndex(columns, journalColumn, journalIndex);
  }
  return columns;
};

const updateColumnsProcess = new Process()
  .addHandler(updateColumnsAccessor)
  .addHandler(updateArGlAccountColumn)
  .addHandler(updateFranchiseColumn)
  .addHandler(updateJournalColumn);

export const updateAgingDetailsColumns = ({
  arAgingDetailColumns,
  accountsMapByDealerId,
  journalsMapByDealerId,
  dealersById,
  isExternalDealerIdEnabled,
  getFormattedDateAndTime,
  dealerDetailsById,
}) =>
  updateColumnsProcess.run({
    columns: arAgingDetailColumns,
    accountsMapByDealerId,
    journalsMapByDealerId,
    dealersById,
    isExternalDealerIdEnabled,
    getFormattedDateAndTime,
    dealerDetailsById,
  });
