import { compose } from 'recompose';

// Lodash
import _noop from 'lodash/noop';
import _head from 'lodash/head';
import _keys from 'lodash/keys';

// Constants
import SORT_ORDER from 'tbase/constants/sortOrder';
import { EMPTY_OBJECT } from 'tbase/app.constants';

// Helpers
import { getSortedTableData } from 'tbase/helpers/tableSort.helper';

const getSortAccessor = (columnConfig = EMPTY_OBJECT) => (keyToSort, item) => {
  const columnToSortBy = columnConfig[keyToSort] || EMPTY_OBJECT;
  const { sortAccessor = _noop } = columnToSortBy;
  return sortAccessor(item);
};

export const getSortedAgingReportList = (arAgingReportList, columnConfig, sortDetails) =>
  getSortedTableData(arAgingReportList, sortDetails, getSortAccessor(columnConfig));

export const getHiddenColumnId = columnKey => `hidden_${columnKey}`;

const getKeyToSort = compose(_head, _keys);

export const getSortProps = sortDetails => {
  const keyToSort = getKeyToSort(sortDetails);
  const isSortDesc = sortDetails[keyToSort] === SORT_ORDER.DESC;
  return [
    [
      {
        id: getHiddenColumnId(keyToSort),
        desc: isSortDesc,
      },
    ],
    keyToSort,
  ];
};