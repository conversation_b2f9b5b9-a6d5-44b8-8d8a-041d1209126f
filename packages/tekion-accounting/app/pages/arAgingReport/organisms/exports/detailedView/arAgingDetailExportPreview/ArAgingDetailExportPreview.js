/* eslint-disable import/order */
import { withProps } from 'recompose';

// Containers
import withRouter from 'tcomponents/hoc/withRouter';
import BaseARAgingDetailReport from '../baseArAgingDetailExport';

// Helpers
import { getProps } from './helpers/arAgingDetailExportPreview.general';

const ArAgingDetailExportPreview = withProps(getProps)(BaseARAgingDetailReport);

ArAgingDetailExportPreview.propTypes = BaseARAgingDetailReport.propTypes;

ArAgingDetailExportPreview.defaultProps = BaseARAgingDetailReport.defaultProps;

export default withRouter(ArAgingDetailExportPreview);
