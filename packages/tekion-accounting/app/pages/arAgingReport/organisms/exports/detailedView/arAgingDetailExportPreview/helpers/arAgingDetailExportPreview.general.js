/* eslint-disable import/order */

import _slice from 'lodash/slice';
import _reduce from 'lodash/reduce';
import _concat from 'lodash/concat';
import _map from 'lodash/map';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import { PREVIEW_AGGREGATE_MAX_COUNT, PREVIEW_DRILLDOWN_MAX_COUNT } from 'constants/pdf';
import {
  AR_AGING_DETAIL_PREVIEW_ASSET_TYPE,
  AR_AGING_DETAIL_DOCUMENT_TYPE,
} from '../../../../../constants/exports/arAgingReportExport.preview';

// Actions
import { resolveInvoiceDependencies, getArAgingReportList } from '../../../../../actions/arAgingReport.exportActions';

// Readers
import arAgingReportReader from 'readers/ArAgingReport';
import arInvoicesPerCustomerReader from '../../../../../readers/ArInvoicesPerCustomer';

// Helpers
import getCustomerInvoiceList from '../../../../../helpers/arAgingReport.getCustomerInvoiceList';
import { handlePrintPDF } from '../../../helpers/arAgingReportExport.makeRequest';

// Utils
import getDataFromResponse from 'utils/getDataFromResponse';

const accumulateTrimmedInvoices = (allTrimmedInvoices, invoices) => {
  const trimmedInvoices = _slice(invoices, 0, PREVIEW_DRILLDOWN_MAX_COUNT);

  return _concat(allTrimmedInvoices, trimmedInvoices);
};

const handleInvoiceListSuccess =
  ({ arSummary, resolvedArAgingReportList, localLookup }) =>
  response => {
    const arInvoicesPerCustomerData = getDataFromResponse(response);
    const arInvoicesPerCustomer = arInvoicesPerCustomerReader.invoicesPerCustomer(arInvoicesPerCustomerData);
    const invoices = _reduce(arInvoicesPerCustomer, accumulateTrimmedInvoices, EMPTY_ARRAY);

    return resolveInvoiceDependencies({ localLookup, invoices, arSummary, resolvedArAgingReportList });
  };

const getInvoiceDetailsForAgingReport =
  ({
    selectedFilters,
    localLookup,
    arSetup,
    includeZeroAmountInvoices,
    otherReceivableSetupId,
    accessibleEnterpriseWorkspaceIds,
  }) =>
  ({ arSummary, resolvedArAgingReportList }) => {
    const trimmedArAgingReportList = _slice(resolvedArAgingReportList, 0, PREVIEW_AGGREGATE_MAX_COUNT);
    return getCustomerInvoiceList({
      arSetup,
      selectedFilters,
      includeZeroAmountInvoices,
      otherReceivableSetupId,
      customerIds: _map(trimmedArAgingReportList, arAgingReportReader.customerId),
      dealerIds: accessibleEnterpriseWorkspaceIds,
    }).then(handleInvoiceListSuccess({ arSummary, resolvedArAgingReportList: trimmedArAgingReportList, localLookup }));
  };

const handlePrintTypstPdf = (getPayloadForPdf, location) => () => {
  const { urlParams } = getPayloadForPdf(location, AR_AGING_DETAIL_DOCUMENT_TYPE);
  return handlePrintPDF(urlParams, AR_AGING_DETAIL_PREVIEW_ASSET_TYPE, AR_AGING_DETAIL_DOCUMENT_TYPE);
};

export const getProps = ({ location, assetId, isCentralPreview, shouldShowBusinessUnitColumn }) => {
  const urlParams = {
    queryString: location.search,
  };
  return {
    getArAgingReportList,
    getInvoiceDetailsForAgingReport,
    isPreviewPage: true,
    onPrintPdf: () => handlePrintPDF(urlParams, AR_AGING_DETAIL_PREVIEW_ASSET_TYPE, AR_AGING_DETAIL_DOCUMENT_TYPE),
    onPrintTypstPdf: handlePrintTypstPdf,
    assetId,
    isCentralPreview,
    shouldShowBusinessUnitColumn,
  };
};
