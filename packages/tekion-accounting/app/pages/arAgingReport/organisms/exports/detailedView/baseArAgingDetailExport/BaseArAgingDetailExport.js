/* eslint-disable import/order */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';

// Lodash
import _noop from 'lodash/noop';
import _identity from 'lodash/identity';

// Containers
import { withTekionConversion } from '@tekion/tekion-conversion-web';
import withCustomerComplianceHeaderInfo from 'containers/withCustomerComplianceHeaderInfo';

// Components
import { Pdf } from 'organisms/pdfGenerator';
import RightHeaderPanel from '../../../../atoms/rightHeaderPanel';
import PDFViewer from 'organisms/pdfViewer';
import Loader from 'tcomponents/molecules/loader';

// Contexts
import FeatureFlagsContext, { EMPTY_FEATURE_FLAGS } from 'twidgets/context/featureFlags';

// Constants
import { HEADING_HELP_TEXT } from 'constants/pdf';
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { HEADER_CONFIG } from './constants/baseArAgingDetailExport.sectionConfiguration';
import { AR_AGING_DETAIL_PDF_PREVIEW_TITLE } from './constants/baseArAgingDetailExport.general';
import { AR_AGING_DETAIL_DOCUMENT_TYPE } from '../../../../constants/exports/arAgingReportExport.preview';
import {
  DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION,
  AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID,
  AR_AGING_REPORT_PDF_ASSET_ID,
  AR_AGING_REPORT_DETAIL_PDF_ASSET_TYPE,
  AR_AGING_REPORT_DETAIL_COLUMN_CONFIGURATION_ID,
  OVERRIDE_KEYS_FROM_TEKION_DEFAULTS,
} from '../../../../constants/exports/arAgingReportExport.general';

// Helpers
import { getColumnConfig } from './helpers/baseArAgingDetailExport.detailColumnConfig';
import { getUpdatedColumnConfig, makeTableData } from './helpers/baseArAgingDetailExport.general';
import { getIsCustomerDeletedInPage } from '../../../../helpers/arAgingReport.general';
import getSectionConfig from './helpers/baseArAgingDetailExport.sectionConfig';
import { getArAgingDetailReportColumnConfigurationsByDealerType } from '../../helpers/arAgingReportExport.columnConfiguration';
import { getPdfConfiguration, loadPdfConfiguration } from '../../helpers/arAgingReportExport.pdfConfiguration';
import { getPayloadForPdf } from '../../../../helpers/arAgingReport.makeExportRequest';

// Readers
import TEnvReader from 'tbase/readers/Env';

const RightHeaderWithCustomerComplianceInfo = withCustomerComplianceHeaderInfo(RightHeaderPanel);

class BaseArAgingDetailExport extends Component {
  state = {
    isLoading: true,
    pdfConfiguration: EMPTY_OBJECT,
  };

  makeTableData = defaultMemoize(makeTableData);

  getUpdatedColumnConfig = defaultMemoize(getUpdatedColumnConfig);

  getIsCustomerDeletedInPage = defaultMemoize(getIsCustomerDeletedInPage);

  getSectionConfig = defaultMemoize(getSectionConfig);

  getColumnConfig = defaultMemoize(getColumnConfig);

  componentDidMount() {
    const { assetId , showTypstPdf} = this.props;
    loadPdfConfiguration({assetType: AR_AGING_REPORT_DETAIL_PDF_ASSET_TYPE, assetId, showTypstPdf, setState: this.setState.bind(this) });
  }

  makePayloadForPDF = () => {
    const { location } = this.props;
    const request = getPayloadForPdf(location, AR_AGING_DETAIL_DOCUMENT_TYPE);
    return request;
  };

  renderTypstPDF({
    featureFlags,
    pdfConfiguration,
    getFormattedNumber,
    getFormattedCurrency,
    getFormattedPhoneNumber,
    selectedMonthEndTime,
    getFormattedDateAndTime,
    assetId,
  }) {
    const {
      isPreviewPage,
      onPrintTypstPdf,
      location,
      isCentralPreview,
      dealersById,
      journalsMapByDealerId,
      accountsMapByDealerId,
      dealerDetailsById,
      shouldShowBusinessUnitColumn,
    } = this.props;
    const isServerSideRendered = TEnvReader.isExportMode();
    const columnConfigurations = getArAgingDetailReportColumnConfigurationsByDealerType(
      pdfConfiguration,
      getFormattedDateAndTime,
      getFormattedCurrency,
      getFormattedNumber,
      getFormattedPhoneNumber,
      featureFlags,
      selectedMonthEndTime,
      isCentralPreview,
      dealersById,
      journalsMapByDealerId,
      accountsMapByDealerId,
      dealerDetailsById,
      shouldShowBusinessUnitColumn
    );
    const defaultPdfConfiguration = getPdfConfiguration(pdfConfiguration, columnConfigurations);

    const onPrintPdf = onPrintTypstPdf(getPayloadForPdf, location);

    const {
      printMode = DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION.printMode,
      printView = DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION.printView,
      textWrap,
    } = pdfConfiguration;

    return (
      <PDFViewer
        assetType={AR_AGING_REPORT_DETAIL_PDF_ASSET_TYPE}
        assetId={assetId}
        pdfTitle={AR_AGING_DETAIL_PDF_PREVIEW_TITLE}
        columnConfigurations={columnConfigurations}
        printView={printView}
        printMode={printMode}
        wordWrap={textWrap}
        defaultPdfConfiguration={defaultPdfConfiguration}
        isServerSideRendered={isServerSideRendered}
        getPayloadForPdf={this.makePayloadForPDF}
        isPreviewPage={isPreviewPage}
        onPreviewPdfPrint={onPrintPdf}
        headingHelpText={HEADING_HELP_TEXT}
      />
    );
  }

  render() {
    const {
      arAgingReportList,
      arInvoiceList,
      onPrintPdf,
      dealerInfo,
      selectedMonthEndTime,
      arSummary,
      getFormattedDateAndTime,
      getFormattedCurrency,
      getFormattedNumber,
      getFormattedPhoneNumber,
      showTypstPdf,
      assetId,
      featureFlags,
    } = this.props;
    const { pdfConfiguration, isLoading } = this.state;
    const tableData = this.makeTableData(arAgingReportList, arInvoiceList);
    const columnConfig = this.getColumnConfig(
      getFormattedCurrency,
      getFormattedNumber,
      getFormattedPhoneNumber,
      selectedMonthEndTime
    );

    const updatedColumnConfig = this.getUpdatedColumnConfig(columnConfig, getFormattedDateAndTime);

    const isCustomerDeleted = this.getIsCustomerDeletedInPage(arAgingReportList);

    const sectionConfig = this.getSectionConfig(featureFlags);

    if (isLoading) return <Loader />;

    return showTypstPdf ? (
      this.renderTypstPDF({
        featureFlags,
        pdfConfiguration,
        getFormattedNumber,
        getFormattedCurrency,
        getFormattedPhoneNumber,
        selectedMonthEndTime,
        getFormattedDateAndTime,
        assetId,
      })
    ) : (
      <Pdf.Container
        title={AR_AGING_DETAIL_PDF_PREVIEW_TITLE}
        tekionDefaultSectionConfigurations={sectionConfig}
        assetType={AR_AGING_REPORT_DETAIL_PDF_ASSET_TYPE}
        assetId={assetId}
        printMode={DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION.printMode}
        printView={DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION.printView}
        overrideKeysFromTekionDefaults={OVERRIDE_KEYS_FROM_TEKION_DEFAULTS}
        headingHelpText={HEADING_HELP_TEXT}
        onPrintPdf={onPrintPdf}>
        <Pdf.Page
          columnConfigByKey={updatedColumnConfig}
          tableData={tableData}
          summaryConfigurationId={AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID}
          detailConfigurationId={AR_AGING_REPORT_DETAIL_COLUMN_CONFIGURATION_ID}
          dealerInfo={dealerInfo}
          headerConfig={HEADER_CONFIG}>
          <RightHeaderWithCustomerComplianceInfo
            arSummary={arSummary}
            dealerInfo={dealerInfo}
            selectedMonthEndTime={selectedMonthEndTime}
            getFormattedDateAndTime={getFormattedDateAndTime}
            getFormattedCurrency={getFormattedCurrency}
            isCustomerDeleted={isCustomerDeleted}
            getFormattedNumber={getFormattedNumber}
          />
        </Pdf.Page>
      </Pdf.Container>
    );
  }
}

BaseArAgingDetailExport.propTypes = {
  arSummary: PropTypes.object,
  dealerInfo: PropTypes.object,
  selectedMonthEndTime: PropTypes.number,
  onPrintPdf: PropTypes.func,
  arAgingReportList: PropTypes.array,
  arInvoiceList: PropTypes.array,
  getFormattedDateAndTime: PropTypes.func,
  getFormattedCurrency: PropTypes.func,
  getFormattedNumber: PropTypes.func,
  getFormattedPhoneNumber: PropTypes.func,
  location: PropTypes.object,
  isPreviewPage: PropTypes.bool,
  onPrintTypstPdf: PropTypes.func,
  assetId: PropTypes.string,
  featureFlags: PropTypes.instanceOf(FeatureFlagsContext),
  showTypstPdf: PropTypes.bool,
  isCentralPreview: PropTypes.bool,
  dealersById: PropTypes.object,
  shouldShowBusinessUnitColumn: PropTypes.bool,
  accountsMapByDealerId: PropTypes.object,
  journalsMapByDealerId: PropTypes.object,
  dealerDetailsById: PropTypes.object,
};

BaseArAgingDetailExport.defaultProps = {
  arSummary: EMPTY_OBJECT,
  dealerInfo: EMPTY_OBJECT,
  selectedMonthEndTime: undefined,
  onPrintPdf: _noop,
  arAgingReportList: EMPTY_ARRAY,
  arInvoiceList: EMPTY_ARRAY,
  getFormattedDateAndTime: _noop,
  getFormattedCurrency: _noop,
  getFormattedNumber: _identity,
  getFormattedPhoneNumber: _identity,
  location: EMPTY_OBJECT,
  isPreviewPage: false,
  onPrintTypstPdf: _noop,
  featureFlags: EMPTY_FEATURE_FLAGS,
  assetId: AR_AGING_REPORT_PDF_ASSET_ID,
  showTypstPdf: false,
  isCentralPreview: false,
  dealersById: EMPTY_OBJECT,
  shouldShowBusinessUnitColumn: false,
  accountsMapByDealerId: EMPTY_OBJECT,
  journalsMapByDealerId: EMPTY_OBJECT,
  dealerDetailsById: EMPTY_OBJECT,
};

export default withTekionConversion(BaseArAgingDetailExport);
