/* eslint-disable import/order */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import { connect } from 'react-redux';
import { compose } from 'recompose';

import _noop from 'lodash/noop';

// Selectors
import {
  getDealerInfo,
  getArSetup,
  getTypstSupportedReportSet,
  getAllJournalsMapByDealerId,
  getAllAccountsMapByDealerId,
} from 'reducers/selectors/app.selectors';
import { getAccessibleEnterpriseWorkspaceIds } from 'reducers/selectors/appCache/accessibleEnterpriseWorkspaceDetails';
import { getTenantDealersById } from 'reducers/selectors/appCache/tenantDealers';
import { getExternalDealerDetailsByWorkspaceId } from 'reducers/selectors/appCache/externalDealerDetails';

// Components
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import Loader from 'tcomponents/molecules/loader';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_SET } from 'tbase/app.constants';
import SUMMARY_COLUMN_CONFIG_BY_KEY from '../../../../constants/exports/arAgingReportExport.summaryPreviewColumnConfig';
import { AR_AGING_REPORT_PDF_ASSET_ID } from '../../../../constants/exports/arAgingReportExport.general';
import TYPST_SUPPORTED_REPORTS from 'tbusiness/appServices/accounting/constants/typstSupportedReports';

// Factories
import getFilename, { EXPORT_VIEWS } from 'factories/filenameFactory';

// Helpers
import getParamsFromQueryString from 'tbusiness/appServices/accounting/utils/getParamsFromQueryString';
import { getSortedAgingReportList } from '../../../../helpers/exports/arAgingReportExport.sort';
import { getEndTimeFromMonthFilter } from '../../../../helpers/exports/arAgingReportExport.general';
import { getPdfConfiguration } from '../../helpers/arAgingReportExport.pdfConfiguration';

// Utils
import shouldShowTypstPdf from 'tbusiness/appServices/accounting/utils/shouldShowTypstPdf';

// Contexts
import FeatureFlagsContext from 'twidgets/context/featureFlags';

// Readers
import { getLocalLookup } from '../../../../reducers/arAgingReportExport.selectors';

// Containers
import BaseArAgingDetailExport from './BaseArAgingDetailExport';

// Actions
import {
  getArAgingReportList as defaultGetArAgingReport,
  getInvoiceDetailsForAgingReport as defaultGetInvoiceDetailsForAgingReport,
} from '../../../../actions/arAgingReport.exportActions';

// Connectors
import { withTekionConversion } from '@tekion/tekion-conversion-web';
import withRouter from 'tcomponents/hoc/withRouter';

class ARAgingDetailExportContainer extends Component {
  getPdfConfiguration = defaultMemoize(getPdfConfiguration);

  getEndTimeFromMonthFilter = defaultMemoize(getEndTimeFromMonthFilter);

  getSortedAgingReportList = defaultMemoize(getSortedAgingReportList);

  getParamsFromQueryString = defaultMemoize(getParamsFromQueryString);

  constructor(props) {
    super(props);
    this.state = {
      arSummary: EMPTY_OBJECT,
      arAgingReportList: EMPTY_ARRAY,
      loading: true,
    };
  }

  componentDidMount() {
    this.setDocumentTitle();
    this.loadInitialData();
  }

  loadInitialData = () => {
    const {
      location,
      localLookup,
      arSetup,
      getArAgingReportList,
      getInvoiceDetailsForAgingReport,
      accessibleEnterpriseWorkspaceIds,
    } = this.props;
    const { filters, additional: additionalQueryParams } = this.getParamsFromQueryString(location.search);
    const { includeZeroAmountInvoices, otherReceivableSetupId } = additionalQueryParams;

    getArAgingReportList({ filters, includeZeroAmountInvoices, accessibleEnterpriseWorkspaceIds })
      .then(
        getInvoiceDetailsForAgingReport({
          selectedFilters: filters,
          localLookup,
          arSetup,
          includeZeroAmountInvoices,
          otherReceivableSetupId,
          accessibleEnterpriseWorkspaceIds,
        })
      )
      .then(this.saveArAgingDetails)
      .catch(this.handleFetchInitialDataFailure)
      .finally(this.setLoaded);
  };

  setLoaded = () => {
    this.setState({ loading: false });
  };

  handleFetchInitialDataFailure = () => {
    toaster(TOASTER_TYPE.ERROR, __('Unable to fetch AR Aging Report details'));
  };

  saveArAgingDetails = ({ arSummary, resolvedArAgingReportList, arInvoiceList }) => {
    const { location } = this.props;
    const { sortDetails } = this.getParamsFromQueryString(location.search);

    const sortedArAgingReportList = this.getSortedAgingReportList(
      resolvedArAgingReportList,
      SUMMARY_COLUMN_CONFIG_BY_KEY,
      sortDetails
    );
    this.setState({
      arAgingReportList: sortedArAgingReportList,
      arSummary,
      arInvoiceList,
    });
  };

  setDocumentTitle = () => {
    const { location, getFormattedDateAndTime } = this.props;
    const { filters } = this.getParamsFromQueryString(location.search);
    const untilTime = this.getEndTimeFromMonthFilter(filters);
    const title = getFilename(EXPORT_VIEWS.AR_AGING, { untilTime, getFormattedDateAndTime });
    document.title = title;
  };

  render() {
    const { loading } = this.state;
    if (loading) {
      return <Loader />;
    }
    const { arSummary, arAgingReportList, arInvoiceList } = this.state;

    const {
      location,
      dealerInfo,
      onPrintPdf,
      onPrintTypstPdf,
      isPreviewPage,
      arSetup,
      assetId,
      typstSupportedReportSet,
      isCentralPreview,
      dealersById,
      journalsMapByDealerId,
      accountsMapByDealerId,
      dealerDetailsById,
      shouldShowBusinessUnitColumn,
    } = this.props;
    const { filters, sortDetails } = this.getParamsFromQueryString(location.search);
    const selectedMonthEndTime = this.getEndTimeFromMonthFilter(filters);
    const featureFlags = this.context;
    const showTypstPdf = shouldShowTypstPdf({
      featureFlags,
      typstSupportedReportSet,
      reportName: TYPST_SUPPORTED_REPORTS.AR_AGING_REPORT,
    });

    return (
      <BaseArAgingDetailExport
        dealerInfo={dealerInfo}
        arSummary={arSummary}
        selectedMonthEndTime={selectedMonthEndTime}
        arAgingReportList={arAgingReportList}
        arInvoiceList={arInvoiceList}
        sortDetails={sortDetails}
        onPrintPdf={onPrintPdf}
        location={location}
        isPreviewPage={isPreviewPage}
        arSetup={arSetup}
        onPrintTypstPdf={onPrintTypstPdf}
        showTypstPdf={showTypstPdf}
        assetId={assetId}
        featureFlags={featureFlags}
        isCentralPreview={isCentralPreview}
        dealersById={dealersById}
        journalsMapByDealerId={journalsMapByDealerId}
        accountsMapByDealerId={accountsMapByDealerId}
        dealerDetailsById={dealerDetailsById}
        shouldShowBusinessUnitColumn={shouldShowBusinessUnitColumn}
      />
    );
  }
}

ARAgingDetailExportContainer.propTypes = {
  dealerInfo: PropTypes.object,
  localLookup: PropTypes.object,
  arSetup: PropTypes.object,
  getArAgingReportList: PropTypes.func,
  getInvoiceDetailsForAgingReport: PropTypes.func,
  getFormattedDateAndTime: PropTypes.func.isRequired,
  accessibleEnterpriseWorkspaceIds: PropTypes.array,
  location: PropTypes.object.isRequired,
  isPreviewPage: PropTypes.bool,
  onPrintTypstPdf: PropTypes.func,
  onPrintPdf: PropTypes.func,
  typstSupportedReportSet: PropTypes.instanceOf(Set),
  assetId: PropTypes.string,
  isCentralPreview: PropTypes.bool,
  shouldShowBusinessUnitColumn: PropTypes.bool,
  dealersById: PropTypes.object,
  accountsMapByDealerId: PropTypes.object,
  journalsMapByDealerId: PropTypes.object,
  dealerDetailsById: PropTypes.object,
};

ARAgingDetailExportContainer.contextType = FeatureFlagsContext;

ARAgingDetailExportContainer.defaultProps = {
  dealerInfo: EMPTY_OBJECT,
  localLookup: EMPTY_OBJECT,
  arSetup: EMPTY_OBJECT,
  getArAgingReportList: defaultGetArAgingReport,
  getInvoiceDetailsForAgingReport: defaultGetInvoiceDetailsForAgingReport,
  accessibleEnterpriseWorkspaceIds: EMPTY_ARRAY,
  isPreviewPage: false,
  onPrintTypstPdf: _noop,
  onPrintPdf: _noop,
  typstSupportedReportSet: EMPTY_SET,
  assetId: AR_AGING_REPORT_PDF_ASSET_ID,
  isCentralPreview: false,
  shouldShowBusinessUnitColumn: false,
  dealersById: EMPTY_OBJECT,
  accountsMapByDealerId: EMPTY_OBJECT,
  journalsMapByDealerId: EMPTY_OBJECT,
  dealerDetailsById: EMPTY_OBJECT,
};

const mapStateToProps = state => ({
  dealerInfo: getDealerInfo(state),
  localLookup: getLocalLookup(state),
  arSetup: getArSetup(state),
  accessibleEnterpriseWorkspaceIds: getAccessibleEnterpriseWorkspaceIds(state),
  typstSupportedReportSet: getTypstSupportedReportSet(state),
  dealersById: getTenantDealersById(state),
  journalsMapByDealerId: getAllJournalsMapByDealerId(state),
  accountsMapByDealerId: getAllAccountsMapByDealerId(state),
  dealerDetailsById: getExternalDealerDetailsByWorkspaceId(state),
});

export default compose(withRouter, withTekionConversion, connect(mapStateToProps))(ARAgingDetailExportContainer);
