// Utils
import addIdsAndKeysToColumnConfig from 'utils/addIdsAndKeysToColumnConfig';

// constants
import { AUTO } from 'twidgets/organisms/pdfGenerator';
import COLUMN_IDS from 'constants/arInvoiceList.columnIds';

export const HEADER_CONFIG = {
  tableHeaderContainerStyles: 'p-y-20',
};

export const DETAIL_COLUMN_CONFIG_BY_KEY = addIdsAndKeysToColumnConfig({
  [COLUMN_IDS.INVOICE_NUMBER]: {
    displayName: __('Invoice Number'),
    enabled: true,
    order: 0,
    printWidth: [AUTO.id],
  },
  [COLUMN_IDS.INVOICE_DATE]: {
    displayName: __('Invoice Date'),
    enabled: true,
    order: 1,
    printWidth: [AUTO.id],
  },
  [COLUMN_IDS.ORIGINAL_AMOUNT]: {
    className: 'tk-table-cell-right-align',
    displayName: __('Invoice Amount'),
    enabled: true,
    order: 2,
    printWidth: [AUTO.id],
  },
  [COLUMN_IDS.DUE_AMOUNT]: {
    className: 'tk-table-cell-right-align',
    displayName: __('Due Amount'),
    enabled: true,
    order: 3,
    printWidth: [AUTO.id],
  },
  [COLUMN_IDS.RECEIVED_AMOUNT]: {
    className: 'tk-table-cell-right-align',
    displayName: __('Received Amount'),
    enabled: true,
    order: 4,
    printWidth: [AUTO.id],
  },
  [COLUMN_IDS.DUE_DATE]: {
    displayName: __('Due Date'),
    enabled: true,
    order: 5,
    printWidth: [AUTO.id],
  },
  [COLUMN_IDS.INVOICE_AGE]: {
    displayName: __('Invoice Age (Days)'),
    enabled: true,
    order: 6,
    printWidth: [AUTO.id],
  },
  [COLUMN_IDS.AR_GL_ACCOUNT]: {
    displayName: __('AR GL Account'),
    enabled: true,
    order: 7,
    printWidth: [AUTO.id],
  },
  [COLUMN_IDS.LAST_RECEIPT_DATE]: {
    displayName: __('Last Receipt Date'),
    enabled: true,
    order: 8,
    printWidth: [AUTO.id],
  },
  [COLUMN_IDS.JOURNAL]: {
    displayName: __('Journal'),
    enabled: true,
    order: 9,
    printWidth: [AUTO.id],
  },
  [COLUMN_IDS.INVOICE_TYPE]: {
    displayName: __('Invoice Type'),
    enabled: true,
    order: 10,
    printWidth: [AUTO.id],
  },
});
