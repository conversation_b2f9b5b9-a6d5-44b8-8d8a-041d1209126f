/* eslint-disable import/order */
import _noop from 'lodash/noop';

// Constants
import COLUMN_IDS from 'constants/arInvoiceList.columnIds';
import SUMMARY_COLUMN_IDS from '../../../../../constants/arAgingReport.listColumnIds';

// Readers
import customerInvoiceReader from 'readers/CustomerInvoice';

// Utils and Formatters
import readAndGetCommaSeparatedValues from 'utils/readAndGetCommaSeparatedValues';
import getClassNameForDeletedCustomerData from 'utils/getClassNameForDeletedCustomerData';
import withDeletedCustomerDataCheck from 'utils/withDeletedCustomerDataCheck';

// helpers
import { getSummaryColumnConfigOverrides } from '../../../summaryView';
import {
  getFormattedDueAmount,
  getFormattedOriginalAmount,
  getFormattedReceivedAmount,
  getFormattedInvoiceAgeWithSelectedMonth,
} from 'helpers/customerInvoice/arInvoiceList.agingColumnAccessors';
import { getInvoiceType } from 'helpers/customerInvoice/arInvoiceList.baseColumnAccessors';
import {
  getJournalNumbers,
  getSortedArGlAccountNumbers,
} from '../../../../../helpers/exports/arAgingReportExport.accessors';
import {
  getCustomerNameFromData,
  getCustomerPhoneNumber,
  getCustomerNumber,
  getCustomerEmail,
} from '../../../../../helpers/arAgingReport.columns';

const getDetailColumnConfigOverrides = (getFormattedCurrency, getFormattedNumber, selectedMonth) => ({
  [COLUMN_IDS.INVOICE_NUMBER]: {
    accessor: customerInvoiceReader.invoiceNumber,
  },
  [COLUMN_IDS.INVOICE_TYPE]: {
    accessor: getInvoiceType,
  },
  [COLUMN_IDS.ORIGINAL_AMOUNT]: {
    accessor: getFormattedOriginalAmount(getFormattedCurrency),
    className: 'tk-table-cell-right-align',
  },
  [COLUMN_IDS.DUE_AMOUNT]: {
    accessor: getFormattedDueAmount(getFormattedCurrency),
    className: 'tk-table-cell-right-align',
  },
  [COLUMN_IDS.RECEIVED_AMOUNT]: {
    accessor: getFormattedReceivedAmount(getFormattedCurrency),
    className: 'tk-table-cell-right-align',
  },
  [COLUMN_IDS.INVOICE_AGE]: {
    accessor: getFormattedInvoiceAgeWithSelectedMonth(getFormattedNumber, selectedMonth),
  },
  [COLUMN_IDS.AR_GL_ACCOUNT]: {
    accessor: readAndGetCommaSeparatedValues(getSortedArGlAccountNumbers),
  },
  [COLUMN_IDS.JOURNAL]: {
    accessor: readAndGetCommaSeparatedValues(getJournalNumbers),
  },
});

const getAggregateSummaryColumnConfigOverrides = (getFormattedCurrency, getFormattedPhoneNumber) => ({
  ...getSummaryColumnConfigOverrides(getFormattedCurrency, getFormattedPhoneNumber),
  [SUMMARY_COLUMN_IDS.CUSTOMER_NAME_COLUMN]: {
    accessor: withDeletedCustomerDataCheck(getCustomerNameFromData),
    getClassName: _noop,
    getAggregateCellValueClassName: getClassNameForDeletedCustomerData,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_NUMBER_COLUMN]: {
    accessor: withDeletedCustomerDataCheck(getCustomerNumber),
    getClassName: _noop,
    getAggregateCellValueClassName: getClassNameForDeletedCustomerData,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS]: {
    accessor: withDeletedCustomerDataCheck(getCustomerEmail),
    getClassName: _noop,
    getAggregateCellValueClassName: getClassNameForDeletedCustomerData,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_PHONE_NUMBER]: {
    accessor: withDeletedCustomerDataCheck(getCustomerPhoneNumber(getFormattedPhoneNumber)),
    getClassName: _noop,
    getAggregateCellValueClassName: getClassNameForDeletedCustomerData,
  },
});

export const getColumnConfig = (getFormattedCurrency, getFormattedNumber, getFormattedPhoneNumber, selectedMonth) => ({
  ...getAggregateSummaryColumnConfigOverrides(getFormattedCurrency, getFormattedPhoneNumber),
  ...getDetailColumnConfigOverrides(getFormattedCurrency, getFormattedNumber, selectedMonth),
});
