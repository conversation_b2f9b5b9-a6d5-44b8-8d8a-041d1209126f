import _reduce from 'lodash/reduce';
import _map from 'lodash/map';
import _groupBy from 'lodash/groupBy';

// Constants
import { PdfTableRow, ROW_TYPES, makePdfRowsAlternateColors } from 'twidgets/organisms/pdfGenerator';
import COLUMN_IDS from 'constants/arInvoiceList.columnIds';

// Utils
import makeFormattedDateAccessor from 'tbusiness/utils/makeFormattedDateAccessor';

// Readers
import customerInvoiceReader from 'readers/CustomerInvoice';
import arAgingReportReader from 'readers/ArAgingReport';

import styles from '../baseArAgingDetailExport.module.scss';

const makeInvoicePdfRow = (invoice, rowIndex) =>
  new PdfTableRow()
    .setRowInfo(invoice)
    .setRowType(ROW_TYPES.SIMPLE_ROW)
    .setRowStyles(makePdfRowsAlternateColors(rowIndex));

const makeTableRows = invoicesByCustomerId => (tableRows, arAgingReport) => {
  const customerId = arAgingReportReader.customerId(arAgingReport);
  const agingSummaryRow = new PdfTableRow()
    .setRowInfo(arAgingReport)
    .setRowType(ROW_TYPES.GROUPED_AGGREGATION_ROW)
    .setRowStyles(styles.groupedRowContainer);
  const invoices = invoicesByCustomerId[customerId];
  const invoiceRows = _map(invoices, makeInvoicePdfRow);
  // eslint-disable-next-line rulesdir/no-array-functions
  tableRows.push(agingSummaryRow, ...invoiceRows);
  return tableRows;
};

export const makeTableData = (arAgingReportList, arInvoiceList) => {
  const invoicesByCustomerId = _groupBy(arInvoiceList, customerInvoiceReader.customerId);
  const tableData = _reduce(arAgingReportList, makeTableRows(invoicesByCustomerId), []);
  return tableData;
};

export const getUpdatedColumnConfig = (columnConfig, getFormattedDateAndTime) => ({
  ...columnConfig,
  [COLUMN_IDS.INVOICE_DATE]: {
    accessor: makeFormattedDateAccessor(customerInvoiceReader.invoiceDate, undefined, getFormattedDateAndTime),
  },
  [COLUMN_IDS.DUE_DATE]: {
    accessor: makeFormattedDateAccessor(customerInvoiceReader.dueDate, undefined, getFormattedDateAndTime),
  },
  [COLUMN_IDS.LAST_RECEIPT_DATE]: {
    accessor: makeFormattedDateAccessor(customerInvoiceReader.lastReceiptDate, undefined, getFormattedDateAndTime),
  },
});
