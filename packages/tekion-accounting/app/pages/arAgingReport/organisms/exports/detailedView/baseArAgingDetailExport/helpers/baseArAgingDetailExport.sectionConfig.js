// Lodash
import _values from 'lodash/values';

// Constants
import SUMMARY_COLUMN_CONFIG_BY_KEY from '../../../../../constants/exports/arAgingReportExport.summaryPreviewColumnConfig';
import {
  AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID,
  AR_AGING_REPORT_DETAIL_COLUMN_CONFIGURATION_ID,
  AR_AGING_SUMMARY_SECTION_CONFIG_TITLE,
} from '../../../../../constants/exports/arAgingReportExport.general';
import { AR_AGING_DETAIL_SECTION_CONFIG_TITLE } from '../constants/baseArAgingDetailExport.general';
import { DETAIL_COLUMN_CONFIG_BY_KEY } from '../constants/baseArAgingDetailExport.sectionConfiguration';

// Helpers
import { getFilteredArAgingSummaryColumns } from '../../../../../helpers/exports/arAgingReportExport.general';

const getSectionConfig = featureFlags => {
  const filteredArAgingSummaryColumns = getFilteredArAgingSummaryColumns(
    _values(SUMMARY_COLUMN_CONFIG_BY_KEY),
    featureFlags
  );
  return [
    {
      title: AR_AGING_SUMMARY_SECTION_CONFIG_TITLE,
      id: AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID,
      columns: filteredArAgingSummaryColumns,
    },
    {
      title: AR_AGING_DETAIL_SECTION_CONFIG_TITLE,
      id: AR_AGING_REPORT_DETAIL_COLUMN_CONFIGURATION_ID,
      columns: _values(DETAIL_COLUMN_CONFIG_BY_KEY),
    },
  ];
};

export default getSectionConfig;
