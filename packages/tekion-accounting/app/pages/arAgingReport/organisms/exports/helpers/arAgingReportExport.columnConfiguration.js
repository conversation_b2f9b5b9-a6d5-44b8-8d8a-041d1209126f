/* eslint-disable import/order */
// Lodash
import _isEmpty from 'lodash/isEmpty';
import _keyBy from 'lodash/keyBy';

// Utils
import makeFormattedDateAccessor from 'tbusiness/utils/makeFormattedDateAccessor';
import { updateListItems } from 'utils/list';

// Readers
import customerInvoiceReader from 'readers/CustomerInvoice';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import DETAIL_COLUMN_IDS from 'constants/arInvoiceList.columnIds';
import {
  AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID,
  AR_AGING_REPORT_DETAIL_COLUMN_CONFIGURATION_ID,
  AR_AGING_SUMMARY_SECTION_CONFIG_TITLE,
  COLUMN_TYPES,
} from '../../../constants/exports/arAgingReportExport.general';

// Helpers
import { createColumnsFromPdfConfiguration } from 'twidgets/organisms/pdfGenerator';
import {
  getArPDFColumns,
  getFilteredArAgingSummaryColumns,
} from '../../../helpers/exports/arAgingReportExport.general';
import {
  getArAgingDetailColumnFromConfigurations,
  getArAgingSummaryColumnFromConfigurations,
  getCentralArAgingDetailReportColumnConfigurations,
} from '../../../helpers/exports/arAgingReportExport.centralColumnConfiguration';
import { getColumnKey } from 'helpers/columnHelper/getColumnProperties';

const makeArAgingColumns = ({
  columnsType,
  arAgingReportColumnConfigurations,
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber,
  selectedMonth,
}) => {
  let columns = getArPDFColumns({
    columnsType,
    getFormattedCurrency,
    getFormattedNumber,
    getFormattedPhoneNumber,
    selectedMonth,
  });
  if (!_isEmpty(arAgingReportColumnConfigurations)) {
    const defaultColumnsById = _keyBy(columns, 'id');
    columns = createColumnsFromPdfConfiguration(defaultColumnsById, arAgingReportColumnConfigurations);
  }
  return columns;
};

export const getArAgingSummaryReportColumnConfigurations = (
  pdfConfiguration = EMPTY_OBJECT,
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber,
  featureFlags
) => {
  const { columnConfigurations } = pdfConfiguration;
  const arAgingSummaryColumnConfiguration = getArAgingSummaryColumnFromConfigurations(columnConfigurations);
  const arAgingSummaryColumns = makeArAgingColumns({
    columnsType: COLUMN_TYPES.SUMMARY,
    arAgingReportColumnConfigurations: arAgingSummaryColumnConfiguration,
    getFormattedCurrency,
    getFormattedNumber,
    getFormattedPhoneNumber,
    featureFlags,
  });

  const filteredArAgingSummaryColumns = getFilteredArAgingSummaryColumns(arAgingSummaryColumns, featureFlags);
  return [
    {
      ...arAgingSummaryColumnConfiguration,
      id: AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID,
      title: AR_AGING_SUMMARY_SECTION_CONFIG_TITLE,
      columns: filteredArAgingSummaryColumns,
    },
  ];
};

const getUpdatedArAgingUpdatedColumns = (arAgingDetailColumns, getFormattedDateAndTime) => {
  const columnsToUpdate = {
    [DETAIL_COLUMN_IDS.INVOICE_DATE]: {
      accessor: makeFormattedDateAccessor(customerInvoiceReader.invoiceDate, undefined, getFormattedDateAndTime),
    },
    [DETAIL_COLUMN_IDS.DUE_DATE]: {
      accessor: makeFormattedDateAccessor(customerInvoiceReader.dueDate, undefined, getFormattedDateAndTime),
    },
    [DETAIL_COLUMN_IDS.LAST_RECEIPT_DATE]: {
      accessor: makeFormattedDateAccessor(customerInvoiceReader.lastReceiptDate, undefined, getFormattedDateAndTime),
    },
  };
  return updateListItems(arAgingDetailColumns, columnsToUpdate, getColumnKey);
};

export const getArAgingDetailReportColumnConfiguratons = (
  pdfConfiguration = EMPTY_OBJECT,
  getFormattedDateAndTime,
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber,
  featureFlags,
  selectedMonthEndTime
) => {
  const { columnConfigurations } = pdfConfiguration;
  const arAgingColumnConfigurations = getArAgingSummaryReportColumnConfigurations(
    pdfConfiguration,
    getFormattedCurrency,
    getFormattedNumber,
    getFormattedPhoneNumber,
    featureFlags
  );
  const arAgingDetailColumnConfiguration = getArAgingDetailColumnFromConfigurations(columnConfigurations);
  const arAgingDetailColumns = makeArAgingColumns({
    columnsType: COLUMN_TYPES.DETAIL,
    arAgingReportColumnConfigurations: arAgingDetailColumnConfiguration,
    getFormattedCurrency,
    getFormattedNumber,
    selectedMonth: selectedMonthEndTime,
  });

  const updatedArAgingDetailColumns = getUpdatedArAgingUpdatedColumns(arAgingDetailColumns, getFormattedDateAndTime);

  arAgingColumnConfigurations.push({
    ...arAgingDetailColumnConfiguration,
    id: AR_AGING_REPORT_DETAIL_COLUMN_CONFIGURATION_ID,
    title: __('Detail Columns'),
    columns: updatedArAgingDetailColumns,
  });
  return arAgingColumnConfigurations;
};

export const getArAgingDetailReportColumnConfigurationsByDealerType = (
  pdfConfiguration,
  getFormattedDateAndTime,
  getFormattedCurrency,
  getFormattedNumber,
  getFormattedPhoneNumber,
  featureFlags,
  selectedMonthEndTime,
  isCentralPreview,
  dealersById,
  journalsMapByDealerId,
  accountsMapByDealerId,
  dealerDetailsById,
  shouldShowBusinessUnitColumn
) => {
  if (isCentralPreview) {
    return getCentralArAgingDetailReportColumnConfigurations(
      pdfConfiguration,
      dealersById,
      journalsMapByDealerId,
      accountsMapByDealerId,
      getFormattedDateAndTime,
      getFormattedCurrency,
      getFormattedNumber,
      getFormattedPhoneNumber,
      selectedMonthEndTime,
      shouldShowBusinessUnitColumn,
      dealerDetailsById,
      featureFlags
    );
  }
  return getArAgingDetailReportColumnConfiguratons(
    pdfConfiguration,
    getFormattedDateAndTime,
    getFormattedCurrency,
    getFormattedNumber,
    getFormattedPhoneNumber,
    featureFlags,
    selectedMonthEndTime
  );
};
