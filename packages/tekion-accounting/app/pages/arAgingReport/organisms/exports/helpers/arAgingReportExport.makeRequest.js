// Components
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';

// Builders
import PdfBulkExportRequest from 'builders/PdfBulkExportRequest';
import PdfExportRequest from 'builders/PdfExportRequest';

// utils
import toastAPIError from 'utils/toastAPIError';

// constants
import { exportReportAsPdf } from 'tbusiness/appServices/accounting/services/pdfExportService';
import {
  AR_AGING_SUMMARY_PREVIEW_ASSET_TYPE,
  AR_AGING_SUMMARY_DOCUMENT_TYPE,
} from '../../../constants/exports/arAgingReportExport.preview';

// Helpers
import { getPayloadForPdf } from '../../../helpers/arAgingReport.makeExportRequest';

export const makePdfPreviewExportRequestDTO = (urlParams, assetType, documentType) => {
  const pdfExportRequestParams = new PdfBulkExportRequest().setUrlParams(urlParams).setDocumentType(documentType);
  const request = new PdfExportRequest().setAssetType(assetType).setPdfExportRequestParams(pdfExportRequestParams);
  return request;
};

const handlePrintPdfSuccess = () => toaster(TOASTER_TYPE.SUCCESS, __('PDF generation started'));

const handlePrintPdfFailure = error => {
  toastAPIError(error, __('Failed to generate PDF'));
};

export const handlePrintPDF = (urlParams, assetType, documentType) => {
  const request = makePdfPreviewExportRequestDTO(urlParams, assetType, documentType);
  return exportReportAsPdf(request).then(handlePrintPdfSuccess).catch(handlePrintPdfFailure);
};

export const onPrintSummaryPdf = location => {
  const urlParams = {
    queryString: location.search,
  };
  return handlePrintPDF(urlParams, AR_AGING_SUMMARY_PREVIEW_ASSET_TYPE, AR_AGING_SUMMARY_DOCUMENT_TYPE);
};

export const onPrintSummaryTypstPdf = location => {
  const { urlParams } = getPayloadForPdf(location, AR_AGING_SUMMARY_DOCUMENT_TYPE);
  return handlePrintPDF(urlParams, AR_AGING_SUMMARY_PREVIEW_ASSET_TYPE, AR_AGING_SUMMARY_DOCUMENT_TYPE);
};
