import React from 'react';
import { compose } from 'recompose';

// Lodash
import _find from 'lodash/find';
import _property from 'lodash/property';
import _head from 'lodash/head';
import _isNil from 'lodash/isNil';
import _constant from 'lodash/constant';
import _truncate from 'lodash/truncate';
import _reduce from 'lodash/reduce';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _groupBy from 'lodash/groupBy';
import _includes from 'lodash/includes';

// Utils
import isCollectionEmpty from 'tbase/utils/isCollectionEmpty';

// Readers
import arAgingReportReader from 'readers/ArAgingReport';
import customerInvoiceReader from 'readers/CustomerInvoice';

// components
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';

// Components
import SummaryItem from 'pages/export/components/summaryItem';

// Constants && Readers
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { AUTO, columnConfigReader, getInitialPdfConfiguration } from 'twidgets/organisms/pdfGenerator';
import {
  DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION,
  AR_AGING_SUMMARY_DATA_KEY,
  HIDDEN_COLUMN_STYLE,
  CUSTOMER_PERSONAL_INFO_COLUMN_IDS,
} from '../../../constants/exports/arAgingReportExport.general';
import SUMMARY_COLUMN_IDS from '../../../constants/arAgingReport.listColumnIds';

// Helpers
import {
  getArAgingSummaryColumnFromConfigurations,
  getArAgingDetailColumnFromConfigurations,
} from '../../../helpers/exports/arAgingReportExport.centralColumnConfiguration';
import { getHiddenColumnId } from '../../../helpers/exports/arAgingReportExport.sort';
import { getIsCustomerDeletedInRow } from '../../../helpers/arAgingReport.general';

const addArAgingSummaryDataToArInvoice = arAgingSummaryData => arInvoice => ({
  ...arInvoice,
  [AR_AGING_SUMMARY_DATA_KEY]: arAgingSummaryData,
});

const addArAgingSummaryDataToArInvoices = (arInvoices, arAgingSummaryData) =>
  _map(arInvoices, addArAgingSummaryDataToArInvoice(arAgingSummaryData));

const createArAgingReportDetailDataFromSummary =
  (arInvoicesPerCustomer = EMPTY_OBJECT) =>
  (arAgingReportDetailsList, arAgingSummaryData = EMPTY_OBJECT) => {
    const customerId = arAgingReportReader.customerId(arAgingSummaryData);
    const arInvoices = arInvoicesPerCustomer[customerId];

    if (arInvoices) {
      const arInvoicesWithAgingSummaryData = addArAgingSummaryDataToArInvoices(arInvoices, arAgingSummaryData);
      arAgingReportDetailsList.push(...arInvoicesWithAgingSummaryData);
    }
    return arAgingReportDetailsList;
  };

export const createArAgingReportDetailData = (arInvoiceList, arAgingReportList) =>
  _reduce(
    arAgingReportList,
    createArAgingReportDetailDataFromSummary(_groupBy(arInvoiceList, customerInvoiceReader.customerId)),
    []
  );

const getArAgingSummaryDataFromInvoiceList = compose(
  _property(AR_AGING_SUMMARY_DATA_KEY),
  _property('_original'),
  _head
);

const getArAgingSummaryValueToDisplay = (column, arAgingSummaryData) => {
  const accessor = columnConfigReader.accessor(column);
  const value = accessor(arAgingSummaryData);
  if (_isNil(value)) {
    return undefined;
  }
  const printWidth = columnConfigReader.printWidth(column);
  const printWidthValue = _head(printWidth);
  const characters = columnConfigReader.characters(column);
  return printWidthValue === AUTO.id ? value : _truncate(value, { length: characters, omission: '' });
};

const addArAgingValueToSummary =
  arAgingSummaryData =>
  (summary, column = EMPTY_OBJECT) => {
    const checked = columnConfigReader.checked(column);
    const configurationDisabled = !!columnConfigReader.configurationDisabled(column);
    if (configurationDisabled || !checked) {
      return summary;
    }
    const header = columnConfigReader.header(column);
    const columnId = columnConfigReader.id(column);
    const value = getArAgingSummaryValueToDisplay(column, arAgingSummaryData);
    const isCustomerDeleted = getIsCustomerDeletedInRow(arAgingSummaryData);
    const shouldShowWarningIcon = _includes(CUSTOMER_PERSONAL_INFO_COLUMN_IDS, columnId);
    summary.push({
      label: header,
      value,
      isCustomerDeleted: shouldShowWarningIcon && isCustomerDeleted,
    });
    return summary;
  };

export const createArAgingSummary = (arAgingSummaryColumns, arAgingSummaryData) =>
  _reduce(arAgingSummaryColumns, addArAgingValueToSummary(arAgingSummaryData), []);

const getArAgingSummary = arAgingSummaryColumns => (values, rows) => {
  const arAgingSummaryData = getArAgingSummaryDataFromInvoiceList(rows);
  const arAgingSummary = createArAgingSummary(arAgingSummaryColumns, arAgingSummaryData);
  return arAgingSummary;
};

const createSummaryItem = ({ label, value, isCustomerDeleted }) => (
  <SummaryItem label={label} value={value} isCustomerDeleted={isCustomerDeleted} tooltipVisible={false} />
);

const getAggregatedCell = ({ value: summary }) => _map(summary, createSummaryItem);

const getFirstVisibleColumnId = columns => {
  const visibleColumn = _find(columns, { checked: true }) || EMPTY_OBJECT;
  const { id } = visibleColumn;
  return id;
};

const addAggregateValuesToColumn = (arAgingSummaryColumns, firstVisibleColumnId) => column => ({
  ...column,
  aggregate: column.id === firstVisibleColumnId ? getArAgingSummary(arAgingSummaryColumns) : _constant(''),
  Aggregated: getAggregatedCell,
});

const addAggregateToInvoiceListColumns = (arAgingDetailColumns, arAgingSummaryColumns) => {
  const firstVisibleColumnId = getFirstVisibleColumnId(arAgingDetailColumns);
  return _map(arAgingDetailColumns, addAggregateValuesToColumn(arAgingSummaryColumns, firstVisibleColumnId));
};

const getColumnConfig = (columnKey, arAgingSummaryColumns) =>
  _find(arAgingSummaryColumns, column => column.key === columnKey) || EMPTY_OBJECT;

const getColumnAccessorForSummaryData = accessor => compose(accessor, _property(AR_AGING_SUMMARY_DATA_KEY));

const appendCustomerDeletionProps = getProps => (rowData, values) => {
  const rows = _get(values, 'subRows');
  const arAgingSummaryData = getArAgingSummaryDataFromInvoiceList(rows);
  const isCustomerDeleted = getIsCustomerDeletedInRow(arAgingSummaryData);
  return { ...getProps(), isCustomerDeleted };
};

/**
 * Customer columns from AR Aging Report columns are being merged into posting columns to achieve the desired
 * view/design of the template. Hence this function modifies the column accessor to resolve the data accurately.
 */
export const getSummaryColumnForArAgingDetails = (columnKey, arAgingSummaryColumns) => {
  const column = getColumnConfig(columnKey, arAgingSummaryColumns);
  const { accessor, Cell, getProps } = column;
  return {
    ...column,
    checked: true,
    sortable: false,
    accessor: getColumnAccessorForSummaryData(accessor),
    Pivot: Cell,
    aggregate: getAggregateCustomerColumnAccessor(accessor),
    getProps: appendCustomerDeletionProps(getProps),
  };
};

const getAggregateCustomerColumnAccessor = accessor => (values, rows) => {
  const arInvoiceDetailRow = _head(rows);
  const arInvoiceDetailData = _get(arInvoiceDetailRow, '_original');
  return getColumnAccessorForSummaryData(accessor)(arInvoiceDetailData);
};

export const getHiddenColumnForSort = (keyToSort, arAgingSummaryColumns) => {
  const sortColumnConfig = getColumnConfig(keyToSort, arAgingSummaryColumns);
  const sortAccessor = columnConfigReader.sortAccessor(sortColumnConfig);
  const hiddenColumnId = getHiddenColumnId(keyToSort);
  return {
    ...sortColumnConfig,
    id: hiddenColumnId,
    key: hiddenColumnId,
    aggregate: getAggregateCustomerColumnAccessor(sortAccessor),
    headerStyle: HIDDEN_COLUMN_STYLE,
    style: HIDDEN_COLUMN_STYLE,
  };
};

export const getArAgingReportDetailColumns = (pdfColumnConfigurations = EMPTY_ARRAY, keyToSort = '') => {
  const arAgingDetailColumnConfiguration = getArAgingDetailColumnFromConfigurations(pdfColumnConfigurations);
  const { columns: arAgingDetailColumns } = arAgingDetailColumnConfiguration;

  const arAgingSummaryColumnConfiguration = getArAgingSummaryColumnFromConfigurations(pdfColumnConfigurations);
  const { columns: arAgingSummaryColumns } = arAgingSummaryColumnConfiguration;

  const arAgingDetailColumnsWithAggregate = addAggregateToInvoiceListColumns(
    arAgingDetailColumns,
    arAgingSummaryColumns
  );
  const customerNameColumn = {
    ...getSummaryColumnForArAgingDetails(SUMMARY_COLUMN_IDS.CUSTOMER_NAME_COLUMN, arAgingSummaryColumns),
    accessor: _constant(''),
  };
  const customerNumberColumn = getSummaryColumnForArAgingDetails(
    SUMMARY_COLUMN_IDS.CUSTOMER_NUMBER_COLUMN,
    arAgingSummaryColumns
  );
  const hiddenColumnForSort = getHiddenColumnForSort(keyToSort, arAgingSummaryColumns);
  return [customerNumberColumn, customerNameColumn, ...arAgingDetailColumnsWithAggregate, hiddenColumnForSort];
};

export const getPdfConfiguration = (pdfConfigurationResponse, columnConfigurations) => ({
  columnConfigurations,
  printConfiguration: !isCollectionEmpty(pdfConfigurationResponse)
    ? pdfConfigurationResponse
    : DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION,
});

export const loadPdfConfiguration = async ({ assetType, assetId, showTypstPdf, setState }) => {
  if (showTypstPdf) {
    try {
      const pdfConfiguration = await getInitialPdfConfiguration(assetType, assetId);
      setState({ pdfConfiguration });
    } catch {
      toaster(TOASTER_TYPE.ERROR, __('Failed to fetch PDF configuration'));
    }
  }

  setState({ isLoading: false });
};
