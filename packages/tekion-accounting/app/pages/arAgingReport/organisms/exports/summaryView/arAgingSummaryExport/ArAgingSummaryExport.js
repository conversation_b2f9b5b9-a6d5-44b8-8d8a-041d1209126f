/* eslint-disable import/order */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import { connect } from 'react-redux';
import compose from 'recompose/compose';

// Components
import Loader from 'tcomponents/molecules/loader';
import BaseArAgingReportExport from '../baseArAgingSummaryExport';

// Constants
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { AR_AGING_REPORT_PDF_ASSET_ID } from '../../../../constants/exports/arAgingReportExport.general';

// Selectors
import { getAccessibleEnterpriseWorkspaceIds } from 'reducers/selectors/appCache/accessibleEnterpriseWorkspaceDetails';

// Helpers
import fetchArAgingReportData from '../helpers/summaryView.helpers';
import { getEndTimeFromMonthFilter } from '../../../../helpers/exports/arAgingReportExport.general';

// Utils
import getParamsFromQueryString from 'tbusiness/appServices/accounting/utils/getParamsFromQueryString';

// Connectors
import { withTekionConversion } from '@tekion/tekion-conversion-web';
import withRouter from 'tcomponents/hoc/withRouter';

class ArAgingSummaryExport extends Component {
  state = {
    loading: true,
    arAgingReportList: EMPTY_ARRAY,
    arSummary: EMPTY_OBJECT,
    sortDetails: EMPTY_OBJECT,
  };

  getParamsFromQueryString = defaultMemoize(getParamsFromQueryString);

  getEndTimeFromMonthFilter = defaultMemoize(getEndTimeFromMonthFilter);

  fetchArAgingReportData = defaultMemoize(fetchArAgingReportData);

  componentDidMount() {
    this.loadInitialAgingData();
  }

  getSelectedMonthEndTime() {
    const { location } = this.props;
    const { filters } = this.getParamsFromQueryString(location.search);
    const selectedMonthEndTime = this.getEndTimeFromMonthFilter(filters);
    return selectedMonthEndTime;
  }

  setAsLoaded = () => this.setState({ loading: false });

  saveArAgingReportData = ({ arAgingReportList, arSummary, sortDetails } = EMPTY_OBJECT) => {
    this.setState({
      arAgingReportList,
      arSummary,
      sortDetails,
    });
  };

  loadInitialAgingData() {
    const { location, accessibleEnterpriseWorkspaceIds } = this.props;
    this.fetchArAgingReportData(location, accessibleEnterpriseWorkspaceIds)
      .then(this.saveArAgingReportData)
      .finally(this.setAsLoaded);
  }

  render() {
    const { loading, arAgingReportList, arSummary, sortDetails } = this.state;
    const {
      getFormattedDateAndTime,
      getFormattedCurrency,
      getFormattedPhoneNumber,
      getFormattedNumber,
      location,
      assetId,
    } = this.props;
    const selectedMonthEndTime = this.getSelectedMonthEndTime();
    if (loading) {
      return <Loader />;
    }
    return (
      <BaseArAgingReportExport
        arAgingData={arAgingReportList}
        arSummary={arSummary}
        selectedMonthEndTime={selectedMonthEndTime}
        sortDetails={sortDetails}
        getFormattedDateAndTime={getFormattedDateAndTime}
        getFormattedCurrency={getFormattedCurrency}
        getFormattedPhoneNumber={getFormattedPhoneNumber}
        getFormattedNumber={getFormattedNumber}
        location={location}
        assetId={assetId}
      />
    );
  }
}

ArAgingSummaryExport.propTypes = {
  getFormattedDateAndTime: PropTypes.func.isRequired,
  getFormattedCurrency: PropTypes.func.isRequired,
  getFormattedPhoneNumber: PropTypes.func.isRequired,
  getFormattedNumber: PropTypes.func.isRequired,
  accessibleEnterpriseWorkspaceIds: PropTypes.array,
  location: PropTypes.object.isRequired,
  assetId: PropTypes.string,
};

ArAgingSummaryExport.defaultProps = {
  accessibleEnterpriseWorkspaceIds: EMPTY_ARRAY,
  assetId: AR_AGING_REPORT_PDF_ASSET_ID,
};

const mapStateToProps = state => ({
  accessibleEnterpriseWorkspaceIds: getAccessibleEnterpriseWorkspaceIds(state),
});

export default compose(withRouter, withTekionConversion, connect(mapStateToProps))(ArAgingSummaryExport);
