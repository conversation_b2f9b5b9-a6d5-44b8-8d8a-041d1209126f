/* eslint-disable import/order */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import { connect } from 'react-redux';
import compose from 'recompose/compose';

// Components
import Loader from 'tcomponents/molecules/loader';
import BaseArAgingReportExport from '../baseArAgingSummaryExport';

// Selectors
import { getAccessibleEnterpriseWorkspaceIds } from 'reducers/selectors/appCache/accessibleEnterpriseWorkspaceDetails';

// Constants
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { INITIAL_STATE } from './constants/arAgingSummaryExportPreview.general';
import { AR_AGING_REPORT_PDF_ASSET_ID } from '../../../../constants/exports/arAgingReportExport.general';

// Helpers
import getTrimmedArAgingReportList, {
  onPrintNonTypstSummaryPdf,
  onPrintTypstSummaryPdf,
} from './helpers/arAgingSummaryExportPreview.data';
import fetchArAgingReportData from '../helpers/summaryView.helpers';
import { getEndTimeFromMonthFilter } from '../../../../helpers/exports/arAgingReportExport.general';

// Utils
import getParamsFromQueryString from 'tbusiness/appServices/accounting/utils/getParamsFromQueryString';

// Factories
import getFilename, { EXPORT_VIEWS } from 'factories/filenameFactory';

// Connectors
import { withTekionConversion } from '@tekion/tekion-conversion-web';
import withRouter from 'tcomponents/hoc/withRouter';

class ArAgingSummaryExportPreview extends Component {
  state = INITIAL_STATE;

  getParamsFromQueryString = defaultMemoize(getParamsFromQueryString);

  getEndTimeFromMonthFilter = defaultMemoize(getEndTimeFromMonthFilter);

  getTrimmedArAgingReportList = defaultMemoize(getTrimmedArAgingReportList);

  fetchArAgingReportData = defaultMemoize(fetchArAgingReportData);

  onPrintNonTypstSummaryPdf = defaultMemoize(onPrintNonTypstSummaryPdf);

  onPrintTypstSummaryPdf = defaultMemoize(onPrintTypstSummaryPdf);

  componentDidMount() {
    this.setTitle();
    this.loadInitialAgingData();
  }

  getSelectedMonthEndTime() {
    const { location } = this.props;
    const { filters } = this.getParamsFromQueryString(location.search);
    const selectedMonthEndTime = this.getEndTimeFromMonthFilter(filters);
    return selectedMonthEndTime;
  }

  setTitle() {
    const { getFormattedDateAndTime } = this.props;
    const selectedMonthEndTime = this.getSelectedMonthEndTime();
    const title = getFilename(EXPORT_VIEWS.AR_AGING, { untilTime: selectedMonthEndTime, getFormattedDateAndTime });
    document.title = title;
  }

  setAsLoaded = () => this.setState({ loading: false });

  saveArAgingReportData = ({ arAgingReportList, arSummary, sortDetails } = EMPTY_OBJECT) => {
    this.setState({
      arAgingReportList,
      arSummary,
      sortDetails,
    });
  };

  loadInitialAgingData() {
    const { location, accessibleEnterpriseWorkspaceIds } = this.props;
    this.fetchArAgingReportData(location, accessibleEnterpriseWorkspaceIds)
      .then(this.saveArAgingReportData)
      .finally(this.setAsLoaded);
  }

  render() {
    const { loading, arAgingReportList, arSummary, sortDetails } = this.state;
    const {
      getFormattedDateAndTime,
      getFormattedCurrency,
      getFormattedPhoneNumber,
      getFormattedNumber,
      location,
      assetId,
    } = this.props;
    const selectedMonthEndTime = this.getSelectedMonthEndTime();
    const trimmedArAgingReportList = this.getTrimmedArAgingReportList(arAgingReportList);
    const onPrintNonTypstSummaryPdf = this.onPrintNonTypstSummaryPdf(location);
    const onPrintTypstSummaryPdf = this.onPrintTypstSummaryPdf(location);

    if (loading) {
      return <Loader />;
    }
    return (
      <BaseArAgingReportExport
        arAgingData={trimmedArAgingReportList}
        arSummary={arSummary}
        selectedMonthEndTime={selectedMonthEndTime}
        sortDetails={sortDetails}
        onPrintNonTypstSummaryPdf={onPrintNonTypstSummaryPdf}
        onPrintTypstSummaryPdf={onPrintTypstSummaryPdf}
        getFormattedDateAndTime={getFormattedDateAndTime}
        getFormattedCurrency={getFormattedCurrency}
        getFormattedPhoneNumber={getFormattedPhoneNumber}
        getFormattedNumber={getFormattedNumber}
        location={location}
        assetId={assetId}
        isPreviewPage
      />
    );
  }
}

ArAgingSummaryExportPreview.propTypes = {
  getFormattedDateAndTime: PropTypes.func.isRequired,
  getFormattedCurrency: PropTypes.func.isRequired,
  getFormattedPhoneNumber: PropTypes.func.isRequired,
  getFormattedNumber: PropTypes.func.isRequired,
  accessibleEnterpriseWorkspaceIds: PropTypes.array,
  location: PropTypes.object.isRequired,
  assetId: PropTypes.string,
};

ArAgingSummaryExportPreview.defaultProps = {
  accessibleEnterpriseWorkspaceIds: EMPTY_ARRAY,
  assetId: AR_AGING_REPORT_PDF_ASSET_ID,
};

const mapStateToProps = state => ({
  accessibleEnterpriseWorkspaceIds: getAccessibleEnterpriseWorkspaceIds(state),
});

export default compose(withRouter, withTekionConversion, connect(mapStateToProps))(ArAgingSummaryExportPreview);
