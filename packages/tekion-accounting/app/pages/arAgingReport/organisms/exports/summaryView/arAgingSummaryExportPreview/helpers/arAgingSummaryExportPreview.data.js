// Lodash
import _slice from 'lodash/slice';

// Constants
import { NO_OF_ROWS } from 'constants/pdf';

// helpers
import { onPrintSummaryPdf, onPrintSummaryTypstPdf } from '../../../helpers/arAgingReportExport.makeRequest';

const getTrimmedArAgingReportList = arAgingReportList => _slice(arAgingReportList, 0, NO_OF_ROWS);

export const onPrintNonTypstSummaryPdf = location => () => onPrintSummaryPdf(location);

export const onPrintTypstSummaryPdf = location => () => onPrintSummaryTypstPdf(location);

export default getTrimmedArAgingReportList;
