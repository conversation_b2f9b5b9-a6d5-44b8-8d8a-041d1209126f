/* eslint-disable import/order */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import { connect } from 'react-redux';

// Lodash
import _noop from 'lodash/noop';
import _identity from 'lodash/identity';

// Containers
import withCustomerComplianceHeaderInfo from 'containers/withCustomerComplianceHeaderInfo';

// Components
import { Pdf } from 'organisms/pdfGenerator';
import RightHeaderPanel from '../../../../atoms/rightHeaderPanel';
import PDFViewer from 'organisms/pdfViewer';
import Loader from 'tcomponents/molecules/loader';

// Constants
import { HEADING_HELP_TEXT } from 'constants/pdf';
import { EMPTY_OBJECT, EMPTY_SET } from 'tbase/app.constants';
import {
  DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION,
  AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID,
  AR_AGING_REPORT_SUMMARY_PDF_ASSET_TYPE,
  AR_AGING_REPORT_PDF_ASSET_ID,
  AR_AGING_SUMMARY_PDF_PREVIEW_TITLE,
  OVERRIDE_KEYS_FROM_TEKION_DEFAULTS,
} from '../../../../constants/exports/arAgingReportExport.general';
import TYPST_SUPPORTED_REPORTS from 'tbusiness/appServices/accounting/constants/typstSupportedReports';
import { getSummaryColumnConfigOverrides } from '../helpers/summaryView.previewColumnConfig';
import { AR_AGING_SUMMARY_DOCUMENT_TYPE } from '../../../../constants/exports/arAgingReportExport.preview';

// Helpers
import makeTableData from './helpers/baseArAgingSummaryExport.tableData';
import { getDealerAddress } from '../../../../helpers/exports/arAgingReportExport.general';
import { getIsCustomerDeletedInPage } from '../../../../helpers/arAgingReport.general';
import getSectionConfig from './helpers/baseArAgingSummaryExport.sectionConfig';
import { getArAgingSummaryReportColumnConfigurations } from '../../helpers/arAgingReportExport.columnConfiguration';
import { getPdfConfiguration, loadPdfConfiguration } from '../../helpers/arAgingReportExport.pdfConfiguration';
import { getPayloadForPdf } from '../../../../helpers/arAgingReport.makeExportRequest';

// Readers
import dealerInfoReader from 'tbase/readers/DealerInfo';
import TEnvReader from 'tbase/readers/Env';

// Selectors
import { getDealerInfo, getTypstSupportedReportSet } from 'reducers/selectors/app.selectors';

// Contexts
import FeatureFlagsContext from 'twidgets/context/featureFlags';

// Utils
import shouldShowTypstPdf from 'tbusiness/appServices/accounting/utils/shouldShowTypstPdf';

const RightHeaderWithCustomerComplianceInfo = withCustomerComplianceHeaderInfo(RightHeaderPanel);

class BaseArAgingSummaryExport extends Component {
  state = {
    isLoading: true,
    pdfConfiguration: EMPTY_OBJECT,
  };

  makeTableData = defaultMemoize(makeTableData);

  getIsCustomerDeletedInPage = defaultMemoize(getIsCustomerDeletedInPage);

  getSectionConfig = defaultMemoize(getSectionConfig);

  componentDidMount() {
    const featureFlags = this.context;
    const { typstSupportedReportSet, assetId } = this.props;
    const showTypstPdf = shouldShowTypstPdf({
      featureFlags,
      typstSupportedReportSet,
      reportName: TYPST_SUPPORTED_REPORTS.AR_AGING_REPORT,
    });
    loadPdfConfiguration({
      assetType: AR_AGING_REPORT_SUMMARY_PDF_ASSET_TYPE,
      assetId,
      showTypstPdf,
      setState: this.setState.bind(this),
    });
  }

  makePayloadForPDF = () => {
    const { location } = this.props;
    const request = getPayloadForPdf(location, AR_AGING_SUMMARY_DOCUMENT_TYPE);
    return request;
  };

  renderRightHeaderPanel() {
    const {
      dealerInfo,
      arSummary,
      selectedMonthEndTime,
      getFormattedDateAndTime,
      getFormattedCurrency,
      arAgingData,
      getFormattedNumber,
    } = this.props;
    const dealerName = dealerInfoReader.dealerName(dealerInfo);
    const dealeraddress = getDealerAddress(dealerInfo);
    const dealerWebsite = dealerInfoReader.website(dealerInfo);
    const dealerPhoneNumber = dealerInfoReader.phone(dealerInfo);
    const isCustomerDeleted = this.getIsCustomerDeletedInPage(arAgingData);

    return (
      <RightHeaderWithCustomerComplianceInfo
        dealerName={dealerName}
        dealeraddress={dealeraddress}
        dealeremail={dealerWebsite}
        dealerPhoneNumber={dealerPhoneNumber}
        arSummary={arSummary}
        selectedMonthEndTime={selectedMonthEndTime}
        getFormattedDateAndTime={getFormattedDateAndTime}
        getFormattedCurrency={getFormattedCurrency}
        dealerInfo={dealerInfo}
        isCustomerDeleted={isCustomerDeleted}
        getFormattedNumber={getFormattedNumber}
      />
    );
  }

  renderTypstPDF({
    featureFlags,
    pdfConfiguration,
    getFormattedNumber,
    getFormattedCurrency,
    getFormattedPhoneNumber,
    assetId,
  }) {
    const { isPreviewPage, onPrintTypstSummaryPdf } = this.props;
    const isServerSideRendered = TEnvReader.isExportMode();

    const columnConfigurations = getArAgingSummaryReportColumnConfigurations(
      pdfConfiguration,
      getFormattedCurrency,
      getFormattedNumber,
      getFormattedPhoneNumber,
      featureFlags
    );
    const defaultPdfConfiguration = getPdfConfiguration(pdfConfiguration, columnConfigurations);

    const {
      printMode = DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION.printMode,
      printView = DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION.printView,
      textWrap,
    } = pdfConfiguration;

    return (
      <PDFViewer
        assetType={AR_AGING_REPORT_SUMMARY_PDF_ASSET_TYPE}
        assetId={assetId}
        pdfTitle={AR_AGING_SUMMARY_PDF_PREVIEW_TITLE}
        columnConfigurations={columnConfigurations}
        printView={printView}
        printMode={printMode}
        wordWrap={textWrap}
        defaultPdfConfiguration={defaultPdfConfiguration}
        isServerSideRendered={isServerSideRendered}
        getPayloadForPdf={this.makePayloadForPDF}
        isPreviewPage={isPreviewPage}
        headingHelpText={HEADING_HELP_TEXT}
        onPreviewPdfPrint={onPrintTypstSummaryPdf}
      />
    );
  }

  render() {
    const {
      arAgingData,
      sortDetails,
      onPrintNonTypstSummaryPdf,
      dealerInfo,
      getFormattedCurrency,
      getFormattedPhoneNumber,
      getFormattedNumber,
      typstSupportedReportSet,
      assetId,
    } = this.props;
    const { isLoading, pdfConfiguration } = this.state;
    const tableData = this.makeTableData(arAgingData, sortDetails);
    const summaryColumnConfigOverrides = getSummaryColumnConfigOverrides(getFormattedCurrency, getFormattedPhoneNumber);
    const featureFlags = this.context;
    const sectionConfig = this.getSectionConfig(featureFlags);

    const showTypstPdf = shouldShowTypstPdf({
      featureFlags,
      typstSupportedReportSet,
      reportName: TYPST_SUPPORTED_REPORTS.AR_AGING_REPORT,
    });
    if (isLoading) return <Loader />;

    return showTypstPdf ? (
      this.renderTypstPDF({
        featureFlags,
        pdfConfiguration,
        getFormattedNumber,
        getFormattedCurrency,
        getFormattedPhoneNumber,
        assetId,
      })
    ) : (
      <Pdf.Container
        title={AR_AGING_SUMMARY_PDF_PREVIEW_TITLE}
        tekionDefaultSectionConfigurations={sectionConfig}
        assetType={AR_AGING_REPORT_SUMMARY_PDF_ASSET_TYPE}
        assetId={assetId}
        printMode={DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION.printMode}
        printView={DEFAULT_AR_AGING_REPORT_PRINT_CONFIGURATION.printView}
        overrideKeysFromTekionDefaults={OVERRIDE_KEYS_FROM_TEKION_DEFAULTS}
        headingHelpText={HEADING_HELP_TEXT}
        onPrintPdf={onPrintNonTypstSummaryPdf}>
        <Pdf.Page
          columnConfigByKey={summaryColumnConfigOverrides}
          tableData={tableData}
          detailConfigurationId={AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID}
          dealerInfo={dealerInfo}>
          {this.renderRightHeaderPanel()}
        </Pdf.Page>
      </Pdf.Container>
    );
  }
}

BaseArAgingSummaryExport.contextType = FeatureFlagsContext;

BaseArAgingSummaryExport.propTypes = {
  arAgingData: PropTypes.object,
  arSummary: PropTypes.object,
  dealerInfo: PropTypes.object,
  selectedMonthEndTime: PropTypes.number,
  sortDetails: PropTypes.object,
  onPrintNonTypstSummaryPdf: PropTypes.func,
  getFormattedDateAndTime: PropTypes.func,
  getFormattedCurrency: PropTypes.func,
  getFormattedNumber: PropTypes.func,
  getFormattedPhoneNumber: PropTypes.func,
  location: PropTypes.object,
  isPreviewPage: PropTypes.bool,
  typstSupportedReportSet: PropTypes.instanceOf(Set),
  onPrintTypstSummaryPdf: PropTypes.func,
  assetId: PropTypes.string,
};

BaseArAgingSummaryExport.defaultProps = {
  arAgingData: EMPTY_OBJECT,
  arSummary: EMPTY_OBJECT,
  dealerInfo: EMPTY_OBJECT,
  sortDetails: EMPTY_OBJECT,
  selectedMonthEndTime: undefined,
  onPrintNonTypstSummaryPdf: undefined,
  getFormattedDateAndTime: _noop,
  getFormattedCurrency: _noop,
  getFormattedNumber: _identity,
  getFormattedPhoneNumber: _identity,
  location: EMPTY_OBJECT,
  isPreviewPage: false,
  typstSupportedReportSet: EMPTY_SET,
  onPrintTypstSummaryPdf: _noop,
  assetId: AR_AGING_REPORT_PDF_ASSET_ID,
};

const mapStateToProps = state => ({
  dealerInfo: getDealerInfo(state),
  typstSupportedReportSet: getTypstSupportedReportSet(state),
});

export default connect(mapStateToProps)(BaseArAgingSummaryExport);
