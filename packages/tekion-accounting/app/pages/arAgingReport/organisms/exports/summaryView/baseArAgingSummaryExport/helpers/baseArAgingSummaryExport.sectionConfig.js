// constants
import SUMMARY_COLUMNS from '../constants/baseArAgingSummaryExport.columns';
import {
  AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID,
  AR_AGING_SUMMARY_SECTION_CONFIG_TITLE,
} from '../../../../../constants/exports/arAgingReportExport.general';

// Helpers
import { getFilteredArAgingSummaryColumns } from '../../../../../helpers/exports/arAgingReportExport.general';

const getSectionConfig = featureFlags => {
  const filteredArAgingSummaryColumns = getFilteredArAgingSummaryColumns(SUMMARY_COLUMNS, featureFlags);
  return [
    {
      title: AR_AGING_SUMMARY_SECTION_CONFIG_TITLE,
      id: AR_AGING_REPORT_SUMMARY_COLUMN_CONFIGURATION_ID,
      columns: filteredArAgingSummaryColumns,
    },
  ];
};

export default getSectionConfig;
