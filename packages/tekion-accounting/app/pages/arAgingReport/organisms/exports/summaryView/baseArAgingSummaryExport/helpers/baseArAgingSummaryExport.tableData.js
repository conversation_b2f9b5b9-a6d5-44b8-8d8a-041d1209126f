// Lodash
import _map from 'lodash/map';

// Constants
import { PdfTableRow, ROW_TYPES, makePdfRowsAlternateColors } from 'twidgets/organisms/pdfGenerator';
import SUMMARY_COLUMN_CONFIG_BY_KEY from '../../../../../constants/exports/arAgingReportExport.summaryPreviewColumnConfig';

// Helpers
import { getSortedAgingReportList } from '../../../../../helpers/exports/arAgingReportExport.sort';

const addArAgingListRow = (arAgingListRowData, rowIndex) => {
  const arAgingListRow = new PdfTableRow()
    .setRowInfo(arAgingListRowData)
    .setRowType(ROW_TYPES.SIMPLE_ROW)
    .setRowStyles(makePdfRowsAlternateColors(rowIndex));
  return arAgingListRow;
};

const makeTableData = (arAgingList, sortDetails) => {
  const sortedArAgingReportList = getSortedAgingReportList(arAgingList, SUMMARY_COLUMN_CONFIG_BY_KEY, sortDetails);
  const tableData = _map(sortedArAgingReportList, addArAgingListRow);
  return tableData;
};

export default makeTableData;
