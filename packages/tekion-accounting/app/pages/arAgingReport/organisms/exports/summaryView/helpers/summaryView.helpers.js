// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';

// utils
import toastAPIError from 'utils/toastAPIError';
import getParamsFromQueryString from 'tbusiness/appServices/accounting/utils/getParamsFromQueryString';

// actions
import { getArAgingReportList } from '../../../../actions/arAgingReport.exportActions';

const handleFetchAgingDataFailure = err => {
  toastAPIError(err, __('Unable to fetch AR Aging Report details'));
};

const saveArAgingDetails = sortDetails => ({ arSummary, resolvedArAgingReportList }) => ({
  arAgingReportList: resolvedArAgingReportList,
  arSummary,
  sortDetails,
});

const fetchArAgingReportData = (location, accessibleEnterpriseWorkspaceIds) => {
  const { filters, additional: additionalQueryParams = EMPTY_OBJECT, sortDetails } = getParamsFromQueryString(
    location.search
  );
  const { includeZeroAmountInvoices } = additionalQueryParams;

  return getArAgingReportList({ filters, includeZeroAmountInvoices, accessibleEnterpriseWorkspaceIds })
    .then(saveArAgingDetails(sortDetails))
    .catch(handleFetchAgingDataFailure);
};

export default fetchArAgingReportData;
