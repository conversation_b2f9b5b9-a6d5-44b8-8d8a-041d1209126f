// Readers
import arAgingReportReader from 'readers/ArAgingReport';

// Utils
import withDeletedCustomerDataCheck from 'utils/withDeletedCustomerDataCheck';
import getClassNameForDeletedCustomerData from 'utils/getClassNameForDeletedCustomerData';

// Constants
import SUMMARY_COLUMN_IDS from '../../../../constants/arAgingReport.listColumnIds';

// Helpers
import {
  getCustomerNameFromData,
  getCustomerNumber,
  getAgingBalanceAmount,
  getCustomerEmail,
  getCustomerPhoneNumber,
  getCustomerGroup,
} from '../../../../helpers/arAgingReport.columns';

export const getSummaryColumnConfigOverrides = (getFormattedCurrency, getFormattedPhoneNumber) => ({
  [SUMMARY_COLUMN_IDS.FIFTH_MONTH_COLUMN]: {
    accessor: getAgingBalanceAmount(arAgingReportReader.fifthMonth, getFormattedCurrency),
    className: 'tk-table-cell-right-align',
  },
  [SUMMARY_COLUMN_IDS.FIRST_MONTH_COLUMN]: {
    accessor: getAgingBalanceAmount(arAgingReportReader.firstMonth, getFormattedCurrency),
    className: 'tk-table-cell-right-align',
  },
  [SUMMARY_COLUMN_IDS.FOURTH_MONTH_COLUMN]: {
    accessor: getAgingBalanceAmount(arAgingReportReader.fourthMonth, getFormattedCurrency),
    className: 'tk-table-cell-right-align',
  },
  [SUMMARY_COLUMN_IDS.OUTSTANDING_BAL_COLUMN]: {
    accessor: getAgingBalanceAmount(arAgingReportReader.outstandingBal, getFormattedCurrency),
    className: 'tk-table-cell-right-align',
  },
  [SUMMARY_COLUMN_IDS.SECOND_MONTH_COLUMN]: {
    accessor: getAgingBalanceAmount(arAgingReportReader.secondMonth, getFormattedCurrency),
    className: 'tk-table-cell-right-align',
  },
  [SUMMARY_COLUMN_IDS.THIRD_MONTH_COLUMN]: {
    accessor: getAgingBalanceAmount(arAgingReportReader.thirdMonth, getFormattedCurrency),
    className: 'tk-table-cell-right-align',
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_NAME_COLUMN]: {
    accessor: withDeletedCustomerDataCheck(getCustomerNameFromData),
    getClassName: getClassNameForDeletedCustomerData,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_NUMBER_COLUMN]: {
    accessor: withDeletedCustomerDataCheck(getCustomerNumber),
    getClassName: getClassNameForDeletedCustomerData,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_EMAIL_ADDRESS]: {
    accessor: withDeletedCustomerDataCheck(getCustomerEmail),
    getClassName: getClassNameForDeletedCustomerData,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_PHONE_NUMBER]: {
    accessor: withDeletedCustomerDataCheck(getCustomerPhoneNumber(getFormattedPhoneNumber)),
    getClassName: getClassNameForDeletedCustomerData,
  },
  [SUMMARY_COLUMN_IDS.CUSTOMER_GROUP]: {
    accessor: getCustomerGroup,
  },
  [SUMMARY_COLUMN_IDS.FINANCE_CHARGE]: {
    accessor: getAgingBalanceAmount(arAgingReportReader.financeCharge, getFormattedCurrency),
    className: 'tk-table-cell-right-align',
  },
});
