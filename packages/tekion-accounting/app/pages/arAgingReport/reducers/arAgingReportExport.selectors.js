import { createSelector } from 'reselect';

// Selectors
import {
  getAccountsMap,
  getAllJournalsMap,
  getAllAccountsMapByDealerId,
  getAllJournalsMapByDealerId,
} from 'reducers/selectors/app.selectors';

// Constants
import { RESOURCE_TYPE } from 'tbase/bulkResolvers/constants/resourceType';

const createLocalLookup = (accountsMap, allJournalMap) => ({
  [RESOURCE_TYPE.GLACCOUNT]: accountsMap,
  [RESOURCE_TYPE.JOURNAL_ID]: allJournalMap,
});

export const getLocalLookup = createSelector([getAccountsMap, getAllJournalsMap], createLocalLookup);

const createLocalLookupByDealerId = (accountsMapByDealerId, journalsMapByDealerId) => ({
  [RESOURCE_TYPE.GLACCOUNT]: accountsMapByDealerId,
  [RESOURCE_TYPE.JOURNAL_ID]: journalsMapByDealerId,
});

export const getLocalLookupByDealerId = createSelector(
  [getAllAccountsMapByDealerId, getAllJournalsMapByDealerId],
  createLocalLookupByDealerId
);
