import { lazy } from 'react';

// Constants
import FEATURE_FLAGS from 'tbusiness/appServices/accounting/constants/featureFlags';
import { EMPTY_FEATURE_FLAGS } from 'twidgets/context/featureFlags';

const PostTransaction = lazy(() => import('../organisms/postTransaction' /* webpackChunkName: "postTransaction" */));

const ConfigurableBankRecJournalEntry = lazy(
  () => import('../organisms/configurableBankRecJournalEntry' /* webpackChunkName: "configurableBankRecJournalEntry" */)
);

const CreateBankRecCreationEntry = lazy(
  () => import('../organisms/createBankRecCreationEntry' /* webpackChunkName: "createBankRecCreationEntry" */)
);

const VirtualPostingForm = lazy(
  () => import('../organisms/virtualPostingForm' /* webpackChunkName: "virtualPostingForm" */)
);

export const getCreateBankRecJEComponent = (featureFlags = EMPTY_FEATURE_FLAGS) => {
  const isConfigurableAccountingEnabled = featureFlags.getFlag(FEATURE_FLAGS.CONFIGURABLE_ACCOUNTING_ENABLED);
  return isConfigurableAccountingEnabled ? ConfigurableBankRecJournalEntry : PostTransaction;
};

export const getAddUnreconciledPostingLinesComponent = (featureFlags = EMPTY_FEATURE_FLAGS) => {
  const isConfigurableAccountingMode = featureFlags.getFlag(FEATURE_FLAGS.IS_CONFIGURABLE_ACCOUNTING_MODE);
  return isConfigurableAccountingMode ? VirtualPostingForm : CreateBankRecCreationEntry;
};
