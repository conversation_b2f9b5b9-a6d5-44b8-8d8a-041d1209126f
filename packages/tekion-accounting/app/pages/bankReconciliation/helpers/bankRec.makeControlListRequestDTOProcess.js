// Lodash
import _map from 'lodash/map';
import _head from 'lodash/head';

// Builders
import ControlTag from 'tbusiness/appServices/accounting/builders/ControlTag';

// Helpers
import { getDepartmentFields } from 'twidgets/appServices/accounting/organisms/configurableTransaction';

// Utils
import Process from 'tbase/utils/Process';
import isNotEmpty from 'tbase/utils/isNotEmpty';

// Constants
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import COLUMN_IDS from '../constants/bankRec.virtualPostingsTable.columnIds';

const checkAndAddNonEmptyControl = ({ controlList, controlType, controlId, controlText, order, primary }) => {
  if (controlId) {
    controlList.push({ refType: controlType, refId: controlId, refText: controlText, order, primary });
  }
  return controlList;
};

const addDepartment = (next, params) => {
  const { controlList, posting, setupFields, accountsMap, glAccountId } = params;
  const departmentId = _head(posting[COLUMN_IDS.DEPARTMENT]);
  const accountDetails = accountsMap[glAccountId];
  const departmentIds = getDepartmentFields(accountDetails, setupFields);
  if (isNotEmpty(departmentIds)) {
    checkAndAddNonEmptyControl({
      controlList,
      controlType: 'DEPARTMENT',
      controlId: departmentId,
      controlText: departmentId,
      order: -1,
      primary: false,
    });
    return next({ ...params, controlList });
  }
  return next({ ...params, controlList });
};

const getNormalizedControlTag = (controlTag = new ControlTag()) => ({
  refType: controlTag.getRefType(),
  refId: controlTag.getRefId(),
  refText: controlTag.getRefText(),
  order: controlTag.getOrder(),
  primary: controlTag.getIsPrimary(),
});

const addControls = (next, params) => {
  const { controlList, posting } = params;
  const controls = posting[COLUMN_IDS.CONTROL_TAGS] || EMPTY_ARRAY;
  const updatedControls = _map(controls, getNormalizedControlTag);
  controlList.push(...updatedControls);
  return controlList;
};

const MAKE_CONTROL_LIST_REQUEST_DTO_PROCESS = new Process().addHandler(addDepartment).addHandler(addControls);

const makeControlListRequestDTOProcess = ({ posting = EMPTY_OBJECT, setupFields, accountsMap, glAccountId }) => {
  const controlNumberList = MAKE_CONTROL_LIST_REQUEST_DTO_PROCESS.run({
    controlList: [],
    posting,
    setupFields,
    accountsMap,
    glAccountId,
  });
  return controlNumberList;
};

export default makeControlListRequestDTOProcess;
