/* eslint-disable import/order */
// Lodash
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _head from 'lodash/head';
import _omit from 'lodash/omit';
import _filter from 'lodash/filter';

// Utils
import toastAPIError from 'utils/toastAPIError';
import { getTimeStamp } from 'tbase/utils/dateUtils';

// Components
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';

// Helpers
import { isRowNotEmpty } from 'tbase/helpers/tableRowHelper';
import makeControlListRequestDTOProcess from './bankRec.makeControlListRequestDTOProcess';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { EMPTY_FEATURE_FLAGS } from 'twidgets/context/featureFlags';
import FEATURE_FLAGS from 'tbusiness/appServices/accounting/constants/featureFlags';
import COLUMN_IDS from '../constants/bankRec.virtualPostingsTable.columnIds';
import FIELD_IDS from '../constants/bankRec.virtualPostings.fieldIds';
import { PAGE_TYPE } from '../constants/banRec.pageType';
import { VIRTUAL_POSTING_PREFIX } from '../constants/bankRec.general';

// Services
import { postVirtualTransaction } from '../services/bankRecService';

export const isNonEmptyPostingRow = posting => {
  const postingWithoutId = _omit(posting, VIRTUAL_POSTING_PREFIX);
  return isRowNotEmpty(postingWithoutId);
};

export const getNonEmptyVirtualPostings = postings => _filter(postings, isNonEmptyPostingRow);

const makeVirtualPosting =
  ({ accountsMap, setupFields, glAccountId, featureFlags = EMPTY_FEATURE_FLAGS }) =>
  (posting = EMPTY_OBJECT) => {
    const amount = posting[COLUMN_IDS.AMOUNT];
    const control2Type = posting[COLUMN_IDS.CONTROL2];
    const controlNumber = posting[COLUMN_IDS.CONTROL_NUMBER] || EMPTY_OBJECT;
    const description = posting[COLUMN_IDS.DESCRIPTION];
    const journalId = posting[COLUMN_IDS.JOURNAL_ID];
    const reference = posting[COLUMN_IDS.REFERENCE];
    const accountingDate = posting[COLUMN_IDS.ACCOUNTING_DATE];

    const isConfigurableAccountingEnabled = featureFlags.getFlag(FEATURE_FLAGS.CONFIGURABLE_ACCOUNTING_ENABLED);
    let controlNumberList;
    if (isConfigurableAccountingEnabled) {
      controlNumberList = makeControlListRequestDTOProcess({ posting, setupFields, accountsMap, glAccountId });
    }

    return {
      journalID: _head(journalId),
      parentRefText: reference,
      refText: controlNumber.value,
      description,
      control2Type,
      amount,
      accountingDate: getTimeStamp(accountingDate),
      controlNumberList,
    };
  };

const createVirtualPostingsData = ({ values = EMPTY_OBJECT, glAccountId, accountsMap, setupFields, featureFlags }) => {
  const postings = values[FIELD_IDS.POSTINGS];

  const nonEmptyPostings = getNonEmptyVirtualPostings(postings);
  const virtualPostingsData = _map(
    nonEmptyPostings,
    makeVirtualPosting({ accountsMap, setupFields, glAccountId, featureFlags })
  );

  return virtualPostingsData;
};

const makeRequestDTO = ({ values = EMPTY_OBJECT, accountsMap, setupFields, featureFlags }) => {
  const glAccountId = _head(values[FIELD_IDS.GL_ACCOUNT_ID]);
  const virtualPostings = createVirtualPostingsData({ values, glAccountId, accountsMap, setupFields, featureFlags });

  return {
    glAccountId,
    virtualPostings,
  };
};

const handleVirtualPostingCreationSuccess =
  (setPageType = _noop) =>
  () => {
    toaster(TOASTER_TYPE.SUCCESS, __('Unreconciled postings added successfully'));
    setPageType(PAGE_TYPE.CREATE_BEGINNING_BALANCE);
  };

const handleVirtualPostingError = err => {
  toastAPIError(err, __('Failed to add postings'));
};

const setSubmitBtnAsLoaded =
  (setState = _noop) =>
  () => {
    setState({ submitBtnLoading: false });
  };

const submitVirtualPosting = ({ values, accountsMap, setupFields, setPageType, featureFlags, setState }) => {
  setState({ submitBtnLoading: true });
  const request = makeRequestDTO({ values, accountsMap, setupFields, featureFlags });
  postVirtualTransaction(request)
    .then(handleVirtualPostingCreationSuccess(setPageType))
    .catch(handleVirtualPostingError)
    .finally(setSubmitBtnAsLoaded(setState));
};

export default submitVirtualPosting;
