/* eslint import/order: 0 */
import React, { useMemo, useContext } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'recompose';

// Lodash
import _noop from 'lodash/noop';

// Components
import { FormWithSubmission } from 'tcomponents/pages/formPage';

// Containers
import withConfigurableEntityMetadata from 'containers/withConfigurableEntityMetadata';
import withActions from 'tcomponents/connectors/withActions';

// Context
import SetupFieldsContext from 'twidgets/appServices/accounting/context/SetupFieldContext';
import FeatureFlagsContext from 'twidgets/context/featureFlags';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import ASSET_TYPES_BY_MODULE from 'tbusiness/constants/configurableEntity/assetTypesByModule';
import { ACCOUNTING } from 'tbase/constants/appServices';
import { UNRECONCILED_POSTINGS_CONTEXT_ID } from '../../constants/bankRec.general';

// Helpers
import makeFields from './helpers/virtualPostingForm.makeFields';
import makeSections from './helpers/virtualPostingForm.makeSections';
import ACTION_HANDLERS from './helpers/virtualPostingForm.actionHandlers';
import { makeInitalState } from './helpers/virtualPostingForm.general';

// Selectors
import {
  getActiveJournalsFromStore,
  getAccountsMap,
  getAccountingSettings,
  getProgramFieldMap,
  getLookupAssetSupportMap,
} from '../../../../reducers/selectors/app.selectors';
import { getSelectedGlAccount } from '../../reducers/selectors/bankRec.selectors';

// Actions
import { setPageType } from '../../actions/bankRec.actions';

const VirtualPostingForm = ({
  onAction,
  accountsMap,
  activeJournals,
  accountingSettings,
  entityMetadataByEntityName,
  selectedAccount,
  errors,
  values,
  programFieldMap,
  lookupAssetSupportMap,
}) => {
  const featureFlags = useContext(FeatureFlagsContext);
  const setupFields = useContext(SetupFieldsContext);

  const entityMetadata = entityMetadataByEntityName[ASSET_TYPES_BY_MODULE[ACCOUNTING].BANK_REC_VIRTUAL_POSTING];

  const fields = useMemo(
    () =>
      makeFields({
        entityMetadata,
        selectedAccount,
        accountsMap,
        setupFields,
        activeJournals,
        accountingSettings,
        programFieldMap,
        lookupAssetSupportMap,
        featureFlags,
      }),
    [
      entityMetadata,
      selectedAccount,
      accountsMap,
      setupFields,
      activeJournals,
      accountingSettings,
      programFieldMap,
      lookupAssetSupportMap,
      featureFlags,
    ]
  );

  const sections = useMemo(() => makeSections(entityMetadata), [entityMetadata]);

  return (
    <FormWithSubmission
      sections={sections}
      fields={fields}
      values={values}
      errors={errors}
      onAction={onAction}
      contextId={UNRECONCILED_POSTINGS_CONTEXT_ID}
    />
  );
};

VirtualPostingForm.propTypes = {
  accountsMap: PropTypes.object,
  onAction: PropTypes.func,
  activeJournals: PropTypes.object,
  accountingSettings: PropTypes.object,
  values: PropTypes.object,
  errors: PropTypes.object,
  entityMetadataByEntityName: PropTypes.object,
  selectedAccount: PropTypes.object,
  programFieldMap: PropTypes.object,
  lookupAssetSupportMap: PropTypes.object,
};

VirtualPostingForm.defaultProps = {
  accountsMap: EMPTY_OBJECT,
  onAction: _noop,
  activeJournals: EMPTY_OBJECT,
  accountingSettings: EMPTY_OBJECT,
  values: EMPTY_OBJECT,
  errors: EMPTY_OBJECT,
  entityMetadataByEntityName: EMPTY_OBJECT,
  selectedAccount: EMPTY_OBJECT,
  programFieldMap: EMPTY_OBJECT,
  lookupAssetSupportMap: EMPTY_OBJECT,
};

const mapStateToProps = state => ({
  activeJournals: getActiveJournalsFromStore(state),
  accountsMap: getAccountsMap(state),
  selectedAccount: getSelectedGlAccount(state),
  accountingSettings: getAccountingSettings(state),
  programFieldMap: getProgramFieldMap(state),
  lookupAssetSupportMap: getLookupAssetSupportMap(state),
});

const mapDispatchToProps = {
  setPageType,
};

export default compose(
  withConfigurableEntityMetadata([ASSET_TYPES_BY_MODULE[ACCOUNTING].BANK_REC_VIRTUAL_POSTING]),
  connect(mapStateToProps, mapDispatchToProps),
  withActions(makeInitalState, ACTION_HANDLERS)
)(VirtualPostingForm);
