import React from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Hooks
import useFormFieldChange from 'tcomponents/organisms/FormBuilder/hooks/useFormFieldChange';

// Components
import VirtualPostingTable from '../../organisms/virtualPostingTable';

const VirtualPostingTableField = props => {
  const { onAction, id } = props;

  const { handleChange } = useFormFieldChange(onAction, id);

  return <VirtualPostingTable {...props} onChange={handleChange} />;
};

VirtualPostingTableField.propTypes = {
  onAction: PropTypes.func,
  id: PropTypes.string,
};

VirtualPostingTableField.defaultProps = {
  onAction: _noop,
  id: undefined,
};

export default VirtualPostingTableField;
