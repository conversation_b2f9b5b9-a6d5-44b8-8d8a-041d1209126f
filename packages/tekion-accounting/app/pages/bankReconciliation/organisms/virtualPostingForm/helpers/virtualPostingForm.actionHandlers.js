// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import FORM_ACTION_TYPES from 'tcomponents/organisms/FormBuilder/constants/actionTypes';
import FORM_PAGE_ACTION_TYPES from 'tcomponents/pages/formPage/constants/actionTypes';

export const handleOnFormSubmit = ({ getState }) => {
  const { values, accountsMap, setupFields, setPageType, featureFlags, onSubmit } = getState();
  return onSubmit({ values, accountsMap, setupFields, setPageType, featureFlags });
};

const handleValidationSuccess = ({ setState, params }) => {
  const { errors = EMPTY_OBJECT } = params;
  setState({ errors });
};

const handleFieldChange = ({ params, getState, setState }) => {
  const { id, value } = params;
  const { values } = getState();
  setState({
    values: { ...values, [id]: value },
  });
};

const ACTION_HANDLERS = {
  [FORM_PAGE_ACTION_TYPES.ON_FORM_SUBMIT]: handleOnFormSubmit,
  [FORM_ACTION_TYPES.VALIDATION_SUCCESS]: handleValidationSuccess,
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: handleFieldChange,
};

export default ACTION_HANDLERS;
