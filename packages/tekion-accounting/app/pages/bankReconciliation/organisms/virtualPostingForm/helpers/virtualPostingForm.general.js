// Lodash
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _every from 'lodash/every';
import _stubTrue from 'lodash/stubTrue';

// Utils
import { isRequiredRule } from 'tbase/utils/formValidators';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import FIELD_IDS from '../../../constants/bankRec.virtualPostings.fieldIds';
import COLUMN_IDS from '../../../constants/bankRec.virtualPostingsTable.columnIds';
import { VIRTUAL_POSTING_PREFIX } from '../../../constants/bankRec.general';
import { FIRST_ROW_INDEX } from '../constants/virtualPostingForm.general';
import { getDefaultVirtualPostings } from '../../../helpers/bankRec.general';

export const makeInitalState = props => {
  const glAccountId = _get(props, 'selectedAccount.glAccountId');
  const initialValues = {
    [FIELD_IDS.GL_ACCOUNT_ID]: [glAccountId],
    [FIELD_IDS.POSTINGS]: getDefaultVirtualPostings(),
  };

  return { values: initialValues };
};

const isPostingValueEmpty = value => !value;

const isControlNumberColumnEmpty = value => {
  const controlNumber = _get(value, 'value');
  return !controlNumber;
};

const COLUMN_ID_VS_EMPTY_VALIDATOR = {
  [COLUMN_IDS.AMOUNT]: isPostingValueEmpty,
  [COLUMN_IDS.CONTROL_NUMBER]: isControlNumberColumnEmpty,
  [COLUMN_IDS.CONTROL_TAGS]: _isEmpty,
  [COLUMN_IDS.CONTROL2]: _stubTrue,
  [COLUMN_IDS.REFERENCE]: _stubTrue,
  [COLUMN_IDS.ACCOUNTING_DATE]: isPostingValueEmpty,
  [COLUMN_IDS.DEPARTMENT]: isPostingValueEmpty,
  [VIRTUAL_POSTING_PREFIX]: _stubTrue,
};

const isColumnEmpty = (value, columnId) => {
  const emptyValidator = COLUMN_ID_VS_EMPTY_VALIDATOR[columnId] || _isEmpty;
  return emptyValidator(value);
};

export const isPostingRowEmpty = postingRow => _every(postingRow, isColumnEmpty);

const getCurrentPosting = (formData, pathToUpdate) => _get(formData, `allValues.${FIELD_IDS.POSTINGS}.${pathToUpdate}`);

export const isPostingRowRequiredRule = (columnId, valueToTest, formData = EMPTY_OBJECT) => {
  const { pathToUpdate } = formData;
  const currentPosting = getCurrentPosting(formData, pathToUpdate);
  if (pathToUpdate > FIRST_ROW_INDEX && isPostingRowEmpty(currentPosting)) {
    return { isValid: true };
  }
  return isRequiredRule(columnId, valueToTest, formData);
};

export const getAccountDetails = (selectedAccount = EMPTY_OBJECT, accountsMap = EMPTY_OBJECT) => {
  const { glAccountId } = selectedAccount;
  const accountDetails = accountsMap[glAccountId] || EMPTY_OBJECT;
  return accountDetails;
};
