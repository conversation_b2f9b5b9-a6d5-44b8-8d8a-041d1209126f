/* eslint-disable import/order */
// Lodash
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';

// Utils
import { tableValidator } from 'tcomponents/utils/tableFieldValidators';
import isNotEmpty from 'tbase/utils/isNotEmpty';

// Constants
import { EMPTY_FEATURE_FLAGS } from 'twidgets/context/featureFlags';
import FEATURE_FLAGS from 'tbusiness/appServices/accounting/constants/featureFlags';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import COLUMN_IDS from '../../../constants/bankRec.virtualPostingsTable.columnIds';
import FIELD_IDS from '../../../constants/bankRec.virtualPostings.fieldIds';
import { FIRST_ROW_INDEX } from '../constants/virtualPostingForm.general';

// Helpers
import { getDepartmentFields } from 'twidgets/appServices/accounting/organisms/configurableTransaction';
import { getAccountDetails, isPostingRowEmpty, isPostingRowRequiredRule } from './virtualPostingForm.general';

const getCurrentPosting = (formData, pathToUpdate) => _get(formData, `allValues.${FIELD_IDS.POSTINGS}.${pathToUpdate}`);

const isDepartmentPresent = ({ accountDetails, setupFields }) => {
  const departmentIds = getDepartmentFields(accountDetails, setupFields);
  return isNotEmpty(departmentIds);
};

const isDepartmentFieldRequiredRule =
  ({ accountsMap, setupFields, selectedAccount }) =>
  (columnId, valueToTest, formData = EMPTY_OBJECT) => {
    const { pathToUpdate } = formData;
    const currentPosting = getCurrentPosting(formData, pathToUpdate);
    if (pathToUpdate > FIRST_ROW_INDEX && isPostingRowEmpty(currentPosting)) {
      return { isValid: true };
    }

    const accountDetails = getAccountDetails(selectedAccount, accountsMap);

    const isValuePresent = isDepartmentPresent({ accountDetails, setupFields });

    if (_isEmpty(valueToTest) && isValuePresent) {
      return { isValid: false, message: __('This field is mandatory') };
    }

    return { isValid: true };
  };

const getDepartmentValidators = ({ featureFlags = EMPTY_FEATURE_FLAGS, accountsMap, setupFields, selectedAccount }) => {
  const isDepartmentColumnEnabled = featureFlags.getFlag(FEATURE_FLAGS.IS_BANK_REC_DEPARTMENT_COLUMN_ENABLED);
  if (!isDepartmentColumnEnabled) {
    return EMPTY_OBJECT;
  }
  return {
    [COLUMN_IDS.DEPARTMENT]: [
      isDepartmentFieldRequiredRule({
        accountsMap,
        setupFields,
        selectedAccount,
      }),
    ],
  };
};

const getControlTypeValidators = (featureFlags = EMPTY_FEATURE_FLAGS) => {
  const isControlTagColumnsEnabled = featureFlags.getFlag(FEATURE_FLAGS.IS_CONTROL_TAG_COLUMNS_ENABLED);

  return isControlTagColumnsEnabled
    ? { [COLUMN_IDS.CONTROL_TAGS]: [isPostingRowRequiredRule] }
    : { [COLUMN_IDS.CONTROL_NUMBER]: [isPostingRowRequiredRule] };
};

const getPostingColumnsValidators = ({ featureFlags, accountsMap, setupFields, selectedAccount }) => {
  const controlTypeValidators = getControlTypeValidators(featureFlags);
  const departmentValidators = getDepartmentValidators({ featureFlags, accountsMap, setupFields, selectedAccount });

  const validators = {
    [COLUMN_IDS.ACCOUNTING_DATE]: [isPostingRowRequiredRule],
    [COLUMN_IDS.REFERENCE]: [isPostingRowRequiredRule],
    [COLUMN_IDS.JOURNAL_ID]: [isPostingRowRequiredRule],
    [COLUMN_IDS.AMOUNT]: [isPostingRowRequiredRule],
    ...controlTypeValidators,
    ...departmentValidators,
  };

  return [tableValidator(validators)];
};

export default getPostingColumnsValidators;
