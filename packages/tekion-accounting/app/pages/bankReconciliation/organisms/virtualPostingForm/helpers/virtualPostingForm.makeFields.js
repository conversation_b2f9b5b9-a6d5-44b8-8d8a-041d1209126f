/* eslint-disable import/order */
import { compose } from 'recompose';

// Factories
import getConfigurableFormFieldConfig from 'twidgets/factories/configurableFormFieldConfig/getConfigurableFormFieldConfig';

// Utils
import getAccountOptions from 'utils/getAccountOptions';
import castArrayIfPresent from 'tbase/utils/castArrayIfPresent';

// Helpers
import { makeFormViewSectionByFieldKey } from 'tbusiness/helpers/configurableForm';
import { getFieldDefinition } from 'tbusiness/helpers/configurableForm/configurableForm.fieldDefinition';
import getPostingColumnsValidators from './virtualPostingForm.getPostingColumnsValidators';
import { getAccountDetails } from './virtualPostingForm.general';

// Constants
import { EMPTY_CONFIGURABLE_ENTITY_METADATA } from 'tbusiness/builders/ConfigurableEntityMetadata';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import FIELD_IDS from '../../../constants/bankRec.virtualPostings.fieldIds';
import FIELD_RENDERER_TYPE_TO_CONFIG_MAP from '../constants/virtualPostingForm.fieldRendererTypeToConfigMap';

const getField = ({ fieldId, entityConfig, formViewSectionByFieldKey = EMPTY_OBJECT }) => {
  const formViewSection = formViewSectionByFieldKey[fieldId];
  const fieldDefinition = getFieldDefinition(formViewSection, entityConfig);
  const fieldConfig = getConfigurableFormFieldConfig(FIELD_RENDERER_TYPE_TO_CONFIG_MAP)({
    fieldId,
    fieldDefinition,
    formViewSection,
  });
  return fieldConfig;
};

const getPostingField = ({
  fieldId,
  formViewSectionByFieldKey,
  entityConfig,
  accountsMap,
  setupFields,
  activeJournals,
  accountingSettings,
  selectedAccount,
  programFieldMap,
  lookupAssetSupportMap,
  featureFlags,
}) => {
  const field = getField({ fieldId, formViewSectionByFieldKey, entityConfig });
  field.addToRenderOptions({
    setupFields,
    activeJournals,
    accountingSettings,
    selectedAccount,
    accountsMap,
    programFieldMap,
    lookupAssetSupportMap,
    validators: getPostingColumnsValidators({ featureFlags, accountsMap, setupFields, selectedAccount }),
  });
  return field;
};

const getAccountFieldOptions = compose(getAccountOptions, castArrayIfPresent, getAccountDetails);

const getGlAccountField = ({
  fieldId,
  entityConfig,
  formViewSectionByFieldKey,
  selectedAccount = EMPTY_OBJECT,
  accountsMap = EMPTY_OBJECT,
}) => {
  const accountOptions = getAccountFieldOptions(selectedAccount, accountsMap);

  const field = getField({ fieldId, formViewSectionByFieldKey, entityConfig });
  field.addToRenderOptions({ options: accountOptions, disabled: true, size: 6 });
  return field;
};

const makeFields = ({
  entityMetadata = EMPTY_CONFIGURABLE_ENTITY_METADATA,
  selectedAccount,
  accountsMap,
  setupFields,
  activeJournals,
  accountingSettings,
  programFieldMap,
  lookupAssetSupportMap,
  featureFlags,
}) => {
  const entityConfig = entityMetadata.getEntityConfig();
  const formViewConfig = entityMetadata.getFormViewConfig();
  const formViewSectionByFieldKey = makeFormViewSectionByFieldKey(formViewConfig, entityConfig);

  const glAccountField = getGlAccountField({
    fieldId: FIELD_IDS.GL_ACCOUNT_ID,
    entityConfig,
    formViewSectionByFieldKey,
    selectedAccount,
    accountsMap,
  });

  const postingField = getPostingField({
    fieldId: FIELD_IDS.POSTINGS,
    formViewSectionByFieldKey,
    entityConfig,
    accountsMap,
    setupFields,
    activeJournals,
    accountingSettings,
    selectedAccount,
    programFieldMap,
    lookupAssetSupportMap,
    featureFlags,
  });

  return {
    [FIELD_IDS.GL_ACCOUNT_ID]: glAccountField,
    [FIELD_IDS.POSTINGS]: postingField,
  };
};

export default makeFields;
