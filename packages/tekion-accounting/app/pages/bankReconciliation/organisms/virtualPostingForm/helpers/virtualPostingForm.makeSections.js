// Helpers
import { makeSections as makeBaseSections, addClassesToSections } from 'tbusiness/helpers/configurableForm';

// Constants
import { EMPTY_CONFIGURABLE_ENTITY_METADATA } from 'tbusiness/builders/ConfigurableEntityMetadata';
import { SECTION_IDS } from '../constants/virtualPostingForm.general';

const SECTION_CLASSES_BY_ID = {
  [SECTION_IDS.GENERAL_INFO]: { className: 'm-t-24' },
  [SECTION_IDS.POSTINGS]: { className: 'full-max-width-imp' },
};
const makeSections = (entityMetadata = EMPTY_CONFIGURABLE_ENTITY_METADATA) => {
  const entityConfig = entityMetadata.getEntityConfig();
  const formViewConfig = entityMetadata.getFormViewConfig();
  const sections = makeBaseSections({
    formViewConfig,
    entityConfig,
  });
  const sectionsWithClasses = addClassesToSections(sections, SECTION_CLASSES_BY_ID);
  return sectionsWithClasses;
};

export default makeSections;
