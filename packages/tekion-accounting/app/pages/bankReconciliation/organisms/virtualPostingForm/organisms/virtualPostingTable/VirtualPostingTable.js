/* eslint-disable import/order */
import React, { useMemo, useContext, useCallback } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

// Lodash
import _noop from 'lodash/noop';

// Containers
import withActions from 'tcomponents/connectors/withActions';
import withInputTableActions from 'tcomponents/organisms/inputTable/containers/withInputTableActions';
import withTableShortcutKeys from 'tcomponents/organisms/inputTable/containers/withTableShortcutKeys';
import withSize from 'tcomponents/hoc/withSize';
import withUserPreferenceColumn from 'tcomponents/connectors/withUserPreferenceColumn';

// Hook
import { useTekionConversion } from '@tekion/tekion-conversion-web';

// Context
import FeatureFlagsContext from 'twidgets/context/featureFlags';

// Components
import { VirtualizedInputTable } from 'tcomponents/organisms/inputTable';
import PostingTableHeader from '../../../virtualPostingTableHeader/molecules/postingTableHeader';
import MultiControlInputDrawer from 'twidgets/appServices/accounting/organisms/transactionPostingForm/organisms/postingTable/organisms/multiControlInputDrawer';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import {
  ROW_ACTION_PROPS,
  BANK_REC_VIRTUAL_POSTING_PREFERENCE_ASSET_KEY,
  MULTI_CONTROL_INPUT_COLUMN_INDEX,
} from './constants/virtualPostingTable.general';
import ACTION_TYPES from './constants/virtualPostingTable.actionTypes';
import PROGRAM_FIELD_KEYS from 'tbase/constants/accounting/programFieldKeys';

// Helpers
import {
  getJournalOptions,
  getDepartmentOptions,
  getRowActions,
  getTdProps,
} from './helpers/virtualPostingTable.general';
import { getUpdatedColumns } from './helpers/virtualPostingTable.getUpdatedColumns';
import { getDefaultVirtualPostings } from '../../../../helpers/bankRec.general';
import ACTION_HANDLERS from './helpers/virtualPostingTable.actionHandlers';

// Styles
import s from './virtualPostingTable.module.scss';

const VirtualizedInputTableWithActions = compose(withTableShortcutKeys, withInputTableActions)(VirtualizedInputTable);

const VirtualPostingTable = ({
  value,
  error,
  accountsMap,
  selectedAccount,
  activeJournals,
  accountingSettings,
  columns,
  onAction,
  contentHeight,
  setupFields,
  programFieldMap,
  lookupAssetSupportMap,
}) => {
  const featureFlags = useContext(FeatureFlagsContext);
  const { getFormattedCurrency } = useTekionConversion();

  const journalOptions = useMemo(() => getJournalOptions(activeJournals), [activeJournals]);

  const departmentOptions = useMemo(
    () => getDepartmentOptions({ selectedAccount, setupFields, accountsMap }),
    [selectedAccount, setupFields, accountsMap]
  );

  const updatedColumns = useMemo(() => getUpdatedColumns(columns), [columns]);

  const memoizedGetTdProps = useCallback(
    () =>
      getTdProps({
        journalOptions,
        departmentOptions,
        accountsMap,
        selectedAccount,
        programFieldMap,
      }),
    [journalOptions, departmentOptions, accountsMap, selectedAccount, programFieldMap]
  );

  const style = useMemo(() => ({ height: contentHeight }), [contentHeight]);

  const handleAddNewRow = useCallback(() => {
    onAction({
      type: ACTION_TYPES.ADD_NEW_ROW,
    });
  }, [onAction]);

  return (
    <div className="full-width">
      <PostingTableHeader
        value={value}
        onAddNewRow={handleAddNewRow}
        getFormattedCurrency={getFormattedCurrency}
        featureFlags={featureFlags}
      />
      <VirtualizedInputTableWithActions
        data={value}
        value={value}
        errors={error}
        columns={updatedColumns}
        onAction={onAction}
        getTdProps={memoizedGetTdProps}
        rowActionProps={ROW_ACTION_PROPS}
        getActionsForRow={getRowActions}
        style={style}
        headerClassName={s.stickyHeader}
      />

      <MultiControlInputDrawer
        multiControlInputColumnIndex={MULTI_CONTROL_INPUT_COLUMN_INDEX}
        accountingSettings={accountingSettings}
        controlTypesMap={programFieldMap[PROGRAM_FIELD_KEYS.CONTROL_TYPE]}
        lookupAssetSupportMap={lookupAssetSupportMap}
      />
    </div>
  );
};

VirtualPostingTable.propTypes = {
  value: PropTypes.array,
  error: PropTypes.array,
  accountsMap: PropTypes.object,
  activeJournals: PropTypes.object,
  accountingSettings: PropTypes.object,
  onAction: PropTypes.func,
  contentHeight: PropTypes.number.isRequired,
  columns: PropTypes.array,
  setupFields: PropTypes.object,
  programFieldMap: PropTypes.object,
  lookupAssetSupportMap: PropTypes.object,
  selectedAccount: PropTypes.object,
};

VirtualPostingTable.defaultProps = {
  value: getDefaultVirtualPostings(),
  error: EMPTY_ARRAY,
  accountsMap: EMPTY_OBJECT,
  activeJournals: EMPTY_OBJECT,
  accountingSettings: EMPTY_OBJECT,
  onAction: _noop,
  columns: EMPTY_ARRAY,
  setupFields: EMPTY_OBJECT,
  programFieldMap: EMPTY_OBJECT,
  lookupAssetSupportMap: EMPTY_OBJECT,
  selectedAccount: EMPTY_OBJECT,
};

export default compose(
  withUserPreferenceColumn(BANK_REC_VIRTUAL_POSTING_PREFERENCE_ASSET_KEY),
  withSize({ hasPageHeader: 1, hasPageFooter: 1 }),
  withActions(undefined, ACTION_HANDLERS)
)(VirtualPostingTable);
