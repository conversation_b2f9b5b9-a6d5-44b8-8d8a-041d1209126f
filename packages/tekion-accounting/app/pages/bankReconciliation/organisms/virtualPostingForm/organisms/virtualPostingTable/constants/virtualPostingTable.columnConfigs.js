/* eslint-disable import/order */
import { compose } from 'recompose';

// Lodash
import _property from 'lodash/property';

// Builders
import CellConfig from 'tcomponents/organisms/inputTable/builders/CellConfig';
import ColumnConfig from 'tcomponents/organisms/inputTable/builders/ColumnConfig';

// Utils
import {
  getSelectValueOnChange,
  getTextInputValueOnChange,
  getNumberInputValueOnChange,
  getDateInputValueOnChange,
} from 'tcomponents/organisms/inputTable/utils/onChangeValueAccessors';
import getNonEmptyString from 'tbase/utils/getNonEmptyString';

// Containers
import withCellPopover from 'tcomponents/molecules/withCellPopover';

// Components
import TextInputCellRenderer from 'tcomponents/organisms/inputTable/cellRenderers/TextInputCellRenderer';
import VirtualizedSelectInput from 'tcomponents/organisms/inputTable/cellRenderers/VirtualizedSelectInput';
import SelectInputCellRenderer from 'tcomponents/organisms/inputTable/cellRenderers/SelectInputCellRenderer';
import AmountInputCellRenderer from 'twidgets/appServices/accounting/cellRenderers/amountInputCellRenderer';
import { TenantDealerControlNumberInput } from 'twidgets/appServices/accounting/organisms/interFranchiseJournalEntry/molecules/controlNumberInput';
import DateInput from 'tcomponents/atoms/DateInput/DateInput';

// Constans
import COLUMN_IDS from '../../../../../constants/bankRec.virtualPostingsTable.columnIds';

// Helpers
import {
  getAmountInputValue,
  getControlNumberValueOnChange,
  MultiControlInput,
  getValueForKeyboardShortcuts,
} from 'twidgets/appServices/accounting/organisms/transactionPostingForm';
import {
  getAccountingDateInputProps,
  getDepartmentProps,
  getJournalsProps,
  getMultiControlProps,
} from '../helpers/virtualPostingTable.getColumnProps';

const DateInputRenderer = withCellPopover(DateInput);

const getControl2NumberCellValue = compose(getNonEmptyString, _property(COLUMN_IDS.CONTROL2));

const COLUMN_CONFIGS = {
  [COLUMN_IDS.AMOUNT]: new ColumnConfig(COLUMN_IDS.AMOUNT)
    .addCellConfig(new CellConfig().setComponent(AmountInputCellRenderer))
    .setGetValueFromOnChange(getAmountInputValue)
    .setWidth(200)
    .setHeaderRightAlign()
    .setHeader(__('Amount'))
    .setAccessor(_property(COLUMN_IDS.AMOUNT)),
  [COLUMN_IDS.CONTROL_NUMBER]: new ColumnConfig(COLUMN_IDS.CONTROL_NUMBER)
    .addCellConfig(new CellConfig().setComponent(TenantDealerControlNumberInput))
    .setGetValueFromOnChange(getControlNumberValueOnChange)
    .setHeader(__('Control'))
    .setAccessor(_property(COLUMN_IDS.CONTROL_NUMBER))
    .setGetValueForKeyboardShortcuts(getValueForKeyboardShortcuts),
  [COLUMN_IDS.CONTROL2]: new ColumnConfig(COLUMN_IDS.CONTROL2)
    .addCellConfig(new CellConfig().setComponent(TextInputCellRenderer))
    .setAccessor(getControl2NumberCellValue)
    .setGetValueFromOnChange(getTextInputValueOnChange)
    .setHeader(__('Control 2')),
  [COLUMN_IDS.DESCRIPTION]: new ColumnConfig(COLUMN_IDS.DESCRIPTION)
    .addCellConfig(new CellConfig().setComponent(TextInputCellRenderer))
    .setGetValueFromOnChange(getTextInputValueOnChange)
    .setHeader(__('Description'))
    .setAccessor(_property(COLUMN_IDS.DESCRIPTION)),
  [COLUMN_IDS.CONTROL_TAGS]: new ColumnConfig(COLUMN_IDS.CONTROL_TAGS)
    .addCellConfig(
      new CellConfig().setComponent(MultiControlInput).setMapCellPropsToComponentProps(getMultiControlProps)
    )
    .setGetValueFromOnChange(getNumberInputValueOnChange)
    .setHeader(__('Control/Tags'))
    .setAccessor(_property(COLUMN_IDS.CONTROL_TAGS))
    .setMinWidth(80),
  [COLUMN_IDS.REFERENCE]: new ColumnConfig(COLUMN_IDS.REFERENCE)
    .addCellConfig(new CellConfig().setComponent(TextInputCellRenderer))
    .setGetValueFromOnChange(getTextInputValueOnChange)
    .setHeader(__('Reference'))
    .setAccessor(_property(COLUMN_IDS.REFERENCE)),
  [COLUMN_IDS.ACCOUNTING_DATE]: new ColumnConfig(COLUMN_IDS.ACCOUNTING_DATE)
    .addCellConfig(
      new CellConfig().setComponent(DateInputRenderer).setMapCellPropsToComponentProps(getAccountingDateInputProps)
    )
    .setGetValueFromOnChange(getDateInputValueOnChange)
    .setHeader(__('Accounting Date'))
    .setAccessor(_property(COLUMN_IDS.ACCOUNTING_DATE)),
  [COLUMN_IDS.JOURNAL_ID]: new ColumnConfig(COLUMN_IDS.JOURNAL_ID)
    .addCellConfig(
      new CellConfig().setComponent(VirtualizedSelectInput).setMapCellPropsToComponentProps(getJournalsProps)
    )
    .setGetValueFromOnChange(getSelectValueOnChange)
    .setHeader(__('Journal'))
    .setAccessor(_property(COLUMN_IDS.JOURNAL_ID)),
  [COLUMN_IDS.DEPARTMENT]: new ColumnConfig(COLUMN_IDS.DEPARTMENT)
    .addCellConfig(
      new CellConfig().setComponent(SelectInputCellRenderer).setMapCellPropsToComponentProps(getDepartmentProps)
    )
    .setGetValueFromOnChange(getTextInputValueOnChange)
    .setGetValueFromOnChange(getSelectValueOnChange)
    .setHeader(__('Department'))
    .setAccessor(_property(COLUMN_IDS.DEPARTMENT)),
};

export default COLUMN_CONFIGS;
