// Lodash
import _noop from 'lodash/noop';
import _size from 'lodash/size';

// Utils
import updateElementAtIndex from 'tbase/utils/updateElementAtIndex';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import FORM_ACTION_TYPES from 'tcomponents/organisms/FormBuilder/constants/actionTypes';
import TABLE_ACTION_TYPES from 'tcomponents/molecules/tableInputField/constants/TableInputField.actionTypes';
import ACTION_TYPES from '../constants/virtualPostingTable.actionTypes';

// Helpers
import { getPostingRowWithUniqueId } from '../../../../../helpers/bankRec.general';
import ROW_ACTION_HANDLERS from './virtualPostingTable.rowActionHandlers';

const handleFieldChange = ({ params = EMPTY_OBJECT, getState }) => {
  const { value = EMPTY_OBJECT, onChange = _noop } = getState();
  const { additional } = params;
  const pathToUpdate = additional?.pathToUpdate;
  const updatedRowValue = additional?.updatedValue;
  let updatedPostings = updateElementAtIndex(value, updatedRowValue, pathToUpdate);

  if (_size(value) - 1 === pathToUpdate) {
    updatedPostings = [...updatedPostings, getPostingRowWithUniqueId()];
  }

  return onChange(updatedPostings);
};

const handleAddRow = ({ getState }) => {
  const { value = EMPTY_OBJECT, onChange = _noop } = getState() || EMPTY_OBJECT;
  const postingsWithNewRow = [...value, getPostingRowWithUniqueId()];

  return onChange(postingsWithNewRow);
};

const handleRowActionClick = ({ getState, params = EMPTY_OBJECT }) => {
  const { actionType } = params;
  const actionHandler = ROW_ACTION_HANDLERS[actionType] || _noop;

  return actionHandler({ params, getState });
};

const ACTION_HANDLERS = {
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: handleFieldChange,
  [ACTION_TYPES.ADD_NEW_ROW]: handleAddRow,
  [TABLE_ACTION_TYPES.TABLE_ACTION_CLICK]: handleRowActionClick,
};

export default ACTION_HANDLERS;
