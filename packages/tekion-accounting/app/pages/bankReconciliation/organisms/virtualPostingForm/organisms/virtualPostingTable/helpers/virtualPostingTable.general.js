/* eslint-disable import/order */
// Lodash
import _constant from 'lodash/constant';

// Utils
import standardFieldOptionMapper from 'tbase/utils/optionMappers/standardFieldMapper';
import customFieldOptionMapper from 'tbase/utils/optionMappers/customFieldMappers';

// Helpers
import { getDepartmentFields } from 'twidgets/appServices/accounting/organisms/configurableTransaction';
import { getAccountDetails } from '../../../helpers/virtualPostingForm.general';

// Constants
import { ROW_ACTIONS } from '../constants/virtualPostingTable.general';
import SETUP_FIELDS_KEYS from 'tbase/constants/accounting/setupFieldKeys';

export const getRowActions = _constant(ROW_ACTIONS);

export const getJournalOptions = activeJournals => standardFieldOptionMapper('JOURNAL', activeJournals);

export const getDepartmentOptions = ({ selectedAccount, accountsMap, setupFields }) => {
  const accountDetails = getAccountDetails(selectedAccount, accountsMap);
  const departmentFields = getDepartmentFields(accountDetails, setupFields);

  return customFieldOptionMapper(SETUP_FIELDS_KEYS.DEPARTMENT, departmentFields);
};

export const getTdProps = ({ journalOptions, departmentOptions, accountsMap, selectedAccount, programFieldMap }) => ({
  journalOptions,
  departmentOptions,
  accountsMap,
  selectedAccount,
  programFieldMap,
});
