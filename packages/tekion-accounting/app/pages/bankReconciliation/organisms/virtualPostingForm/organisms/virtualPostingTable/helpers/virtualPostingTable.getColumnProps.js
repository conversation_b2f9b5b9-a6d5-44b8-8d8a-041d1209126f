// Lodash
import _constant from 'lodash/constant';

// Utils
import { toMoment, isAfter } from 'tbase/utils/dateUtils';

// Constants
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import PROGRAM_FIELD_KEYS from 'tbase/constants/accounting/programFieldKeys';

// Helpers
import { getAccountDetails } from '../../../helpers/virtualPostingForm.general';

export const getJournalsProps = props => {
  const { journalOptions = EMPTY_ARRAY } = props;
  return { options: journalOptions };
};

export const getDepartmentProps = props => {
  const { departmentOptions = EMPTY_ARRAY } = props;
  return { options: departmentOptions };
};

export const getMultiControlProps = props => {
  const { programFieldMap = EMPTY_OBJECT, selectedAccount, accountsMap } = props;
  const controlTypesMap = programFieldMap[PROGRAM_FIELD_KEYS.CONTROL_TYPE];
  const accountDetails = getAccountDetails(selectedAccount, accountsMap);

  return {
    glAccount: accountDetails,
    controlTypesMap,
  };
};

const isAccountingDateDisabled = currentMomment => isAfter(toMoment(), currentMomment);

export const getAccountingDateInputProps = _constant({
  format: DATE_TIME_FORMAT.BASE,
  showErrorMsg: false,
  disabledDate: isAccountingDateDisabled,
});
