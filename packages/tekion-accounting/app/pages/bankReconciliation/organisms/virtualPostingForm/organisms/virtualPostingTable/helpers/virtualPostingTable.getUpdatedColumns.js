// Lodash
import _map from 'lodash/map';

// Readers
import columnConfigReader from 'tbusiness/appServices/accounting/readers/configurableTable/ColumnConfig';

// Constants
import COLUMN_CONFIGS from '../constants/virtualPostingTable.columnConfigs';

const makeUpdatedPreferenceColumnsForInputTable = column => {
  const key = columnConfigReader.key(column);
  return COLUMN_CONFIGS[key];
};

export const getUpdatedColumns = columns => _map(columns, makeUpdatedPreferenceColumnsForInputTable);
