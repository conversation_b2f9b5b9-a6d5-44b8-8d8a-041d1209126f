// Lodash
import _noop from 'lodash/noop';
import _size from 'lodash/size';

// Utils
import removeElementAtIndex from 'utils/removeElementAtIndex';
import addElementAtIndex from 'tbase/utils/accounting/addElementAtIndex';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { REMOVE_ACTION, ADD_ROW_ACTION } from 'tcomponents/molecules/tableInputField/constants/general';
import { FIRST_ROW_INDEX } from '../../../constants/virtualPostingForm.general';

// Helpers
import { getDefaultVirtualPostings, getPostingRowWithUniqueId } from '../../../../../helpers/bankRec.general';

const removeRow = ({ params = EMPTY_OBJECT, getState = _noop }) => {
  const { value = EMPTY_OBJECT, onChange = _noop } = getState() || EMPTY_OBJECT;
  const rowIndex = params?.nestingPath?.[0];
  const isFirstRowAndOnlyRow = rowIndex === FIRST_ROW_INDEX && _size(value) === 1;

  if (isFirstRowAndOnlyRow) {
    const defaultRow = getDefaultVirtualPostings();
    return onChange(defaultRow);
  }

  const updatedRows = removeElementAtIndex(value, rowIndex);
  return onChange(updatedRows);
};

const addNewRowAtIndex = ({ params = EMPTY_OBJECT, getState = _noop }) => {
  const { value = EMPTY_OBJECT, onChange = _noop } = getState() || EMPTY_OBJECT;
  const rowIndex = params?.nestingPath?.[0];
  const newRow = getPostingRowWithUniqueId();
  const updatedRows = addElementAtIndex(value, newRow, rowIndex + 1);

  return onChange(updatedRows);
};

const ROW_ACTION_HANDLERS = {
  [REMOVE_ACTION.id]: removeRow,
  [ADD_ROW_ACTION.id]: addNewRowAtIndex,
};

export default ROW_ACTION_HANDLERS;
