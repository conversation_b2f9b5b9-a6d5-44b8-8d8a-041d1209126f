/* eslint import/order: 0 */
import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

// Lodash
import _noop from 'lodash/noop';

// Helpers
import { getTotalAmount } from './postingTableHeader.helpers';

// Components
import Button from 'tcomponents/atoms/Button';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import FEATURE_FLAGS from 'tbusiness/appServices/accounting/constants/featureFlags';
import { REACT_POST_PROCESSOR_NAME } from '@tekion/tekion-i18n/constants/i18next.postProcessor';

// Context
import { FeatureFlags, EMPTY_FEATURE_FLAGS } from 'twidgets/context/featureFlags';

// Styles
import styles from './postingTableHeader.module.scss';

const PostingTableHeader = props => {
  const { value: postings, featureFlags, getFormattedCurrency, onAddNewRow } = props;
  const totalAmount = useMemo(() => getTotalAmount(postings, getFormattedCurrency), [postings, getFormattedCurrency]);

  const isConfigurableAccountingEnabled = featureFlags.getFlag(FEATURE_FLAGS.CONFIGURABLE_ACCOUNTING_ENABLED);

  return (
    <div className={cx(styles.headerContainer, 'flex align-items-center justify-content-between m-b-8')}>
      <div className={cx(styles.headerContent, 'flex align-items-center')}>
        {__('Total Amount: <total>', {
          postProcess: REACT_POST_PROCESSOR_NAME,
          total: <div className={styles.total}>&nbsp;{totalAmount}</div>,
        })}
      </div>
      {!isConfigurableAccountingEnabled && (
        <Button view={Button.VIEW.SECONDARY} label={__('Add New')} onClick={onAddNewRow} />
      )}
    </div>
  );
};

PostingTableHeader.propTypes = {
  value: PropTypes.array,
  getFormattedCurrency: PropTypes.func,
  onAddNewRow: PropTypes.func,
  featureFlags: PropTypes.instanceOf(FeatureFlags),
};

PostingTableHeader.defaultProps = {
  value: EMPTY_ARRAY,
  getFormattedCurrency: _noop,
  onAddNewRow: _noop,
  featureFlags: EMPTY_FEATURE_FLAGS,
};

export default PostingTableHeader;
