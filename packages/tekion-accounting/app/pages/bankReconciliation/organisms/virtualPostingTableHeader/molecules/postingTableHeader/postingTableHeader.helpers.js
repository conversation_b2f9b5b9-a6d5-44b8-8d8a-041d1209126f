// Lodash
import _reduce from 'lodash/reduce';
import _noop from 'lodash/noop';

// Utils
import moneyUtils from 'tbase/utils/money';

// Constatns
import COLUMN_IDS from '../../../../constants/posting.columnIds';

const addTotalAmount = (sum, posting) => {
  const amount = posting?.[COLUMN_IDS.AMOUNT_COLUMN];
  return moneyUtils.add(sum, amount);
};

export const getTotalAmount = (postings, getFormattedCurrency = _noop) => {
  const total = _reduce(postings, addTotalAmount, 0);
  return getFormattedCurrency(total);
};
