/* eslint-disable import/order */
import React, { useMemo, useContext, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';

// Lodash
import _noop from 'lodash/noop';

// Context
import SetupFieldsContext from 'twidgets/appServices/accounting/context/SetupFieldContext';

// Utils
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';

// Containers
import withActions from 'tcomponents/connectors/withActions';

// Selectors
import {
  getAllJournalsMap,
  getAccountsMap,
  getDealerInfo,
  isPostingAllowedForUser,
  getCurrentActiveMonth,
  getActiveYear,
  getAccountingSettings,
  getApSetup,
  getArSetup,
  getProgramFieldMap,
  getLookupAssetSupportMap,
} from 'reducers/selectors/app.selectors';

// Components
import TransactionFooter from 'twidgets/appServices/accounting/molecules/transactionFooter';
import { TransactionPageHeader } from 'molecules/transactionPageHeaders';
import {
  ConfigurableEditTransaction,
  TRANSACTION_FIELD_ID,
  getSections,
} from 'twidgets/appServices/accounting/organisms/configurableTransaction';

// Helpers
import { getCombinedApArAccountIds } from 'helpers/apArAccounts';
import getFields from './helpers/reallocatePayment.fields';
import { getFooterProps, getHeaderProps } from '../../helpers/walletJournalEntry/walletJournalEntry.general';
import { makeInitialState } from './helpers/realloactePayment.initialState';
import { getReallocationGlAccountOptions } from 'helpers/apSetup.helper';

// Action Handlers
import ACTION_HANDLERS from './reallocatePayment.actionHandlers';

// Constants
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { CONTEXT_ID } from './constants/reallocatePayment.general';
import ACTION_TYPES from './constants/reallocatePayment.actionTypes';

// Readers
import paymentReader from 'tbase/readers/Payment';

const handleSubmit = () => {
  triggerSubmit(CONTEXT_ID);
};

const ReallocatePayment = props => {
  const { entityMetadata, values, errors, onAction, isSubmitting, reallocationPayment, initialPostings } = props;

  const setupFields = useContext(SetupFieldsContext);

  const handleCancel = useCallback(() => {
    onAction({
      type: ACTION_TYPES.ON_CANCEL,
    });
  }, [onAction]);

  const referenceType = values[TRANSACTION_FIELD_ID.REFERENCE_TYPE];
  const sections = useMemo(() => getSections(entityMetadata, referenceType), [entityMetadata, referenceType]);

  const journalsMap = useSelector(getAllJournalsMap);
  const accountsMap = useSelector(getAccountsMap);
  const dealerInfo = useSelector(getDealerInfo);
  const accountingSettings = useSelector(getAccountingSettings);
  const programFieldMap = useSelector(getProgramFieldMap);
  const lookupAssetSupportMap = useSelector(getLookupAssetSupportMap);
  const activeMonth = useSelector(getCurrentActiveMonth);
  const activeYear = useSelector(getActiveYear);
  const postingAllowedForUser = useSelector(isPostingAllowedForUser);
  const apSetup = useSelector(getApSetup);
  const arSetup = useSelector(getArSetup);

  const excludedAccountIds = useMemo(() => getCombinedApArAccountIds(apSetup, arSetup), [apSetup, arSetup]);
  const additionalTdProps = useMemo(
    () => ({
      reallocationGlAccountOptions: getReallocationGlAccountOptions(accountsMap, apSetup),
    }),
    [accountsMap, apSetup]
  );

  const fields = getFields({
    entityMetadata,
    values,
    journalsMap,
    accountsMap,
    dealerInfo,
    accountingSettings,
    programFieldMap,
    lookupAssetSupportMap,
    excludedAccountIds,
    activeMonth,
    activeYear,
    postingAllowedForUser,
    setupFields,
    apSetup,
    paymentDate: paymentReader.paymentDate(reallocationPayment),
    additionalTdProps,
    initialPostings,
  });

  const footerProps = getFooterProps(isSubmitting, handleSubmit, handleCancel);
  const headerProps = getHeaderProps();

  return (
    <ConfigurableEditTransaction
      headerComponent={TransactionPageHeader}
      headerProps={headerProps}
      footerComponent={TransactionFooter}
      footerProps={footerProps}
      values={values}
      errors={errors}
      formFields={fields}
      sections={sections}
      contextId={CONTEXT_ID}
      onAction={onAction}
      journalsMap={journalsMap}
      accountsMap={accountsMap}
      setupFields={setupFields}
      useKeyBoardShortcuts
      showFooter
    />
  );
};

ReallocatePayment.propTypes = {
  entityMetadata: PropTypes.object.isRequired,
  values: PropTypes.object,
  errors: PropTypes.object,
  onAction: PropTypes.func,
  isSubmitting: PropTypes.bool,
  reallocationPayment: PropTypes.object,
  initialPostings: PropTypes.array,
};

ReallocatePayment.defaultProps = {
  values: EMPTY_OBJECT,
  errors: EMPTY_OBJECT,
  onAction: _noop,
  isSubmitting: false,
  reallocationPayment: EMPTY_OBJECT,
  initialPostings: EMPTY_ARRAY,
};

export default withActions(makeInitialState, ACTION_HANDLERS)(ReallocatePayment);
