/* eslint-disable import/order */
import React from 'react';
import PropTypes from 'prop-types';
import { connect, useSelector } from 'react-redux';
import { compose } from 'recompose';

// Containers
import withRouteParams from 'tcomponents/connectors/withRouteParams';

// Components
import Loader from 'tcomponents/molecules/loader';
import ReallocatePayment from './ReallocatePayment';

// Constants
import { ACCOUNTING } from 'tbase/constants/appServices';
import ASSET_TYPES_BY_MODULE from 'tbusiness/constants/configurableEntity/assetTypesByModule';

// Action
import { getConfigurableEntityMetadata as getConfigurableEntityMetadataAction } from 'tbusiness/actions/configurableEntity';

// Builders
import ConfigurableEntityMetadata from 'tbusiness/builders/ConfigurableEntityMetadata';

// Selectors
import { getConfigurableEntityMetadata as getConfigurableEntityMetadataSelector } from 'tbusiness/reducers/configurableEntity';
import { getAccountsMapByDealerId, getJournals } from 'reducers/selectors/app.selectors';

// Hooks
import useReallocatePaymentDetails from './hooks/useReallocatePaymentDetails';

const ReallocatePaymentContainer = props => {
  const { paymentId, entityMetadata, getConfigurableEntitiesMetadata } = props;

  const { isLoading, reallocationPayment, reallocationPaymentPostings } = useReallocatePaymentDetails({
    paymentId,
    entityMetadata,
    getConfigurableEntitiesMetadata,
  });

  const accountsMapByDealerId = useSelector(getAccountsMapByDealerId);
  const journals = useSelector(getJournals);

  if (isLoading) {
    return <Loader />;
  }

  return (
    <ReallocatePayment
      {...props}
      reallocationPayment={reallocationPayment}
      reallocationPaymentPostings={reallocationPaymentPostings}
      accountsMapByDealerId={accountsMapByDealerId}
      journals={journals}
    />
  );
};

const mapStateToProps = state => ({
  entityMetadata: getConfigurableEntityMetadataSelector(
    state,
    ASSET_TYPES_BY_MODULE[ACCOUNTING].AP_TRANSACTION_POSTING
  ),
});

const mapDispatchToProps = {
  getConfigurableEntitiesMetadata: getConfigurableEntityMetadataAction,
};

ReallocatePaymentContainer.propTypes = {
  entityMetadata: PropTypes.instanceOf(ConfigurableEntityMetadata).isRequired,
  getConfigurableEntitiesMetadata: PropTypes.func.isRequired,
  paymentId: PropTypes.string.isRequired,
};

export default compose(connect(mapStateToProps, mapDispatchToProps), withRouteParams)(ReallocatePaymentContainer);
