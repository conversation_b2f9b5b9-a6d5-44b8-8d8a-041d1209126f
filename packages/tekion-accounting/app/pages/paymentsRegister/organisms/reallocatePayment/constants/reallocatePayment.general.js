// Constants
import { REMOVE_ACTION, ADD_ROW_ACTION } from 'tcomponents/molecules/tableInputField/constants/general';
import { POSTING_TABLE_COLUMN_ID } from 'twidgets/appServices/accounting/organisms/configurableTransaction';

export const COLUMN_IDS_TO_FILTER = [POSTING_TABLE_COLUMN_ID.COUNT];

export const ROW_ACTIONS = [ADD_ROW_ACTION, REMOVE_ACTION];

export const CONTEXT_ID = 'REALLOCATE_PAYMENT';

export const REALLOCATION_ACCOUNT_POSTING_LINE_INDEX = 0;
