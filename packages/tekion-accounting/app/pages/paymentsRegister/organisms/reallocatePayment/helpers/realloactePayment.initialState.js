// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
// Helpers
import { TRANSACTION_FIELD_ID } from 'twidgets/appServices/accounting/organisms/configurableTransaction';

// Helpers
import { createReallocatePaymentInitialValues } from './reallocatePayment.initialValues';

export const makeInitialState = props => {
  const { reallocationPayment, reallocationPaymentPostings, accountsMapByDealerId, journals } = props;

  const initialValues = createReallocatePaymentInitialValues({
    reallocationPayment,
    reallocationPaymentPostings,
    accountsMapByDealerId,
    journals,
  });

  return {
    values: initialValues,
    errors: EMPTY_OBJECT,
    isSubmitting: false,
    initialPostings: initialValues[TRANSACTION_FIELD_ID.POSTINGS],
  };
};
