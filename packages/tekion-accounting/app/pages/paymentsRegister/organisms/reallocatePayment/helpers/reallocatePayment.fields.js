/* eslint-disable import/order */
// Utils
import standardFieldOptionMapper from 'tbase/utils/optionMappers/standardFieldMapper';

// Helpers
import {
  getFields as getBaseFields,
  TRANSACTION_FIELD_ID as TRANSACTION_POSTING_FIELD_IDS,
} from 'twidgets/appServices/accounting/organisms/configurableTransaction';
import { getCashPaymentJournals } from 'helpers/apSetup.helper';
import isPostingDateDisabled from 'tbase/utils/accounting/isPostingDateDisabled';
import { COLUMN_ID_TO_PROPS_GETTER } from './reallocatePayment.postingColumnProps';
import { getRowActions } from './reallocatePayment.general';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { COLUMN_IDS_TO_FILTER } from '../constants/reallocatePayment.general';

const shouldDisableAccountingDate =
  ({ paymentDate, activeMonth, activeYear, postingAllowedForUser }) =>
  currentMoment => {
    const disabledByPostingDate = isPostingDateDisabled({
      activeMonth,
      activeYear,
      canUserPostInActiveMonth: postingAllowedForUser,
    });
    return currentMoment.isBefore(paymentDate, 'day') || disabledByPostingDate(currentMoment);
  };

const overrideBaseFields = ({
  baseFields = EMPTY_OBJECT,
  excludedAccountIds,
  cashPaymentJournals,
  paymentDate,
  activeMonth,
  activeYear,
  postingAllowedForUser,
  additionalTdProps,
  initialPostings,
}) => {
  const postingsField = baseFields[TRANSACTION_POSTING_FIELD_IDS.POSTINGS] || EMPTY_OBJECT;
  const refTypeField = baseFields[TRANSACTION_POSTING_FIELD_IDS.REFERENCE_TYPE] || EMPTY_OBJECT;
  const refTextField = baseFields[TRANSACTION_POSTING_FIELD_IDS.REFERENCE_TEXT] || EMPTY_OBJECT;
  const accountingDateField = baseFields[TRANSACTION_POSTING_FIELD_IDS.ACCOUNTING_DATE] || EMPTY_OBJECT;
  const journalField = baseFields[TRANSACTION_POSTING_FIELD_IDS.JOURNAL_ID] || EMPTY_OBJECT;

  return {
    ...baseFields,
    [TRANSACTION_POSTING_FIELD_IDS.REFERENCE_TYPE]: refTypeField.addToRenderOptions({
      disabled: true,
    }),
    [TRANSACTION_POSTING_FIELD_IDS.REFERENCE_TEXT]: refTextField.addToRenderOptions({
      disabled: true,
    }),
    [TRANSACTION_POSTING_FIELD_IDS.JOURNAL_ID]: journalField.addToRenderOptions({
      options: standardFieldOptionMapper('JOURNAL', cashPaymentJournals),
    }),
    [TRANSACTION_POSTING_FIELD_IDS.POSTINGS]: postingsField.addToRenderOptions({
      excludedAccountIds,
      getActionsForRow: getRowActions,
      columnIdToPropsGetter: COLUMN_ID_TO_PROPS_GETTER,
      columnIdsToFilter: COLUMN_IDS_TO_FILTER,
      additionalTdProps,
      initialPostings,
    }),
    [TRANSACTION_POSTING_FIELD_IDS.ACCOUNTING_DATE]: accountingDateField.addToRenderOptions({
      disabledDate: shouldDisableAccountingDate({ paymentDate, activeMonth, activeYear, postingAllowedForUser }),
    }),
  };
};

const getFields = ({
  entityMetadata,
  journalsMap,
  accountsMap,
  dealerInfo,
  accountingSettings,
  values,
  programFieldMap,
  lookupAssetSupportMap,
  excludedAccountIds,
  activeMonth,
  activeYear,
  postingAllowedForUser,
  setupFields,
  apSetup,
  paymentDate,
  additionalTdProps,
  initialPostings,
}) => {
  const baseFields = getBaseFields({
    entityMetadata,
    journalsMap,
    programFieldMap,
    accountsMap,
    dealerInfo,
    values,
    accountingSettings,
    lookupAssetSupportMap,
    activeMonth,
    activeYear,
    postingAllowedForUser,
    setupFields,
  });

  const cashPaymentJournals = getCashPaymentJournals(journalsMap, apSetup);

  const fields = overrideBaseFields({
    baseFields,
    excludedAccountIds,
    cashPaymentJournals,
    paymentDate,
    activeMonth,
    activeYear,
    postingAllowedForUser,
    additionalTdProps,
    initialPostings,
  });
  return fields;
};

export default getFields;
