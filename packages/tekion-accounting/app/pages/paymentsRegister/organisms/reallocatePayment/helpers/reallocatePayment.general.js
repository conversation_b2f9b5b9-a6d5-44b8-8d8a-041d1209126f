// Lodash
import _size from 'lodash/size';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import { ROW_ACTIONS } from '../constants/reallocatePayment.general';

export const shouldDisablePosting = (initialPostings, rowIndex) => {
  const totalPostingsToDisable = _size(initialPostings);
  return rowIndex < totalPostingsToDisable;
};

export const getRowActions = ({ initialPostings, rowIndex }) => {
  const shouldDisable = shouldDisablePosting(initialPostings, rowIndex);
  return shouldDisable ? EMPTY_ARRAY : ROW_ACTIONS;
};
