// Utils
import { getCurrentTime } from 'tbase/utils/dateUtils';
import castArrayIfPresent from 'tbase/utils/castArrayIfPresent';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import { REFERENCE_TYPES } from 'tbase/constants/accounting/referenceTypes';

// Helpers
import {
  TRANSACTION_FIELD_ID,
  makePostingsInitialValues,
} from 'twidgets/appServices/accounting/organisms/configurableTransaction';
import { getDocumentTypeId } from 'twidgets/appServices/accounting/organisms/transactionPostingForm';

// Readers
import paymentReader from 'tbusiness/appServices/accounting/readers/Payment';

export const createReallocatePaymentInitialValues = ({
  reallocationPayment,
  reallocationPaymentPostings = EMPTY_ARRAY,
  accountsMapByDealerId,
  journals,
}) => {
  const paymentId = paymentReader.id(reallocationPayment);
  const paymentNumber = paymentReader.paymentNumber(reallocationPayment);
  const paymentJournalId = paymentReader.journalId(reallocationPayment);
  const dealerId = paymentReader.franchise(reallocationPayment);

  const postingsInitialValues = makePostingsInitialValues({
    postings: reallocationPaymentPostings,
    accountsMapByDealerId,
    parentRefText: paymentNumber,
  });

  return {
    [TRANSACTION_FIELD_ID.FRANCHISE]: castArrayIfPresent(dealerId),
    [TRANSACTION_FIELD_ID.JOURNAL_ID]: castArrayIfPresent(paymentJournalId),
    [TRANSACTION_FIELD_ID.DOCUMENT_TYPE]: getDocumentTypeId(journals, paymentJournalId),
    [TRANSACTION_FIELD_ID.REFERENCE_TYPE]: castArrayIfPresent(REFERENCE_TYPES.CUSTOM),
    [TRANSACTION_FIELD_ID.REFERENCE_ID]: castArrayIfPresent(paymentId),
    [TRANSACTION_FIELD_ID.REFERENCE_TEXT]: paymentNumber,
    [TRANSACTION_FIELD_ID.ACCOUNTING_DATE]: getCurrentTime(),
    [TRANSACTION_FIELD_ID.POSTINGS]: postingsInitialValues,
  };
};
