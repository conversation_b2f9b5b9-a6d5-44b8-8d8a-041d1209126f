// Utils
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import toastAPIError from 'utils/toastAPIError';

// Readers
import paymentReader from 'tbusiness/appServices/accounting/readers/Payment';

// Services
import { reallocatePayment as rellocatePaymentService } from '../../../services/reallocatePayment';

// Helpers
import { redirectToPaymentRegisterList } from '../../../helpers/paymentsRegister.general';

const createReallocatePaymentRequest = transaction => {
  const request = transaction;
  return request;
};

export const processPaymentReallocation = async ({ setState, navigate, reallocationPayment, transaction }) => {
  try {
    setState({ isSubmitting: true });

    const request = createReallocatePaymentRequest(transaction);
    const paymentId = paymentReader.id(reallocationPayment);
    await rellocatePaymentService(paymentId, request);

    setState({ isSubmitting: false });
    toaster(TOASTER_TYPE.SUCCESS, __('Payment reallocation completed successfully'));
    redirectToPaymentRegisterList(navigate);
  } catch (error) {
    toastAPIError(error, __('Failed to reallocate payment'));
    setState({ isSubmitting: false });
  }
};
