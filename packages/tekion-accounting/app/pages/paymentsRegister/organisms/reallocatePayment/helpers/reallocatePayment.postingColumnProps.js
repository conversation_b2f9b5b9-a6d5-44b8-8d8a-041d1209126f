// Readers

// Constants
import { POSTING_TABLE_COLUMN_ID } from 'twidgets/appServices/accounting/organisms/configurableTransaction';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { REALLOCATION_ACCOUNT_POSTING_LINE_INDEX } from '../constants/reallocatePayment.general';

// Helpers
import { shouldDisablePosting } from './reallocatePayment.general';

const getAccountColumnProps = cellProps => {
  const { initialPostings, rowIndex, additionalTdProps = EMPTY_OBJECT } = cellProps;

  if (rowIndex === REALLOCATION_ACCOUNT_POSTING_LINE_INDEX) {
    const { reallocationGlAccountOptions } = additionalTdProps;
    return {
      options: reallocationGlAccountOptions,
    };
  }

  return {
    isDisabled: shouldDisablePosting(initialPostings, rowIndex),
  };
};

const getAmountColumnProps = cellProps => {
  const { initialPostings, rowIndex } = cellProps;
  return {
    disabled: shouldDisablePosting(initialPostings, rowIndex),
  };
};

const getControlColumnProps = cellProps => {
  const { initialPostings, rowIndex } = cellProps;
  return {
    disabled: shouldDisablePosting(initialPostings, rowIndex),
  };
};

export const COLUMN_ID_TO_PROPS_GETTER = {
  [POSTING_TABLE_COLUMN_ID.GL_ACCOUNT]: getAccountColumnProps,
  [POSTING_TABLE_COLUMN_ID.AMOUNT]: getAmountColumnProps,
  [POSTING_TABLE_COLUMN_ID.CONTROL_NUMBER]: getControlColumnProps,
};
