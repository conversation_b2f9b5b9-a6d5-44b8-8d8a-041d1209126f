// Utils
import getDataFromResponse from 'utils/getDataFromResponse';
import toastAPIError from 'utils/toastAPIError';

// Services
import { fetchCheckById as fetchPaymentById } from 'services/checkServices';
import { fetchReallocationPaymentPostings } from '../../../../services/reallocatePayment';

const getReallocationPaymentPostings = paymentId =>
  fetchReallocationPaymentPostings(paymentId).then(getDataFromResponse);

const getPaymentById = paymentId => fetchPaymentById(paymentId).then(getDataFromResponse);

export const fetchReallocatePaymentDetails = async ({
  paymentId,
  setReallocationPaymentPostings,
  setReallocationPayment,
  setReallocatePaymentDetailsLoaded,
  skipPaymentFetch = false,
}) => {
  try {
    if (skipPaymentFetch) {
      const reallocationPaymentPostings = await getReallocationPaymentPostings(paymentId);
      setReallocationPaymentPostings(reallocationPaymentPostings);
    } else {
      const [reallocationPaymentPostings, reallocationPayment] = await Promise.all([
        getReallocationPaymentPostings(paymentId),
        getPaymentById(paymentId),
      ]);
      setReallocationPaymentPostings(reallocationPaymentPostings);
      setReallocationPayment(reallocationPayment);
    }
  } catch (err) {
    toastAPIError(err, __('Failed to fetch reallocate payment details'));
  } finally {
    setReallocatePaymentDetailsLoaded();
  }
};
