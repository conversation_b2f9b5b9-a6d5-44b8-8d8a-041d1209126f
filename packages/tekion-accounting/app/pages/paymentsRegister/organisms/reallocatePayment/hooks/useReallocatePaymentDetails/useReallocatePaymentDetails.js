import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';

// Hooks
import useConfigurableEntityMetadata from 'twidgets/hooks/useConfigurableEntityMetadata';
import useLoading from 'tcomponents/hooks/useLoading';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import { ACCOUNTING } from 'tbase/constants/appServices';
import ASSET_TYPES_BY_MODULE from 'tbusiness/constants/configurableEntity/assetTypesByModule';

// Selectors
import { defaultGetById } from 'tcomponents/reducers/tableItemReducer';
import { getPaymentsRegisterTableItems } from '../../../../reducers/selectors/paymentsRegister.selectors';

// Helpers
import { fetchReallocatePaymentDetails } from './useReallocatePaymentDetails.helpers';

const getPaymentFromStore = (paymentsRegisterListItems, paymentId) => {
  const paymentsById = defaultGetById(paymentsRegisterListItems);
  return paymentsById?.[paymentId];
};

const useReallocatePaymentDetails = ({ paymentId, entityMetadata, getConfigurableEntitiesMetadata }) => {
  const paymentsRegisterListItems = useSelector(getPaymentsRegisterTableItems);
  const paymentFromStore = getPaymentFromStore(paymentsRegisterListItems, paymentId);

  const [reallocationPayment, setReallocationPayment] = useState(paymentFromStore);
  const [reallocationPaymentPostings, setReallocationPaymentPostings] = useState(EMPTY_ARRAY);
  const [isLoadingReallocatePaymentDetails, setReallocatePaymentDetailsLoaded] = useLoading(true);

  const { isLoading: isLoadingEntityMetadata } = useConfigurableEntityMetadata(
    entityMetadata,
    getConfigurableEntitiesMetadata,
    ASSET_TYPES_BY_MODULE[ACCOUNTING].AP_TRANSACTION_POSTING
  );

  useEffect(() => {
    if (paymentId) {
      fetchReallocatePaymentDetails({
        paymentId,
        setReallocationPaymentPostings,
        setReallocationPayment,
        setReallocatePaymentDetailsLoaded,
        skipPaymentFetch: !!paymentFromStore,
      });
    }
  }, [paymentId, paymentFromStore, setReallocatePaymentDetailsLoaded]);

  const isLoading = isLoadingEntityMetadata || isLoadingReallocatePaymentDetails;

  return {
    reallocationPayment,
    reallocationPaymentPostings,
    isLoading,
  };
};

export default useReallocatePaymentDetails;
