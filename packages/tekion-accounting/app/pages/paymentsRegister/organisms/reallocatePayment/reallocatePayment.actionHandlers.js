// Constants
import FORM_PAGE_ACTION_TYPES from 'tcomponents/pages/formPage/constants/actionTypes';
import FORM_ACTION_TYPES from 'tcomponents/organisms/FormBuilder/constants/actionTypes';
import ACTION_TYPES from './constants/reallocatePayment.actionTypes';

// Helpers
import { processPaymentReallocation } from './helpers/reallocatePayment.onSubmit';
import { redirectToPaymentRegisterList } from '../../helpers/paymentsRegister.general';

const handleFieldChange = ({ setState, params }) => {
  const { values } = params;
  setState({ values });
};

const handleErrors = ({ params, setState }) => {
  const { errors } = params;
  setState({ errors });
};

const handleSubmit = async ({ params, setState, getState }) => {
  const { submitData: transaction } = params;
  const { reallocationPayment, navigate } = getState();
  processPaymentReallocation({ setState, navigate, reallocationPayment, transaction });
};

const handleCancel = ({ getState }) => {
  const { navigate } = getState();
  redirectToPaymentRegisterList(navigate);
};

const ACTION_HANDLERS = {
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: handleFieldChange,
  [FORM_ACTION_TYPES.VALIDATION_SUCCESS]: handleErrors,
  [FORM_PAGE_ACTION_TYPES.ON_FORM_SUBMIT]: handleSubmit,
  [ACTION_TYPES.ON_CANCEL]: handleCancel,
};

export default ACTION_HANDLERS;
