import React, { useCallback, memo } from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Components
import Heading from 'tcomponents/atoms/Heading';
import IconAsBtn from 'tcomponents/atoms/iconAsBtn';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';

// Helpers
import { getFieldName } from '../../helpers/setupField.field';
import { getModalConfig } from './fieldHeader.helper';

// Styles
import styles from './fieldHeader.module.scss';

const FieldHeader = ({ field, toggleModal, showAction }) => {
  const handleAddIconClick = useCallback(() => {
    const modalConfig = getModalConfig(field);
    toggleModal(modalConfig);
  }, [field, toggleModal]);

  const displayName = getFieldName(field);

  return (
    <div className={`full-width d-flex align-items-center justify-content-between ${styles.panelHeader}`}>
      <Heading size={3}>{displayName}</Heading>
      {showAction && (
        <IconAsBtn containerClassName={styles.addIcon} onClick={handleAddIconClick}>
          icon-add
        </IconAsBtn>
      )}
    </div>
  );
};

FieldHeader.propTypes = {
  field: PropTypes.object,
  toggleModal: PropTypes.func,
  showAction: PropTypes.bool,
};

FieldHeader.defaultProps = {
  field: EMPTY_OBJECT,
  toggleModal: _noop,
  showAction: false,
};

export default memo(FieldHeader);
