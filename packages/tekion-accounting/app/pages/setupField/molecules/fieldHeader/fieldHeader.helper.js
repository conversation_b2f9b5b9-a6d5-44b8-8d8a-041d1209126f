// Constants
import OPTION_ACTIONS from '../../constants/setupField.optionActions';

// Helpers
import { getFieldName } from '../../helpers/setupField.field';

export const getModalConfig = field => {
  const { onAddAction, CreateOption, cancelButtonText, submitButtonText, key } = field;
  return {
    label: getFieldName(field),
    actionType: key,
    CreateOption,
    key: OPTION_ACTIONS.ADD,
    onSuccessAction: onAddAction,
    submitButtonText,
    cancelButtonText,
    modalComponent: CreateOption,
    currentField: field,
  };
};
