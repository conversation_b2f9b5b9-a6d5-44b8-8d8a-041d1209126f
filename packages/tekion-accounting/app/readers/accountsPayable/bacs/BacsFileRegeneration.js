import _property from 'lodash/property';

const paymentIdentifier = _property('paymentIdentifier');
const paymentSubType = _property('paymentSubType');
const totalPaymentsForBACSGenerated = _property('totalPaymentsForBACSGenerated');
const totalPaymentsForBACSNotGenerated = _property('totalPaymentsForBACSNotGenerated');
const bacsHistoryResponse = _property('bacsHistoryResponse');

const READER = {
  paymentIdentifier,
  paymentSubType,
  bacsHistoryResponse,
  totalPaymentsForBACSGenerated,
  totalPaymentsForBACSNotGenerated,
};

export default READER;
