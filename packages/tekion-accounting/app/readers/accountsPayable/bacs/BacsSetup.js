import _property from 'lodash/property';

const enableDownloadForOneOffPayments = _property('enableDownloadForOneOffPayments');
const enableDownloadForConsolidatedPayments = _property('enableDownloadForConsolidatedPayments');
const enableDownloadForBatchPayments = _property('enableDownloadForBatchPayments');
const serviceUserNumber = _property('serviceUserNumber');
const originatorName = _property('originatorName');
const paymentExpirationDays = _property('paymentExpirationDays');

const READER = {
  enableDownloadForOneOffPayments,
  enableDownloadForConsolidatedPayments,
  enableDownloadForBatchPayments,
  serviceUserNumber,
  originatorName,
  paymentExpirationDays,
};

export default READER;
