import React from 'react';
import PropTypes from 'prop-types';
import { components } from 'react-select';

import Icon from '@tekion/tekion-components/src/atoms/FontIcon';

const OptionWithIcon = props => {
  const { data, shouldUseOptionIcon, icon } = props;
  const { label, icon: dataIcon } = data;

  const optionIcon = shouldUseOptionIcon && dataIcon ? dataIcon : icon;
  return (
    <components.Option {...props}>
      <div className="flex justify-content-between">
        {label}
        {optionIcon && <Icon>{optionIcon}</Icon>}
      </div>
    </components.Option>
  );
};

OptionWithIcon.propTypes = {
  data: PropTypes.object,
  shouldUseOptionIcon: PropTypes.bool,
  icon: PropTypes.string,
};

OptionWithIcon.defaultProps = {
  data: {},
  shouldUseOptionIcon: true,
  icon: '',
};

export default OptionWithIcon;
