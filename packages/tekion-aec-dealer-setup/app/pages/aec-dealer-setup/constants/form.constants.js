import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import Select from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/select';
import Radio from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/radio';
import Label from '@tekion/tekion-components/src/atoms/Label';

export const DATA_TYPE = {
  INTEGER: 'INTEGER',
  ARRAY: 'ARRAY',
};

export const FORM_FIELD_PROPS_OVERRIDE_MAP = {
  label: 'fieldLabel',
  required: 'mandatory',
};

const FIELD_TYPE = {
  RADIO: 'RADIO',
  FREE_TEXT: 'FREE_TEXT',
  SINGLE_SELECT: 'SINGLE_SELECT',
  LABEL: 'LABEL',
};

export const FIELD_TYPE_VS_COMPONENT_MAP = {
  [FIELD_TYPE.FREE_TEXT]: TextInput,
  [FIELD_TYPE.SINGLE_SELECT]: Select,
  [FIELD_TYPE.RADIO]: Radio,
  [FIELD_TYPE.LABEL]: Label,
};
