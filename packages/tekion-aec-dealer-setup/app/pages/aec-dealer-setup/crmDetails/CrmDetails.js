import React, { useCallback, memo, useMemo, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

import _noop from 'lodash/noop';

import { tget } from '@tekion/tekion-base/utils/general';
import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import withActions from '@tekion/tekion-components/connectors/withActions';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import Loader from '@tekion/tekion-components/src/molecules/loader';
import { FormWithSubmission } from '@tekion/tekion-components/pages/formPage';
import SaveComponent from '@tekion/tekion-components/molecules/SaveComponent';
import { triggerSubmit } from '@tekion/tekion-components/pages/formPage/utils/formAction';

import ACTION_HANDLERS from './crmDetails.actionHandlers';
import { NAV_KEYS, SIDE_NAV_ITEMS } from '../constants/dealerOnboarding.constants';
import UserMapping from './components/userMapping';
import { ACTION_TYPES, FIELD_IDS, FORM_CONTEXT_ID } from './crmDetails.constants';
import { getSections, fieldCreator } from './crmDetails.config';

import styles from './crmDetails.module.scss';

const CrmDetails = props => {
  const {
    onAction,
    values,
    errors,
    primaryActionLoading,
    crmDmsProviders,
    fieldBySelectedCRM,
    userMapping,
    isTableSubmittingInProcess,
    pageLoading,
  } = props;

  useEffect(() => {
    onAction({
      type: ACTION_TYPES.INIT_FORM,
    });
  }, [onAction]);

  const [modalVisible, setModalVisible] = useState(false);
  const [isWarningModalVisible, setWarningModalVisible] = useState(false);

  const { [FIELD_IDS.HOME_CRM]: homeCrmId } = values;
  const {
    fields: fieldMetadata = EMPTY_ARRAY,
    setupIntegrationRequired,
    setupIntegrationBannerText,
  } = fieldBySelectedCRM;

  const handleToggle = () => {
    setModalVisible(prevState => !prevState);
  };

  const openUserMappingModal = useCallback(() => {
    onAction({
      type: ACTION_TYPES.FETCH_TABLE_DATA,
    });
    setModalVisible(true);
  }, [onAction]);

  const handleCloseModal = () => {
    if (setupIntegrationRequired) setWarningModalVisible(true);
    else handleToggle();
  };

  const handleSubmitModal = () => {
    onAction({
      type: ACTION_TYPES.SUBMIT_TABLE,
      payload: {
        setModalVisible,
      },
    });
  };

  const handleCloseToggleWarningModal = () => {
    handleToggle();
    setWarningModalVisible(false);
  };

  const fields = useMemo(
    () => fieldCreator({ crmDmsProviders, fieldMetadata, onSave: openUserMappingModal, values }),
    [crmDmsProviders, fieldMetadata, values, openUserMappingModal]
  );

  const sections = useMemo(() => getSections(fieldMetadata), [fieldMetadata]);

  const handleSave = useCallback(() => triggerSubmit(FORM_CONTEXT_ID), []);

  const handleReset = useCallback(() => {
    onAction({
      type: ACTION_TYPES.RESET_FORM,
    });
  }, [onAction]);

  if (pageLoading) return <Loader />;

  return (
    <>
      <div className={styles.container}>
        <div>
          <Heading size={2}>{tget(SIDE_NAV_ITEMS[NAV_KEYS.CRM_DETAILS], 'name', EMPTY_STRING)}</Heading>
          <FormWithSubmission
            contextId={FORM_CONTEXT_ID}
            sections={sections}
            fields={fields}
            values={values}
            errors={errors}
            onAction={onAction}
            className={styles.containerClassname}
          />
        </div>
        <SaveComponent
          onPrimaryAction={handleSave}
          primaryActionLoading={primaryActionLoading}
          secondaryButtonLabel={__('Reset')}
          onSecondaryAction={handleReset}
        />
      </div>
      <Modal
        title={__('Setup Integration')}
        visible={modalVisible}
        width={Modal.SIZES.L}
        onCancel={handleCloseModal}
        hideSubmit={!setupIntegrationRequired}
        onSubmit={handleSubmitModal}
        loading={isTableSubmittingInProcess}
        secondaryBtnLoading={isTableSubmittingInProcess}>
        <UserMapping
          id={FIELD_IDS.USER_MAPPING_TABLE}
          homeCrmId={homeCrmId}
          onAction={onAction}
          mappings={userMapping}
          setupIntegrationRequired={setupIntegrationRequired}
          setupIntegrationBannerText={setupIntegrationBannerText}
          isTableSubmittingInProcess={isTableSubmittingInProcess}
        />
      </Modal>
      <Modal
        title={__('Are you sure you want to close CRM User Mapping?')}
        visible={isWarningModalVisible}
        width={Modal.SIZES.M}
        onCancel={handleCloseToggleWarningModal}
        hideSubmit
        secondaryBtnText={__('Close Without Saving')}>
        <div>
          <p>{__('CRM User Mapping must be completed and saved to properly integrate with your Home CRM.')}</p>
          <p>{__('Are you sure you want to continue?')}</p>
        </div>
      </Modal>
    </>
  );
};

CrmDetails.propTypes = {
  onAction: PropTypes.func,
  values: PropTypes.object,
  errors: PropTypes.object,
  primaryActionLoading: PropTypes.bool,
  contentHeight: PropTypes.number,
  crmDmsProviders: PropTypes.array,
  userMapping: PropTypes.array,
  fieldBySelectedCRM: PropTypes.object,
  isTableSubmittingInProcess: PropTypes.bool,
  pageLoading: PropTypes.bool,
};

CrmDetails.defaultProps = {
  onAction: _noop,
  values: EMPTY_OBJECT,
  errors: EMPTY_OBJECT,
  primaryActionLoading: false,
  contentHeight: 0,
  crmDmsProviders: EMPTY_ARRAY,
  userMapping: EMPTY_ARRAY,
  fieldBySelectedCRM: EMPTY_OBJECT,
  isTableSubmittingInProcess: false,
  pageLoading: false,
};

export default compose(memo, withActions(EMPTY_OBJECT, ACTION_HANDLERS))(CrmDetails);
