import React, { useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';

import _castArray from 'lodash/castArray';
import _noop from 'lodash/noop';

import Heading from '@tekion/tekion-components/atoms/Heading';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import Banner from '@tekion/tekion-components/src/molecules/banner';
import { TableFieldWithActions } from '@tekion/tekion-components/src/molecules/tableInputField';
import { REMOVE_ACTION } from '@tekion/tekion-components/src/molecules/tableInputField/constants/general';

import { getColumns } from './userMapping.columns';
import { BANNER_CLASS_NAME, CONTENT_BANNER_CLASS_NAME } from './userMapping.constants';

import styles from './userMapping.module.scss';

const UserMapping = props => {
  const { drpUsers, homeCrmUsers, mappings, onAction, id, setupIntegrationBannerText, setupIntegrationRequired } =
    props;

  const columns = useMemo(() => getColumns(drpUsers, homeCrmUsers), [drpUsers, homeCrmUsers]);

  const getActionsForRow = useCallback(({ original = EMPTY_OBJECT }) => {
    const { isLastRow, id } = original;
    if (isLastRow || id) return EMPTY_ARRAY;
    return _castArray(REMOVE_ACTION);
  }, []);

  const bannerClassName = setupIntegrationRequired ? BANNER_CLASS_NAME : CONTENT_BANNER_CLASS_NAME;

  return (
    <div className={styles.setupContainer}>
      {setupIntegrationRequired && (
        <div className={styles.tableContainer}>
          <Heading size={4}>{__('CRM User Mapping')}</Heading>

          <TableFieldWithActions
            id={id}
            columns={columns}
            value={mappings}
            onAction={onAction}
            getActionsForRow={getActionsForRow}
          />
        </div>
      )}
      <Banner show={!!setupIntegrationBannerText} bannerText={setupIntegrationBannerText} {...bannerClassName} />
    </div>
  );
};

UserMapping.propTypes = {
  drpUsers: PropTypes.array,
  homeCrmUsers: PropTypes.array,
  mappings: PropTypes.array,
  onAction: PropTypes.func,
  id: PropTypes.string,
  setupIntegrationRequired: PropTypes.bool,
  setupIntegrationBannerText: PropTypes.string,
};

UserMapping.defaultProps = {
  drpUsers: EMPTY_ARRAY,
  homeCrmUsers: EMPTY_ARRAY,
  mappings: EMPTY_ARRAY,
  onAction: _noop,
  id: EMPTY_STRING,
  setupIntegrationRequired: false,
  setupIntegrationBannerText: EMPTY_STRING,
};

export default UserMapping;
