import { mapProps } from 'recompose';

import { SingleSelect } from '@tekion/tekion-components/src/molecules/advancedSelect';
import makeCellRenderer from '@tekion/tekion-components/src/molecules/tableInputField/containers/makeCellRenderer';
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { ColumnConfig, CellConfig } from '@tekion/tekion-components/src/molecules/tableInputField';
import { getSelectValueOnChange } from '@tekion/tekion-components/organisms/inputTable/utils/onChangeValueAccessors';

import OptionWithIcon from 'atoms/OptionWithIcon';

import { COLUMN_IDS } from './userMapping.constants';

const SelectInputCellRenderer = makeCellRenderer(SingleSelect);

const OptionWithDeleteIcon = mapProps(props => ({ ...props, icon: 'icon-trash', shouldUseOptionIcon: false }))(
  OptionWithIcon
);

const getDrpUserSelectProps = drpUsers => () => ({
  placeholder: __('Select DR ID'),
  options: drpUsers,
  showSearch: true,
});

const getHomeCrmUserSelectProps = homeCrmUsers => () => ({
  placeholder: __('Select/Add CRM ID'),
  options: homeCrmUsers,
  showSearch: true,
  components: {
    Option: OptionWithDeleteIcon,
  },
});

export const getDrpUserColumn = drpUsers =>
  new ColumnConfig(COLUMN_IDS.AEC_USER_ID)
    .setTitle(__('DR User ID'))
    .setAccessor(COLUMN_IDS.AEC_USER_ID)
    .addCellConfig(new CellConfig().setComponent(SelectInputCellRenderer))
    .setMapCellPropsToComponentProps(getDrpUserSelectProps(drpUsers))
    .setGetValueFromOnChange(getSelectValueOnChange);

export const getHomeCrmUserColumn = homeCrmUsers =>
  new ColumnConfig(COLUMN_IDS.HOME_CRM_USER_ID)
    .setTitle(__('Home CRM User ID'))
    .setAccessor(COLUMN_IDS.HOME_CRM_USER_ID)
    .addCellConfig(new CellConfig().setComponent(SelectInputCellRenderer))
    .setMapCellPropsToComponentProps(getHomeCrmUserSelectProps(homeCrmUsers))
    .setGetValueFromOnChange(getSelectValueOnChange);

export const getColumns = (drpUsers = EMPTY_ARRAY, homeCrmUsers = EMPTY_ARRAY) => [
  getDrpUserColumn(drpUsers),
  getHomeCrmUserColumn(homeCrmUsers),
];
