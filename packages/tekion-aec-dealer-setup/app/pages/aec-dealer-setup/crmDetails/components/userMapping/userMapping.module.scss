@use "@tekion/tekion-styles-next/scss/component.scss";

%blue-container {
  background: component.$aliceBlue;
  color: component.$black;
}

.iconContainer {
  @extend %blue-container;
}

.bannerContainer {
  @extend %blue-container;
  border: none;
  border-radius: 0.3rem;
  height: fit-content;
  padding-block: 1rem;
}

.setupContainer {
  @include component.flex($flex-flow: column);
  gap: 1.6rem;
}

.tableContainer {
  height: 40rem;
  overflow: scroll;
  @extend .setupContainer;
}

.contentBannerIcon {
  background-color: component.$white;
  color: component.$black;
}

.contentBannerContainer {
  background-color: component.$white;
  border: none !important;
}
