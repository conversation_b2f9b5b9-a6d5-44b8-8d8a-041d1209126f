import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/organisms/NotificationWrapper';

import aecOnboardingApi from '../services/aecOnboardingServices';

export const fetchCrmDetailsAction = async oemId => {
  try {
    const response = await aecOnboardingApi.getHomeCrmMetadata(oemId);
    return getDataFromResponse(response);
  } catch (e) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(e), __('Failed to Load Home CRM Form'));
    return EMPTY_OBJECT;
  }
};

export const updateHomeCrmInfoAction = async payload => {
  try {
    await aecOnboardingApi.updateHomeCrmInfo(payload);
    toaster(TOASTER_TYPE.SUCCESS, __('Home CRM Updated Successfully'));
    return true;
  } catch (err) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(err), __('Failed to update Home CRM'));
    return false;
  }
};

export const getPrimaryHomeCrmInfoAction = async oemId => {
  try {
    const response = await aecOnboardingApi.getPrimaryHomeCrmInfo(oemId);
    return getDataFromResponse(response);
  } catch (err) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(err));
    return EMPTY_OBJECT;
  }
};
