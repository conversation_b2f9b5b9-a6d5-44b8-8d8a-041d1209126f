import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _head from 'lodash/head';
import _find from 'lodash/find';
import _keyBy from 'lodash/keyBy';
import _mapValues from 'lodash/mapValues';
import _size from 'lodash/size';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import updateElementAtIndex from '@tekion/tekion-base/utils/updateElementAtIndex';
import removeAtIndex from '@tekion/tekion-base/utils/removeElementAtIndex';
import { REMOVE_ACTION } from '@tekion/tekion-components/src/molecules/tableInputField/constants/general';
import { defaultOnChangeHandler } from '@tekion/tekion-components/connectors/withFormPageState/withFormPageState.helpers';
import FORM_ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import TABLE_ACTION_TYPES from '@tekion/tekion-components/molecules/tableInputField/constants/TableInputField.actionTypes';
import { getTableDataWithLastRowBoolean } from '@tekion/tekion-components/src/molecules/tableInputField/utils/tableStateHelpers';

import { fetchCrmDetailsAction, updateHomeCrmInfoAction, getPrimaryHomeCrmInfoAction } from './crmDetails.action';
import { ACTION_TYPES, FIELD_IDS } from './crmDetails.constants';
import { getPayloadFromStateForFeatureInfo, handleValidationSuccess } from '../helpers/form.helpers';

const initForm = async ({ setState, getState }) => {
  const { oemId } = getState();

  setState({ pageLoading: true });
  const [crmDetailsMetadata, primaryHomeCrmInfo] = await Promise.all([
    fetchCrmDetailsAction(oemId),
    getPrimaryHomeCrmInfoAction(oemId),
  ]);
  const { crmDmsProviders = EMPTY_ARRAY } = crmDetailsMetadata;

  const { [FIELD_IDS.HOME_CRM]: homeCrm } = primaryHomeCrmInfo;
  const fieldBySelectedCRM = _find(crmDmsProviders, { dmsProviderName: homeCrm }) || EMPTY_OBJECT;

  const values = _mapValues(
    _get(primaryHomeCrmInfo, 'fieldNameToValueObjectMap', EMPTY_OBJECT),
    ({ fieldValue }) => fieldValue
  );
  setState({
    pageLoading: false,
    crmDmsProviders,
    values: {
      [FIELD_IDS.HOME_CRM]: homeCrm,
      ...values,
    },
    fieldBySelectedCRM,
    crmDetailsMetadata,
  });
};

const handleTableFieldChange = ({ params = EMPTY_OBJECT, getState, setState }) => {
  const { userMapping = EMPTY_ARRAY } = getState();
  const { nestingPath, columnId, value: currValue } = params;
  const index = _head(nestingPath);
  const oldRowValue = userMapping[index];
  const newRowValue = { ...oldRowValue, [columnId]: currValue };
  const updateTabledata = updateElementAtIndex(userMapping, newRowValue, index);
  if (index === _size(userMapping) - 1) {
    const newTableValue = getTableDataWithLastRowBoolean([...updateTabledata, {}]);
    setState({
      userMapping: newTableValue,
    });
    return;
  }
  setState({ userMapping: updateTabledata });
};

const onChangeHandler = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { id, value } = params;
  if (id === FIELD_IDS.USER_MAPPING_TABLE) {
    handleTableFieldChange({ params, getState, setState });
    return;
  }
  if (id === FIELD_IDS.HOME_CRM) {
    const { crmDetailsMetadata } = getState();
    const crmDmsProviders = _get(crmDetailsMetadata, 'crmDmsProviders', []);
    const fieldBySelectedCRM = _find(crmDmsProviders, { dmsProviderName: value }) || EMPTY_OBJECT;

    setState({
      values: {
        [id]: value,
      },
      fieldBySelectedCRM,
    });
    return;
  }
  defaultOnChangeHandler({ getState, setState, params });
};

const handleSubmit = async ({ getState, setState }) => {
  const { values, fieldBySelectedCRM } = getState();
  const { fields: fieldMetadata = EMPTY_ARRAY } = fieldBySelectedCRM;
  const { [FIELD_IDS.HOME_CRM]: homeCrm } = values;

  const payload = {
    [FIELD_IDS.HOME_CRM]: homeCrm,
    fieldNameToValueMap: getPayloadFromStateForFeatureInfo(values, _keyBy(fieldMetadata, 'fieldName')),
  };

  setState({
    primaryActionLoading: true,
  });

  await updateHomeCrmInfoAction(payload);

  setState({
    primaryActionLoading: false,
  });
};

const handleRemoveTableRow = ({ params = EMPTY_OBJECT, getState, setState }) => {
  const { userMapping = EMPTY_ARRAY } = getState();
  const { nestingPath } = params;
  const index = _head(nestingPath);
  const updatedValue = removeAtIndex(userMapping, index);
  setState({ userMapping: updatedValue });
};

const TABLE_ROW_ACTION_VS_HANDLER = {
  [REMOVE_ACTION.id]: handleRemoveTableRow,
};

const handleTableActionClick = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { actionType } = params;
  const rowActionHandler = _get(TABLE_ROW_ACTION_VS_HANDLER, actionType, _noop);
  rowActionHandler({ params, getState, setState });
};

const handleUserMappingFetch = async ({ getState, setState }) => {
  const { values } = getState();
  setState({ isTableFetchingInProcess: true });
  // const response = await fetchUserMapping();
  // const userMapping = getTableDataWithLastRowBoolean([{ hello: 'world' }]);
  setState({ isTableFetchingInProcess: false });
};

const handleUserMappingSave = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { setModalVisible } = params;
  const { userMapping } = getState();

  setState({
    isTableSubmittingInProcess: true,
  });

  // const response = await saveUserMapping(userMapping);
  setState({
    isTableSubmittingInProcess: false,
  });

  setModalVisible(false);
};

const resetHomeCrmForm = ({ setState }) => {
  setState({ values: EMPTY_OBJECT });
};

export default {
  [ACTION_TYPES.INIT_FORM]: initForm,
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: onChangeHandler,
  [FORM_ACTION_TYPES.ON_FORM_SUBMIT]: handleSubmit,
  [FORM_ACTION_TYPES.VALIDATION_SUCCESS]: handleValidationSuccess,
  [TABLE_ACTION_TYPES.TABLE_ACTION_CLICK]: handleTableActionClick,
  [ACTION_TYPES.SUBMIT_TABLE]: handleUserMappingSave,
  [ACTION_TYPES.FETCH_TABLE_DATA]: handleUserMappingFetch,
  [ACTION_TYPES.RESET_FORM]: resetHomeCrmForm,
};
