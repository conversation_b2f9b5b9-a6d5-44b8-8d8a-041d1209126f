import React from 'react';

import _keyBy from 'lodash/keyBy';
import _has from 'lodash/has';

import { EMPTY_ARRAY, EMPTY_STRING, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { tget } from '@tekion/tekion-base/utils/general';
import { isRequiredRule } from '@tekion/tekion-base/utils/formValidators';
import standardFieldOptionMapper from '@tekion/tekion-base/utils/optionMappers/standardFieldMapper';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Select from '@tekion/tekion-components/organisms/FormBuilder/fieldRenderers/select';
import SaveComponent from '@tekion/tekion-components/molecules/SaveComponent';

import {
  dmsProviderFormatter,
  getFormSectionsFromMetadata,
  getFormFieldsFromMetadata,
} from 'pages/aec-dealer-setup/helpers/form.helpers';
import { NAV_KEYS, SIDE_NAV_ITEMS } from '../constants/dealerOnboarding.constants';
import { FIELD_IDS } from './crmDetails.constants';

import styles from './crmDetails.module.scss';

const getHomeCrmField = (crmDmsProviders = EMPTY_ARRAY) => ({
  id: FIELD_IDS.HOME_CRM,
  renderer: Select,
  renderOptions: {
    label: __('Select your Home CRM and map it to the DRP'),
    placeholder: __('Select Home CRM'),
    options: standardFieldOptionMapper(undefined, crmDmsProviders, dmsProviderFormatter),
    required: true,
    validators: [isRequiredRule],
  },
});

const getSetupIntegrationField = (onSave, isPrimaryDisabled) => ({
  id: FIELD_IDS.SETUP_INTEGRATION,
  renderer: SaveComponent,
  renderOptions: {
    primaryButtonLabel: __('Setup Integration'),
    onPrimaryAction: onSave,
    showSecondaryButton: false,
    containerClassName: 'full-width p-0',
    isPrimaryDisabled,
  },
});

const HEADING_FIELD = {
  renderer: () => (
    <div className="flex-column">
      <Heading size={3}>{__('Home Crm')}</Heading>
      <Content className={styles.text}>{tget(SIDE_NAV_ITEMS[NAV_KEYS.CRM_DETAILS], 'text', EMPTY_STRING)}</Content>
    </div>
  ),
};

export const getSections = fieldMetadata => [
  { rows: [{ columns: [FIELD_IDS.HEADING] }], className: `p-0 ${styles.headingContainer}` },
  {
    rows: [{ columns: [FIELD_IDS.HOME_CRM] }, ...getFormSectionsFromMetadata(fieldMetadata)],
    className: `p-0 ${styles.formContainer}`,
  },
  {
    rows: [
      {
        columns: [FIELD_IDS.SETUP_INTEGRATION],
      },
    ],
    className: `p-0 ${styles.headingContainer}`,
  },
];

export const fieldCreator = ({ fieldMetadata, crmDmsProviders = EMPTY_ARRAY, onSave, values = EMPTY_OBJECT }) => {
  const { [FIELD_IDS.HOME_CRM]: homeCrmId, [FIELD_IDS.HOME_CRM_DEALER_ID]: homeCrmDealerId } = values;
  const fieldNameMap = _keyBy(fieldMetadata, 'fieldName');
  const dynamicFields = getFormFieldsFromMetadata(fieldNameMap);

  const isSetupIntegrationDisabled =
    !homeCrmId || (_has(dynamicFields, FIELD_IDS.HOME_CRM_DEALER_ID) && !homeCrmDealerId);

  return {
    [FIELD_IDS.HEADING]: HEADING_FIELD,
    [FIELD_IDS.HOME_CRM]: getHomeCrmField(crmDmsProviders),
    [FIELD_IDS.SETUP_INTEGRATION]: getSetupIntegrationField(onSave, isSetupIntegrationDisabled),
    ...dynamicFields,
  };
};
