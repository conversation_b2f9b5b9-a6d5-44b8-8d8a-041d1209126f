@use "@tekion/tekion-styles-next/scss/component.scss";

.container {
  @include component.flex($flex-flow: column, $justify-content: space-between);
  gap: 1.6rem;
  flex-grow: 1;
}

.formContainer {
  width: 60%;
  margin-top: 2rem;
}

.containerClassname {
  border: component.$border;
  padding: 2rem;
  width: 95%;
  margin-top: 2rem;
}

.text {
  margin: 1rem 0 0 0;
}

.headingContainer {
  max-width: 90%;
}
