import { filterSideNavItems } from '../dealerOnboarding.helpers';
import { SIDE_NAV_ITEMS } from '../../constants/dealerOnboarding.constants';

describe('filterSideNavItems', () => {
  const MATCHING_MODULES = [
    { moduleName: 'dealershipOverview', moduleLabel: 'Dealership Overview' },
    { moduleName: 'primaryDMS', moduleLabel: 'Primary DMS' },
  ];

  const NON_MATCHING_MODULES = [{ moduleName: 'nonExistentModule', moduleLabel: 'Non-existent Module' }];

  const MIXED_MODULES = [
    { moduleName: 'dealershipOverview', moduleLabel: 'Dealership Overview' },
    { moduleName: 'nonExistentModule', moduleLabel: 'Non-existent Module' },
    { moduleName: 'primaryDMS', moduleLabel: 'Primary DMS' },
  ];

  const NULL_MODULE_NAME = [
    { moduleName: null, moduleLabel: 'Null Module Name' },
    { moduleName: 'primaryDMS', moduleLabel: 'Primary DMS' },
  ];

  const UNDEFINED_MODULE_NAME = [
    { moduleName: undefined, moduleLabel: 'Undefined Module Name' },
    { moduleName: 'primaryDMS', moduleLabel: 'Primary DMS' },
  ];

  const MISSING_MODULE_NAME = [
    { moduleLabel: 'Missing Module Name' },
    { moduleName: 'primaryDMS', moduleLabel: 'Primary DMS' },
  ];

  it('should filter side nav items based on available modules', () => {
    const result = filterSideNavItems(MATCHING_MODULES);

    expect(result).toEqual({
      dealershipOverview: {
        ...SIDE_NAV_ITEMS.dealershipOverview,
        name: 'Dealership Overview',
      },
      primaryDMS: {
        ...SIDE_NAV_ITEMS.primaryDMS,
        name: 'Primary DMS',
      },
    });
  });

  it('should return empty object when no modules match', () => {
    const result = filterSideNavItems(NON_MATCHING_MODULES);

    expect(result).toEqual({});
  });

  it('should handle empty modules array', () => {
    const result = filterSideNavItems([]);

    expect(result).toEqual({});
  });

  it('should use the moduleLabel from the module if available', () => {
    const modules = [{ moduleName: 'dealershipOverview', moduleLabel: 'Custom Dealership Overview Label' }];

    const result = filterSideNavItems(modules);

    expect(result).toEqual({
      dealershipOverview: {
        ...SIDE_NAV_ITEMS.dealershipOverview,
        name: 'Custom Dealership Overview Label',
      },
    });
  });

  it('should filter out modules that do not exist in SIDE_NAV_ITEMS', () => {
    const result = filterSideNavItems(MIXED_MODULES);

    expect(result).toEqual({
      dealershipOverview: {
        ...SIDE_NAV_ITEMS.dealershipOverview,
        name: 'Dealership Overview',
      },
      primaryDMS: {
        ...SIDE_NAV_ITEMS.primaryDMS,
        name: 'Primary DMS',
      },
    });
  });

  it('should handle null moduleName', () => {
    const result = filterSideNavItems(NULL_MODULE_NAME);

    expect(result).toEqual({
      primaryDMS: {
        ...SIDE_NAV_ITEMS.primaryDMS,
        name: 'Primary DMS',
      },
    });
  });

  it('should handle undefined moduleName', () => {
    const result = filterSideNavItems(UNDEFINED_MODULE_NAME);

    expect(result).toEqual({
      primaryDMS: {
        ...SIDE_NAV_ITEMS.primaryDMS,
        name: 'Primary DMS',
      },
    });
  });

  it('should handle missing moduleName property', () => {
    const result = filterSideNavItems(MISSING_MODULE_NAME);

    expect(result).toEqual({
      primaryDMS: {
        ...SIDE_NAV_ITEMS.primaryDMS,
        name: 'Primary DMS',
      },
    });
  });
});
