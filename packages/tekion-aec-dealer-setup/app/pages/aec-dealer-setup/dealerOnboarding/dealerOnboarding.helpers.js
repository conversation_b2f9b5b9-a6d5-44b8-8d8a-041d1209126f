import { defaultMemoize } from 'reselect';

import _get from 'lodash/get';
import _set from 'lodash/set';
import _forEach from 'lodash/forEach';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { SIDE_NAV_ITEMS } from '../constants/dealerOnboarding.constants';

export const filterSideNavItems = defaultMemoize((modules = EMPTY_ARRAY) => {
  const navItems = {};
  _forEach(modules, ({ moduleName, moduleLabel }) => {
    if (!moduleName || !(moduleName in SIDE_NAV_ITEMS)) return;
    const moduleConfig = _get(SIDE_NAV_ITEMS, [moduleName], EMPTY_OBJECT);
    _set(navItems, moduleName, { ...moduleConfig, name: moduleLabel });
  });
  return navItems;
});
