import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import { Link } from 'react-router-dom';

import Banner from 'tcomponents/molecules/banner';
import { BANNER_TYPES } from 'tcomponents/molecules/banner/banner.constants';
import Icon from 'tcomponents/atoms/FontIcon';

import styles from './bannerRedirect.module.scss';

const BannerRedirect = ({ message, redirectText, redirectLink, className = '' }) => {
  const actionComponent = (
    <Link to={redirectLink} className={styles.redirectButton}>
      <span>{redirectText}</span>
      <Icon>icon-redirect</Icon>
    </Link>
  );

  return (
    <Banner
      type={BANNER_TYPES.INFO}
      bannerText={message}
      actionComponent={actionComponent}
      containerClassName={cx(styles.bannerContainer, className)}
      iconClassName={styles.infoIcon}
      bannerTextClassName={styles.bannerText}
      show
      actionClassName={styles.actionContainer}
    />
  );
};

BannerRedirect.propTypes = {
  message: PropTypes.string.isRequired,
  redirectText: PropTypes.string.isRequired,
  redirectLink: PropTypes.string.isRequired,
  className: PropTypes.string,
};

BannerRedirect.defaultProps = {
  className: '',
};

export default BannerRedirect;
