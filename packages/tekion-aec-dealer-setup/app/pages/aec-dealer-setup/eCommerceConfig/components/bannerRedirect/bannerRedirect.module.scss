@use "tstyles/component.scss";

.bannerContainer {
  background-color: component.$aliceBlue;
  border: none;
}

.infoIcon {
  background-color: component.$aliceBlue;
  color: component.$black;
  margin-left: 0.8rem;
}

.bannerText {
  margin-left: 0.8rem;
}

.redirectButton {
  @include component.flex($align-items: center);
  font-weight: 600;
  gap: 0.8rem;

  &:hover {
    text-decoration: none;
  }
}

.actionContainer {
  @include component.flex($align-items: center);
  gap: 0.8rem;
}
