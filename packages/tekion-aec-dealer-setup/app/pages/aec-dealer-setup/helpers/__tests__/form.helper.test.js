import {
  resolveChainDependancies,
  getDependancies,
  getPayloadFromStateForFeatureInfo,
  getFormSectionsFromMetadata,
} from '../form.helpers';

describe('resolveChainDependancies', () => {
  const SIMPLE_DEPENDENCY_OBJECT = {
    field1: ['field2', 'field3'],
    field2: ['field4'],
    field3: ['field5'],
    field4: ['field6'],
    field5: [],
    field6: [],
  };

  const CIRCULAR_DEPENDENCY_OBJECT = {
    field1: ['field2'],
    field2: ['field3'],
    field3: ['field1'],
  };

  const EMPTY_DEPENDENCY_OBJECT = {};

  const MISSING_KEY_OBJECT = {
    field1: ['field2', 'field3'],
    field3: ['field5'],
  };

  it('should resolve simple chain dependencies correctly', () => {
    const result = resolveChainDependancies(SIMPLE_DEPENDENCY_OBJECT, 'field1');

    expect(result).toEqual(['field2', 'field4', 'field6', 'field3', 'field5']);
  });

  it('should handle circular dependencies', () => {
    const result = resolveChainDependancies(CIRCULAR_DEPENDENCY_OBJECT, 'field1');

    expect(result).toEqual(['field2', 'field3', 'field1']);
  });

  it('should return empty array for non-existent start key', () => {
    const result = resolveChainDependancies(SIMPLE_DEPENDENCY_OBJECT, 'nonExistentField');

    expect(result).toEqual([]);
  });

  it('should return empty array for empty dependency object', () => {
    const result = resolveChainDependancies(EMPTY_DEPENDENCY_OBJECT, 'field1');

    expect(result).toEqual([]);
  });

  it('should handle missing dependency keys', () => {
    const result = resolveChainDependancies(MISSING_KEY_OBJECT, 'field1');

    expect(result).toEqual(['field2', 'field3', 'field5']);
  });

  it('should handle empty dependencies array', () => {
    const result = resolveChainDependancies(SIMPLE_DEPENDENCY_OBJECT, 'field5');

    expect(result).toEqual([]);
  });

  it('should use provided visited set', () => {
    const visited = new Set(['field2']);
    const result = resolveChainDependancies(SIMPLE_DEPENDENCY_OBJECT, 'field1', visited);

    expect(result).toEqual(['field2', 'field3', 'field5']);
    expect(visited.has('field1')).toBe(true);
    expect(visited.has('field3')).toBe(true);
    expect(visited.has('field5')).toBe(true);
  });
});

describe('getDependancies', () => {
  const FIELD_METADATA_WITH_DEPENDENCIES = [
    {
      fieldName: 'field1',
      dependencyFields: {
        dependencyField: 'field2',
        matchValue: 'value2',
      },
    },
    {
      fieldName: 'field3',
      dependencyFields: {
        dependencyField: 'field2',
        matchValue: 'value3',
      },
    },
    {
      fieldName: 'field4',
      dependencyFields: {
        dependencyField: 'field5',
        matchValue: 'value5',
      },
    },
    {
      fieldName: 'field6',
    },
  ];

  const FIELD_METADATA_WITHOUT_DEPENDENCIES = [
    {
      fieldName: 'field1',
    },
    {
      fieldName: 'field2',
    },
  ];

  const FIELD_METADATA_WITH_EMPTY_DEPENDENCIES = [
    {
      fieldName: 'field1',
      dependencyFields: {},
    },
    {
      fieldName: 'field2',
      dependencyFields: {},
    },
  ];

  it('should create a dependency map from field metadata', () => {
    const result = getDependancies(FIELD_METADATA_WITH_DEPENDENCIES);

    expect(result).toEqual({
      field2: ['field1', 'field3'],
      field5: ['field4'],
    });
  });

  it('should return an empty object when no dependencies exist', () => {
    const result = getDependancies(FIELD_METADATA_WITHOUT_DEPENDENCIES);

    expect(result).toEqual({});
  });

  it('should return an empty object when dependencies are empty objects', () => {
    const result = getDependancies(FIELD_METADATA_WITH_EMPTY_DEPENDENCIES);

    expect(result).toEqual({});
  });

  it('should return an empty object when field metadata is empty', () => {
    const result = getDependancies([]);

    expect(result).toEqual({});
  });

  it('should handle undefined field metadata by returning an empty object', () => {
    const result = getDependancies(undefined);

    expect(result).toEqual({});
  });

  it('should be memoized and return the same result for the same input', () => {
    const result1 = getDependancies(FIELD_METADATA_WITH_DEPENDENCIES);
    const result2 = getDependancies(FIELD_METADATA_WITH_DEPENDENCIES);

    expect(result1).toBe(result2);
  });

  it('should return different results for different inputs', () => {
    const result1 = getDependancies(FIELD_METADATA_WITH_DEPENDENCIES);
    const result2 = getDependancies(FIELD_METADATA_WITHOUT_DEPENDENCIES);

    expect(result1).not.toBe(result2);
  });
});

describe('getPayloadFromStateForFeatureInfo', () => {
  const FORM_VALUES_WITH_MIXED_TYPES = {
    stringField: 'string value',
    integerField: '42',
    arrayField: '[1,2,3]',
    booleanField: 'true',
    extraField: 'should not be included',
  };

  const FEATURE_FIELD_METADATA = {
    stringField: {
      dataType: 'STRING',
    },
    integerField: {
      dataType: 'INTEGER',
    },
    arrayField: {
      dataType: 'ARRAY',
    },
    booleanField: {
      dataType: 'BOOLEAN',
    },
  };

  const FORM_VALUES_WITH_INVALID_JSON = {
    integerField: 'not a number',
    arrayField: 'not an array',
  };

  it('should extract and transform values based on metadata', () => {
    const result = getPayloadFromStateForFeatureInfo(FORM_VALUES_WITH_MIXED_TYPES, FEATURE_FIELD_METADATA);

    expect(result).toEqual({
      stringField: 'string value',
      integerField: 42,
      arrayField: [1, 2, 3],
      booleanField: 'true',
    });
  });

  it('should handle invalid JSON for INTEGER and ARRAY types', () => {
    const result = getPayloadFromStateForFeatureInfo(FORM_VALUES_WITH_INVALID_JSON, FEATURE_FIELD_METADATA);

    expect(result).toEqual({
      integerField: 'not a number',
      arrayField: 'not an array',
    });
  });

  it('should return empty array when featureFieldMetadata is empty', () => {
    const result = getPayloadFromStateForFeatureInfo(FORM_VALUES_WITH_MIXED_TYPES, {});

    expect(result).toEqual({});
  });

  it('should only include fields that exist in the metadata', () => {
    const result = getPayloadFromStateForFeatureInfo(FORM_VALUES_WITH_MIXED_TYPES, FEATURE_FIELD_METADATA);

    expect(result).not.toHaveProperty('extraField');
  });

  it('should handle empty form values', () => {
    const result = getPayloadFromStateForFeatureInfo({}, FEATURE_FIELD_METADATA);

    expect(result).toEqual({});
  });

  it('should handle null form values', () => {
    const result = getPayloadFromStateForFeatureInfo(null, FEATURE_FIELD_METADATA);

    expect(result).toEqual({});
  });

  it('should handle undefined form values', () => {
    const result = getPayloadFromStateForFeatureInfo(undefined, FEATURE_FIELD_METADATA);

    expect(result).toEqual({});
  });

  it('should handle partial form values', () => {
    const partialFormValues = {
      stringField: 'string value',
    };

    const result = getPayloadFromStateForFeatureInfo(partialFormValues, FEATURE_FIELD_METADATA);

    expect(result).toEqual({
      stringField: 'string value',
    });
  });
});

describe('getFormSectionsFromMetadata', () => {
  const FEATURE_FIELD_METADATA_WITH_ROWS = [
    {
      fieldName: 'field1',
      fieldRow: 1,
    },
    {
      fieldName: 'field2',
      fieldRow: 1,
    },
    {
      fieldName: 'field3',
      fieldRow: 2,
    },
    {
      fieldName: 'field4',
      fieldRow: 2,
      dependencyFields: {
        dependencyField: 'field1',
        matchValue: 'value1',
      },
    },
  ];

  const FEATURE_FIELD_METADATA_WITH_DEPENDENCIES = [
    {
      fieldName: 'field1',
      fieldRow: 1,
    },
    {
      fieldName: 'field2',
      fieldRow: 1,
      dependencyFields: {
        dependencyField: 'field1',
        matchValue: 'value1',
      },
    },
    {
      fieldName: 'field3',
      fieldRow: 2,
      dependencyFields: {
        dependencyField: 'field1',
        matchValue: 'value2',
      },
    },
  ];

  const FORM_VALUES_MATCHING_DEPENDENCIES = {
    field1: 'value1',
    field2: 'some value',
    field3: 'some other value',
  };

  const FORM_VALUES_NOT_MATCHING_DEPENDENCIES = {
    field1: 'value3',
    field2: 'some value',
    field3: 'some other value',
  };

  it('should group fields by row and create sections', () => {
    const result = getFormSectionsFromMetadata(FEATURE_FIELD_METADATA_WITH_ROWS, {});

    expect(result).toEqual([{ columns: ['field1', 'field2'] }, { columns: ['field3'] }]);
  });

  it('should filter fields based on dependency conditions when values match', () => {
    const result = getFormSectionsFromMetadata(
      FEATURE_FIELD_METADATA_WITH_DEPENDENCIES,
      FORM_VALUES_MATCHING_DEPENDENCIES
    );

    expect(result).toEqual([{ columns: ['field1', 'field2'] }, { columns: [] }]);
  });

  it('should filter fields based on dependency conditions when values do not match', () => {
    const result = getFormSectionsFromMetadata(
      FEATURE_FIELD_METADATA_WITH_DEPENDENCIES,
      FORM_VALUES_NOT_MATCHING_DEPENDENCIES
    );

    expect(result).toEqual([{ columns: ['field1'] }, { columns: [] }]);
  });

  it('should handle empty metadata', () => {
    const result = getFormSectionsFromMetadata([], {});

    expect(result).toEqual([]);
  });

  it('should handle empty values', () => {
    const result = getFormSectionsFromMetadata(FEATURE_FIELD_METADATA_WITH_DEPENDENCIES, {});

    expect(result).toEqual([{ columns: ['field1'] }, { columns: [] }]);
  });

  it('should handle metadata without fieldRow property', () => {
    const metadataWithoutRows = [
      {
        fieldName: 'field1',
      },
      {
        fieldName: 'field2',
      },
    ];

    const result = getFormSectionsFromMetadata(metadataWithoutRows, {});

    expect(result).toEqual([{ columns: ['field1', 'field2'] }]);
  });

  it('should handle null values for dependency checks', () => {
    const metadataWithNullableDependency = [
      {
        fieldName: 'field1',
        fieldRow: 1,
      },
      {
        fieldName: 'field2',
        fieldRow: 1,
        dependencyFields: {
          dependencyField: 'field1',
          matchValue: 'null',
        },
      },
    ];

    const valuesWithNull = {
      field1: null,
    };

    const result = getFormSectionsFromMetadata(metadataWithNullableDependency, valuesWithNull);

    expect(result).toEqual([{ columns: ['field1'] }]);
  });

  it('should sort sections by row number', () => {
    const unsortedMetadata = [
      {
        fieldName: 'field3',
        fieldRow: 3,
      },
      {
        fieldName: 'field1',
        fieldRow: 1,
      },
      {
        fieldName: 'field2',
        fieldRow: 2,
      },
    ];

    const result = getFormSectionsFromMetadata(unsortedMetadata, {});

    expect(result).toEqual([{ columns: ['field1'] }, { columns: ['field2'] }, { columns: ['field3'] }]);
  });
});
