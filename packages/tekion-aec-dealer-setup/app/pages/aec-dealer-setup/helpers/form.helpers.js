import { defaultMemoize } from 'reselect';

import _keys from 'lodash/keys';
import _map from 'lodash/map';
import _forEach from 'lodash/forEach';
import _reduce from 'lodash/reduce';
import _get from 'lodash/get';
import _first from 'lodash/first';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _last from 'lodash/last';
import _pick from 'lodash/pick';
import _property from 'lodash/property';
import _groupBy from 'lodash/groupBy';
import _set from 'lodash/set';
import _sortBy from 'lodash/sortBy';
import _toPairs from 'lodash/toPairs';
import _flatMap from 'lodash/flatMap';
import _filter from 'lodash/filter';

import { tget } from '@tekion/tekion-base/utils/general';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import addToRenderOptions from '@tekion/tekion-base/utils/addToRenderOptions';
import { isRequiredRule } from '@tekion/tekion-base/utils/formValidators';

import {
  DATA_TYPE,
  FIELD_TYPE_VS_COMPONENT_MAP,
  FORM_FIELD_PROPS_OVERRIDE_MAP,
} from 'pages/aec-dealer-setup/constants/form.constants';

const getDropdownAdditionalProps = (fieldOptions = EMPTY_ARRAY) => {
  const options = _map(fieldOptions, ({ displayName, value } = EMPTY_OBJECT) => ({ label: displayName, value }));

  return { options };
};

export const isArrayFormatField = (fieldId, valueToTest) => {
  if (_isNil(valueToTest) || valueToTest === '') return { isValid: true };
  const isValid = _first(valueToTest) === '[' && _last(valueToTest) === ']';
  return isValid ? { isValid } : { isValid, message: __('Enter valid array enclosed with []') };
};

const getFieldValidators = defaultMemoize((metadataObject = EMPTY_OBJECT) => {
  const validators = [];
  const { mandatory, dataType } = metadataObject;

  if (dataType === DATA_TYPE.ARRAY) validators.push(isArrayFormatField);
  if (mandatory) validators.push(isRequiredRule);

  return validators;
});

export const getFormSectionsFromMetadata = (featureFieldMetadata = EMPTY_ARRAY, values = EMPTY_OBJECT) => {
  const fieldKeyList = _groupBy(featureFieldMetadata, 'fieldRow');
  const sections = _reduce(
    fieldKeyList,
    (sections, fieldData, fieldIndex) => {
      const filteredRows = _filter(fieldData, field => {
        const dependencyFields = tget(field, 'dependencyFields', EMPTY_OBJECT);

        if (!_isEmpty(dependencyFields)) {
          const { dependencyField, matchValue } = dependencyFields;
          const { [dependencyField]: valueToCheck } = values;

          return !_isNil(valueToCheck) && !_isNil(matchValue) && valueToCheck.toString() === matchValue.toString();
        }
        return true;
      });

      _set(sections, fieldIndex - 1, _map(filteredRows, 'fieldName'));
      return sections;
    },
    {}
  );
  return _map(_sortBy(_toPairs(sections)), item => ({ columns: item[1] }));
};

export const getDependancies = defaultMemoize((featureFieldMetadata = EMPTY_ARRAY) => {
  const dependancies = {};
  _forEach(featureFieldMetadata, fieldData => {
    const { fieldName } = fieldData;
    const dependencyFields = tget(fieldData, 'dependencyFields', EMPTY_OBJECT);

    if (!_isEmpty(dependencyFields)) {
      const { dependencyField } = dependencyFields;
      const newDependancy = [..._get(dependancies, dependencyField, EMPTY_ARRAY), fieldName];
      _set(dependancies, dependencyField, newDependancy);
    }
  });
  return dependancies;
});

export const resolveChainDependancies = (obj, startKey, visited = new Set()) => {
  if (!obj[startKey] || visited.has(startKey)) return [];

  visited.add(startKey);

  const children = obj[startKey];
  return _flatMap(children, key => [key, ...resolveChainDependancies(obj, key, visited)]);
};

export const getFormFieldsFromMetadata = defaultMemoize(
  (featureFieldMetadata = EMPTY_OBJECT, formFieldMetadataMapper = FORM_FIELD_PROPS_OVERRIDE_MAP) =>
    /*
      featureFieldMetadata : {
          payoutType: {
            fieldName: 'dealer_provider_id',
            fieldLabel: 'Dealer Provider ID',
            mandatory: true,
            freeTextAllowed: false,
            options: [
              {
                displayName: 'Manual',
                value: 'Manual',
                defaultOption: true,
              },
              {
                displayName: 'Automatic',
                value: 'Automatic',
                defaultOption: false,
              },
            ],
          },
        }
    */

    _reduce(
      featureFieldMetadata,
      (acc, metadataObject = EMPTY_OBJECT, featureFieldKey) => {
        const { freeTextAllowed, options, dataType, fieldType } = metadataObject;
        const staticFieldConfig = {
          id: featureFieldKey,
          renderer: FIELD_TYPE_VS_COMPONENT_MAP[fieldType] || FIELD_TYPE_VS_COMPONENT_MAP.FREE_TEXT,
          renderOptions: {
            ...(!freeTextAllowed && getDropdownAdditionalProps(options)),
            validators: getFieldValidators(metadataObject),
            placeholder: dataType === DATA_TYPE.ARRAY ? __(`Enter a valid ${dataType}`) : __('Type Here'),
          },
        };
        const renderOptions = _map(formFieldMetadataMapper, (metadataKey, propKey) => ({
          path: propKey,
          value: _get(metadataObject, [metadataKey], ''),
        }));

        return { ...acc, [featureFieldKey]: addToRenderOptions(staticFieldConfig, renderOptions) };
      },
      EMPTY_OBJECT
    )
);

export const getPayloadFromStateForFeatureInfo = (formValues, featureFieldMetadata = EMPTY_OBJECT) => {
  if (_isEmpty(featureFieldMetadata)) return EMPTY_OBJECT;

  const featureFields = _keys(featureFieldMetadata);
  const featureInfoFromFormValues = _pick(formValues, featureFields);
  const result = {};
  _forEach(featureInfoFromFormValues, (value, key) => {
    const dataType = _get(featureFieldMetadata, [key, 'dataType']);
    let fieldValue = value;
    if (dataType === DATA_TYPE.INTEGER || dataType === DATA_TYPE.ARRAY) {
      try {
        fieldValue = JSON.parse(value);
      } catch {
        fieldValue = value;
      }
    }
    _set(result, key, fieldValue);
  });
  return result;
};

export const dmsProviderFormatter = { label: _property('dmsProviderLabel'), value: _property('dmsProviderName') };

export const handleValidationSuccess = ({ setState, params }) => {
  const { errors } = params;
  setState({
    errors,
  });
};
