import produce from 'immer';

import _map from 'lodash/map';
import _keys from 'lodash/keys';
import _forEach from 'lodash/forEach';
import _toLower from 'lodash/toLower';
import _includes from 'lodash/includes';
import _size from 'lodash/size';
import _remove from 'lodash/remove';
import _head from 'lodash/head';
import _isArray from 'lodash/isArray';
import _uniq from 'lodash/uniq';
import _set from 'lodash/set';
import _noop from 'lodash/noop';

import {
  fetchRoleList,
  saveDataSourceAccessSettings,
} from 'pages/analyticsPortal/services/analyticsPortal.accessSettings';
import getDataFromResponse from 'tbase/utils/getDataFromResponse';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import {
  mapRoleToDepartment,
  getDataSourcePayload,
  sortRoleList,
  getIsUnsavedDataSourcePermissionExists,
  getIsUnsavedDashboardPermissionExists,
} from './accessSettings.general';
import { ACTION_TYPES } from '../constants/accessSettings.actionTypes';
import { saveAccessSettings } from '../services/accessSettings.services';
import {
  DASHBOARD_TAB,
  DATA_SOURCES_TAB,
} from '../organisms/accessSettingsRolesContent/constants/accessSettingsRolesContent.tabs';
import { EDIT, NO_ACCESS, VIEW } from '../constants/accessSettings.accessTypes';

const handleTabChange = ({ getState, setState, params }) => {
  const { value: selectedTab } = params;

  const {
    accessSettingsState,
    originalAccessSettingsState,
    selectedRole,
    updatedAccessTypeToResourceId,
    selectedTab: currentSelectedTab,
  } = getState();

  if (selectedTab === DATA_SOURCES_TAB.id) {
    const isUnsavedPermissionExists = getIsUnsavedDashboardPermissionExists(updatedAccessTypeToResourceId);
    if (isUnsavedPermissionExists) {
      return setState({ isUnsavedAccessSettingsModalVisible: true });
    }
    return setState({
      selectedTab,
    });
  }
  const isUnsavedDataSourcePermissionExists = getIsUnsavedDataSourcePermissionExists(
    accessSettingsState,
    originalAccessSettingsState,
    selectedRole,
    currentSelectedTab
  );

  if (isUnsavedDataSourcePermissionExists) {
    return setState({ isUnsavedAccessSettingsModalVisible: true });
  }
  return setState({
    selectedTab,
  });
};

const handleFetchList = async ({ getState, setState }) => {
  const { selectedRole } = getState();
  try {
    setState({ loading: true });
    const response = await fetchRoleList();
    const roles = getDataFromResponse(response);
    const mapRolesWithDept = mapRoleToDepartment(roles, selectedRole, setState);
    const sortedMappedRoleToShow = sortRoleList({ ascendingSort: true, rolesToShow: mapRolesWithDept });
    return setState({
      deptMappedRoles: sortedMappedRoleToShow,
      rolesToShow: sortedMappedRoleToShow,
    });
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Error in fetching Roles.')));
    return EMPTY_OBJECT;
  } finally {
    setState({ loading: false });
  }
};

const handleRoleSearch = ({ getState, setState, params }) => {
  const { deptMappedRoles } = getState();
  const searchQuery = params;

  if (searchQuery) {
    const tempMappedRoles = {};
    _map(_keys(deptMappedRoles), item => {
      const values = [];
      const categories = deptMappedRoles[item];

      _forEach(categories, role => {
        const itemName = _toLower(role?.name);
        if (_includes(itemName, searchQuery)) {
          values.push(role);
        }
      });

      if (_includes(item, searchQuery) || _size(values) > 0) {
        tempMappedRoles[item] = values;
      }
    });

    return setState({
      rolesToShow: tempMappedRoles,
    });
  }
  return setState({
    rolesToShow: deptMappedRoles,
  });
};

const handleSortType = ({ getState, setState }) => {
  const { ascendingSort, rolesToShow } = getState();
  const sortedMappedRoleToShow = sortRoleList({ ascendingSort, rolesToShow });

  setState({
    ascendingSort: !ascendingSort,
    rolesToShow: sortedMappedRoleToShow,
  });
};

const handleRoleMenuOpen = ({ setState, params }) => {
  const openRoles = params;
  setState({ expandedRolesList: openRoles });
};

const handleRoleSelection = ({ setState, params }) => {
  const selectedRoleKey = params;
  setState({ selectedRole: selectedRoleKey });
};

const handleUpdateAccessSettings = ({ setState, params, getState }) => {
  const { tableValue, path } = params;
  const { accessSettingsState } = getState();
  const updatedAccessSettingsState = produce(accessSettingsState, draft => _set(draft, path, tableValue));
  setState({
    accessSettingsState: updatedAccessSettingsState,
  });
};

const handleSetOriginalAccessSettings = ({ setState, params, getState }) => {
  const { tableValue, path } = params;
  const { originalAccessSettingsState } = getState();
  const updatedAccessSettingsState = produce(originalAccessSettingsState, draft => _set(draft, path, tableValue));
  setState({
    originalAccessSettingsState: updatedAccessSettingsState,
  });
};

const handleSaveAccessSettingsSuccess = (setSaveInProgress, setState, setShowUnsavedCancelDialog) => () => {
  setSaveInProgress(false);
  setShowUnsavedCancelDialog(false);
  toaster(TOASTER_TYPE.SUCCESS, __('Access Settings Saved Successfully'));
  setState({
    updatedAccessTypeToResourceId: {
      [NO_ACCESS.id]: [],
      [VIEW.id]: [],
      [EDIT.id]: [],
    },
    isUnsavedAccessSettingsModalVisible: false,
  });
};

const handleSaveDataSourceAccessSettingsSuccess =
  (setSaveInProgress, setState, accessSettingsState, setShowUnsavedCancelDialog) => () => {
    setSaveInProgress(false);
    setShowUnsavedCancelDialog(false);
    toaster(TOASTER_TYPE.SUCCESS, __('Access Settings Saved Successfully'));
    setState({
      originalAccessSettingsState: accessSettingsState,
      isUnsavedAccessSettingsModalVisible: false,
    });
  };

const handleSaveAccessSettingsFailure = setSaveInProgress => () => {
  setSaveInProgress(false);
  return toaster(TOASTER_TYPE.ERROR, __('Failed to Save Access Settings'));
};

const handleSaveAccessSettings = async ({ getState, setState, params }) => {
  const { isUnsavedAccessSettingsModalVisible } = getState();
  const {
    setSaveInProgress,
    selectedRoleId,
    updatedAccessTypeToResourceId,
    accessSettingsState,
    selectedTab,
    setShowUnsavedCancelDialog = _noop,
  } = params;
  setSaveInProgress(true);

  if (selectedTab === DATA_SOURCES_TAB.id) {
    const { READ_ONLY, NO_ACCESS } = getDataSourcePayload(accessSettingsState, selectedRoleId);

    const saveDataSourceAccessSettingsPayload = {
      accessTypeToResourceId: {
        READ_ONLY,
        NO_ACCESS,
      },
    };

    await saveDataSourceAccessSettings(saveDataSourceAccessSettingsPayload, selectedRoleId)
      .then(
        handleSaveDataSourceAccessSettingsSuccess(
          setSaveInProgress,
          setState,
          accessSettingsState,
          setShowUnsavedCancelDialog
        )
      )
      .catch(handleSaveAccessSettingsFailure(setSaveInProgress));

    if (isUnsavedAccessSettingsModalVisible) {
      return setState({
        selectedTab: DASHBOARD_TAB.id,
      });
    }
    return setState();
  }

  const saveAccessSettingsPayload = {
    accessTypeToResourceId: updatedAccessTypeToResourceId,
  };
  await saveAccessSettings(saveAccessSettingsPayload, selectedRoleId)
    .then(handleSaveAccessSettingsSuccess(setSaveInProgress, setState, setShowUnsavedCancelDialog))
    .catch(handleSaveAccessSettingsFailure(setSaveInProgress));

  if (isUnsavedAccessSettingsModalVisible) {
    return setState({
      selectedTab: DATA_SOURCES_TAB.id,
    });
  }
  return setState();
};

const makeUpdatedAccessTypeToResourceId = ({
  updatedAccessTypeToResourceId,
  updatedResourceTypeValue,
  updatedResourceId,
  previousDashboardAccessValue,
}) => {
  const previousDashboardAccessValueLabel = _isArray(previousDashboardAccessValue)
    ? _head(previousDashboardAccessValue)
    : previousDashboardAccessValue;
  _remove(
    updatedAccessTypeToResourceId?.[previousDashboardAccessValueLabel],
    resourceId => resourceId === updatedResourceId
  ); // to remove existing value from the state
  const previousUpdatedResourceTypeValueState = updatedAccessTypeToResourceId?.[updatedResourceTypeValue] || [];
  const updatedResourceTypeValueWithNewResourceId = [...previousUpdatedResourceTypeValueState, updatedResourceId];
  const uniqueUpdatedAccessTypeToResourceId = _uniq(updatedResourceTypeValueWithNewResourceId);
  return {
    ...updatedAccessTypeToResourceId,
    [updatedResourceTypeValue]: updatedResourceTypeValueWithNewResourceId,
  };
};

const handleUpdateAccessTypeToRoleId = ({ getState, setState, params }) => {
  const { updatedAccessTypeToResourceId } = getState();
  const { updatedResourceTypeValue, updatedResourceId, previousDashboardAccessValue } = params;
  const newUpdatedAccessTypeToResourceId = makeUpdatedAccessTypeToResourceId({
    updatedAccessTypeToResourceId,
    updatedResourceTypeValue,
    updatedResourceId,
    previousDashboardAccessValue,
  });
  setState({ updatedAccessTypeToResourceId: newUpdatedAccessTypeToResourceId });
};

const handleResetAccessSettings = ({ getState, setState, params }) => {
  const { originalAccessSettingsState } = params;
  const { selectedTab, isUnsavedAccessSettingsModalVisible } = getState();
  let updatedTab = selectedTab;

  if (isUnsavedAccessSettingsModalVisible) {
    updatedTab = selectedTab === DATA_SOURCES_TAB.id ? DASHBOARD_TAB.id : DATA_SOURCES_TAB.id;
  }

  setState({
    accessSettingsState: originalAccessSettingsState,
    updatedAccessTypeToResourceId: {
      [NO_ACCESS.id]: [],
      [VIEW.id]: [],
      [EDIT.id]: [],
    },
    isUnsavedAccessSettingsModalVisible: false,
    selectedTab: updatedTab,
  });
};

const ACTION_HANDLERS = {
  [ACTION_TYPES.TAB_CHANGE]: handleTabChange,
  [ACTION_TYPES.FETCH_LIST]: handleFetchList,
  [ACTION_TYPES.SEARCH_ROLE]: handleRoleSearch,
  [ACTION_TYPES.SORT_LIST]: handleSortType,
  [ACTION_TYPES.ROLE_MENU_OPEN]: handleRoleMenuOpen,
  [ACTION_TYPES.ROLE_SELECTION]: handleRoleSelection,
  [ACTION_TYPES.UPDATE_ACCESS_SETTINGS]: handleUpdateAccessSettings,
  [ACTION_TYPES.SET_ORIGINAL_ACCESS_STATE]: handleSetOriginalAccessSettings,
  [ACTION_TYPES.SAVE_ACCESS_SETTINGS]: handleSaveAccessSettings,
  [ACTION_TYPES.RESET_ACCESS_SETTINGS]: handleResetAccessSettings,
  [ACTION_TYPES.UPDATE_ACCESS_TYPE_TO_ROLE_ID]: handleUpdateAccessTypeToRoleId,
};

export default ACTION_HANDLERS;
