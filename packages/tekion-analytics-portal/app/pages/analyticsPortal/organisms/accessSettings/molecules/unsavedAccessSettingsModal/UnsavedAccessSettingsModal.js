import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';

// Components
import Modal from 'tcomponents/molecules/Modal';
import Icon, { SIZES } from 'tcomponents/atoms/FontIcon';
import Content from 'tcomponents/atoms/Content';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { ACTION_TYPES } from '../../constants/accessSettings.actionTypes';

import styles from './unsavedAccessSettingsModal.module.scss';

function UnsavedAccessSettingsModal(props) {
  const {
    isVisible,
    onAction,
    updatedAccessTypeToResourceId,
    selectedTab,
    selectedRoleId,
    accessSettingsState,
    originalAccessSettingsState,
  } = props;
  const [saveInProgress, setSaveInProgress] = useState(false);

  const handleSaveClick = useCallback(() => {
    onAction({
      type: ACTION_TYPES.SAVE_ACCESS_SETTINGS,
      payload: {
        selectedTab,
        updatedAccessTypeToResourceId,
        accessSettingsState,
        selectedRoleId,
        setSaveInProgress,
      },
    });
  }, [onAction, selectedTab, updatedAccessTypeToResourceId, accessSettingsState, selectedRoleId]);

  const handleCancelClick = useCallback(() => {
    onAction({
      type: ACTION_TYPES.RESET_ACCESS_SETTINGS,
      payload: {
        originalAccessSettingsState,
        selectedRoleId,
        setSaveInProgress,
      },
    });
  }, [onAction, originalAccessSettingsState, selectedRoleId]);

  return (
    <Modal
      title={__('Unsaved Changes')}
      visible={isVisible}
      submitBtnText={__('Save Changes')}
      secondaryBtnText={__('Continue Without Saving')}
      width={Modal.SIZES.SM}
      onCancel={handleCancelClick}
      onSubmit={handleSaveClick}
      loading={saveInProgress}
      destroyOnClose>
      <Content className={styles.modalContent}>
        <div>
          <Icon size={SIZES.XXL} className={styles.alertIcon}>
            icon-alert-outline
          </Icon>
        </div>
        <div className="m-x-16">
          <div className="bold m-b-8">{__('You have unsaved changes')}</div>
          <div>
            {__('Some information have been changed and is unsaved. Are you sure you want to continue without saving?')}
          </div>
        </div>
      </Content>
    </Modal>
  );
}

UnsavedAccessSettingsModal.propTypes = {
  updatedAccessTypeToResourceId: PropTypes.object,
  onAction: PropTypes.func,
  selectedRoleId: PropTypes.string,
  originalAccessSettingsState: PropTypes.object,
  isVisible: PropTypes.bool,
};

UnsavedAccessSettingsModal.defaultProps = {
  updatedAccessTypeToResourceId: EMPTY_OBJECT,
  onAction: _noop,
  selectedRoleId: undefined,
  originalAccessSettingsState: EMPTY_OBJECT,
  isVisible: false,
};

export default UnsavedAccessSettingsModal;
