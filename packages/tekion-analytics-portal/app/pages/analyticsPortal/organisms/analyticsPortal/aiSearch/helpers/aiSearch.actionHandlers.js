import { createDashboardEvent, createReportEvent } from 'pages/analyticsPortal/services/analyticsPortal.home';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import { handleNoteCellClick } from '../../helpers/analyticsPortal.general';

// constants
import { TS_EVENT_TYPES } from '../../constants/analyticsPortal.constants';
import ACTION_TYPES from '../constants/aiSearch.actionTypes';
import { EMPTY_OBJECT } from 'tbase/app.constants';

const handleCloseNotesModal = ({ setState }) => {
  setState({
    isNotesModalVisible: false,
  });
};

const handleOpenNotesModal = ({ setState, params }) => handleNoteCellClick({ setState, params });

const handleSaveOrCreateEvent = async ({ setState, params }) => {
  const { type, identifier } = params;

  try {
    setState({ loading: true });
    if (type === TS_EVENT_TYPES.LIVEBOARD) {
      await createDashboardEvent({ type, identifier });
    } else {
      // type === ANSWER and for any other type
      await createReportEvent({ type, identifier });
    }
    return setState({});
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __(`Error in Creating ${type}`)));
    return EMPTY_OBJECT;
  } finally {
    setState({ loading: false });
  }
};

const ACTION_HANDLERS = {
  [ACTION_TYPES.OPEN_NOTES_MODAL]: handleOpenNotesModal,
  [ACTION_TYPES.CLOSE_NOTES_MODAL]: handleCloseNotesModal,
  [ACTION_TYPES.EMBED_EVENT_PERSIST]: handleSaveOrCreateEvent,
};

export default ACTION_HANDLERS;
