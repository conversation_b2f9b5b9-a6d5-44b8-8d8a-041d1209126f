import ACTION_TYPES from '../constants/reportScheduleManager.actionTypes';

const handleCreateNewSchedule = ({ setState, params }) => {
  const { scheduleConfigFormValues } = params;
  console.log(scheduleConfigFormValues, ' --- scheduleConfigFormValues --- ');
  try {
    setState({ isSaving: true });
  } catch (error) {
    console.log(error, ' --- error --- ');
  } finally {
    setState({ isSaving: false });
  }
};

const ACTION_HANDLERS = {
  [ACTION_TYPES.CREATE_NEW_SCHEDULE]: handleCreateNewSchedule,
};

export default ACTION_HANDLERS;
