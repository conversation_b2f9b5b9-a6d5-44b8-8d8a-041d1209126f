import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

// lodash
import _noop from 'lodash/noop';

// constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { CREATE_SCHEDULE_REPORT_MODAL_TITLE } from '../../constants/reportScheduleManager.general';

// helpers
import {
  closeScheduleModal,
  submitNewScheduleReport,
} from '../../../reportSchedulerModal/helpers/reportSchedulerModal.general';
import { getInitialValues } from './helpers/createScheduleReportModal.general';

// components
import ReportSchedulerModal from '../../../reportSchedulerModal';

const CreateScheduleReportModal = props => {
  const { onAction, onParentAction, clickedContentItem, isReportSchedulerModalVisible, isSaving } = props;

  const handleCancel = useMemo(() => closeScheduleModal(onParentAction), [onParentAction]);
  const handleSubmit = useMemo(() => submitNewScheduleReport({ onAction }), [onAction]);

  const initialValues = useMemo(() => getInitialValues(clickedContentItem), [clickedContentItem]);

  return (
    <ReportSchedulerModal
      title={CREATE_SCHEDULE_REPORT_MODAL_TITLE}
      submitBtnText={__('Save')}
      onAction={onAction}
      onParentAction={onParentAction}
      onCancel={handleCancel}
      onSubmit={handleSubmit}
      clickedContentItem={clickedContentItem}
      isReportSchedulerModalVisible={isReportSchedulerModalVisible}
      isSaving={isSaving}
      initialValues={initialValues}
    />
  );
};

CreateScheduleReportModal.propTypes = {
  onAction: PropTypes.func,
  onParentAction: PropTypes.func,
  clickedContentItem: PropTypes.object,
  isReportSchedulerModalVisible: PropTypes.bool,
  isSaving: PropTypes.bool,
};

CreateScheduleReportModal.defaultProps = {
  onAction: _noop,
  onParentAction: _noop,
  clickedContentItem: EMPTY_OBJECT,
  isReportSchedulerModalVisible: false,
  isSaving: false,
};

export default CreateScheduleReportModal;
