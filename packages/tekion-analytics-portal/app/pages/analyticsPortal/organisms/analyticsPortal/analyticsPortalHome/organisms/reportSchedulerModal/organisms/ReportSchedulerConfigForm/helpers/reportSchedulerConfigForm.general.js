// Utils
import { getToday } from 'tbase/utils/dateUtils';

// Constants
import { DAILY, WEEKLY, MONTHLY, HOURLY } from '../constants/reportSchedulerConfigForm.frequencyOptions';
import { USER, ROLE } from '../constants/reportSchedulerConfigForm.recipientTypeOptions';
import { OCCURENCES, SPECIFIC_DATE } from '../constants/reportSchedulerConfigForm.endsOnTypeOptions';

export const isPastDate = current => current.isBefore(getToday());

export const isScheduledHourly = frequency => frequency === HOURLY.id;

export const isScheduledDaily = frequency => frequency === DAILY.id;

export const isScheduledWeekly = frequency => frequency === WEEKLY.id;

export const isScheduledMonthly = frequency => frequency === MONTHLY.id;

export const isEndsOnOccurences = endsOnType => endsOnType === OCCURENCES.id;

export const isEndsOnSpecificDate = endsOnType => endsOnType === SPECIFIC_DATE.id;

export const isUsersRecipientType = recipientType => recipientType === USER.value;

export const isRolesRecipientType = recipientType => recipientType === ROLE.value;

export const isAddCustomMessage = addCustomMessage => addCustomMessage === true;
