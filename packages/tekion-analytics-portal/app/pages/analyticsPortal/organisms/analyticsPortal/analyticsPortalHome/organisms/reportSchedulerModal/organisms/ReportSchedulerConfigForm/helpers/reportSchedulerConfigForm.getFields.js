/* eslint-disable import/order */

// Utils
import addToRenderOptions from 'tbase/utils/addToRenderOptions';

// Constants
import {
  SCHEDULE_NAME_FIELD,
  ADD_CUSTOM_MESSAGE_FIELD,
  CUSTOM_MESSAGE_FIELD,
  FREQUENCY_FIELD,
  SHARE_FORMAT_FIELD,
  REPORT_FORMAT_FIELD,
  START_DATE_FIELD,
  START_TIME_FIELD,
  DAILY_SCHEDULE_FIELD,
  ENDS_ON_TYPE_FIELD,
  END_DATE_FIELD,
  RECIPIENT_TYPE_FIELD,
  USERS_FIELD,
  ROLES_FIELD,
  HOURLY_SCHEDULE_FIELD,
  WEEKLY_SCHEDULE_FIELD,
  MONTHLY_SCHEDULE_FIELD,
  END_OCCURENCES_FIELD,
} from '../constants/reportSchedulerConfigForm.fields';

// Validators
import { isRequiredRule } from 'tbase/utils/formValidators';

const getFields = (formValues, allRoles, clickedContentItem) => ({
  [SCHEDULE_NAME_FIELD.id]: addToRenderOptions(SCHEDULE_NAME_FIELD, [
    {
      path: 'validators',
      value: [isRequiredRule],
    },
  ]),
  [ADD_CUSTOM_MESSAGE_FIELD.id]: ADD_CUSTOM_MESSAGE_FIELD,
  [CUSTOM_MESSAGE_FIELD.id]: addToRenderOptions(CUSTOM_MESSAGE_FIELD, [
    {
      path: 'validators',
      value: [isRequiredRule],
    },
  ]),
  [SHARE_FORMAT_FIELD.id]: SHARE_FORMAT_FIELD,
  [REPORT_FORMAT_FIELD.id]: REPORT_FORMAT_FIELD,
  [START_DATE_FIELD.id]: START_DATE_FIELD,
  [START_TIME_FIELD.id]: START_TIME_FIELD,
  [FREQUENCY_FIELD.id]: FREQUENCY_FIELD,
  [HOURLY_SCHEDULE_FIELD.id]: HOURLY_SCHEDULE_FIELD,
  [DAILY_SCHEDULE_FIELD.id]: DAILY_SCHEDULE_FIELD,
  [WEEKLY_SCHEDULE_FIELD.id]: WEEKLY_SCHEDULE_FIELD,
  [MONTHLY_SCHEDULE_FIELD.id]: MONTHLY_SCHEDULE_FIELD,
  [ENDS_ON_TYPE_FIELD.id]: ENDS_ON_TYPE_FIELD,
  [END_OCCURENCES_FIELD.id]: END_OCCURENCES_FIELD,
  [END_DATE_FIELD.id]: END_DATE_FIELD,
  [RECIPIENT_TYPE_FIELD.id]: RECIPIENT_TYPE_FIELD,
  [USERS_FIELD.id]: USERS_FIELD,
  [ROLES_FIELD.id]: ROLES_FIELD,
});

export default getFields;
