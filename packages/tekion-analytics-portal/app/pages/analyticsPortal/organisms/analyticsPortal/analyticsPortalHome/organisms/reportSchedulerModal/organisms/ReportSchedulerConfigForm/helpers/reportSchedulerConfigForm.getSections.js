// Utils
import { filterRows } from 'tcomponents/organisms/FormBuilder/utils/general';

// Constants
import {
  SCHEDULE_NAME_FIELD,
  CUSTOM_MESSAGE_FIELD,
  ADD_CUSTOM_MESSAGE_FIELD,
  SHARE_FORMAT_FIELD,
  REPORT_FORMAT_FIELD,
  START_DATE_FIELD,
  START_TIME_FIELD,
  FREQUENCY_FIELD,
  DAILY_SCHEDULE_FIELD,
  ENDS_ON_TYPE_FIELD,
  END_DATE_FIELD,
  RECIPIENT_TYPE_FIELD,
  USERS_FIELD,
  ROLES_FIELD,
  WEEKLY_SCHEDULE_FIELD,
  MONTHLY_SCHEDULE_FIELD,
  HOURLY_SCHEDULE_FIELD,
  END_OCCURENCES_FIELD,
} from '../constants/reportSchedulerConfigForm.fields';

// helpers
import {
  isAddCustomMessage,
  isEndsOnOccurences,
  isEndsOnSpecificDate,
  isRolesRecipientType,
  isScheduledDaily,
  isScheduledHourly,
  isScheduledMonthly,
  isScheduledWeekly,
  isUsersRecipientType,
} from './reportSchedulerConfigForm.general';

const CUSTOM_MESSAGE_FIELD_CHECK_MAP = {
  [CUSTOM_MESSAGE_FIELD.id]: isAddCustomMessage,
};

const FREQUENCY_ROW_CHECKS_MAP = {
  [HOURLY_SCHEDULE_FIELD.id]: isScheduledHourly,
  [DAILY_SCHEDULE_FIELD.id]: isScheduledDaily,
  [WEEKLY_SCHEDULE_FIELD.id]: isScheduledWeekly,
  [MONTHLY_SCHEDULE_FIELD.id]: isScheduledMonthly,
};

const ENDS_ON_ROW_CHECKS_MAP = {
  [END_OCCURENCES_FIELD.id]: isEndsOnOccurences,
  [END_DATE_FIELD.id]: isEndsOnSpecificDate,
};

const RECIPIENT_TYPEROW_CHECKS_MAP = {
  [USERS_FIELD.id]: isUsersRecipientType,
  [ROLES_FIELD.id]: isRolesRecipientType,
};

const getScheduleDetailsSection = addCustomMessage => ({
  className: 'is-paddingless',
  header: {
    label: __('Schedule Details'),
    size: 4,
    className: 'm-b-8',
  },
  rows: [
    {
      columns: [SCHEDULE_NAME_FIELD.id, ADD_CUSTOM_MESSAGE_FIELD.id],
    },
    {
      columns: filterRows([CUSTOM_MESSAGE_FIELD.id], addCustomMessage, CUSTOM_MESSAGE_FIELD_CHECK_MAP),
    },
  ],
});

const getScheduleSection = (frequency, endsOnType) => ({
  className: 'is-paddingless',
  header: {
    label: __('Schedule'),
    size: 4,
    className: 'm-b-8',
  },
  rows: [
    {
      columns: [START_DATE_FIELD.id, START_TIME_FIELD.id],
    },
    {
      columns: filterRows(
        [
          FREQUENCY_FIELD.id,
          HOURLY_SCHEDULE_FIELD.id,
          DAILY_SCHEDULE_FIELD.id,
          WEEKLY_SCHEDULE_FIELD.id,
          MONTHLY_SCHEDULE_FIELD.id,
        ],
        frequency,
        FREQUENCY_ROW_CHECKS_MAP
      ),
    },
    {
      columns: filterRows(
        [ENDS_ON_TYPE_FIELD.id, END_OCCURENCES_FIELD.id, END_DATE_FIELD.id],
        endsOnType,
        ENDS_ON_ROW_CHECKS_MAP
      ),
    },
  ],
});

const getRecipientSection = recipientType => ({
  className: 'is-paddingless',
  header: {
    label: __('Recipients'),
    size: 4,
    className: 'm-b-8',
  },
  rows: [
    {
      columns: [RECIPIENT_TYPE_FIELD.id],
    },
    {
      columns: filterRows([USERS_FIELD.id, ROLES_FIELD.id], recipientType, RECIPIENT_TYPEROW_CHECKS_MAP),
    },
  ],
});

const SHARE_DETAILS_SECTION = {
  className: 'is-paddingless',
  header: {
    label: __('Share Details'),
    size: 4,
    className: 'm-b-8',
  },
  rows: [
    {
      columns: [SHARE_FORMAT_FIELD.id, REPORT_FORMAT_FIELD.id],
    },
  ],
};

const getSections = (formValues, permissions, clickedContentItem, scheduleConfig, fieldsMetadataByDataSource) => {
  console.log(formValues, ' --- formValues --- ', clickedContentItem);
  const addCustomMessage = formValues?.[ADD_CUSTOM_MESSAGE_FIELD.id];
  const frequency = formValues?.[FREQUENCY_FIELD.id];
  const endsOnType = formValues?.[ENDS_ON_TYPE_FIELD.id];
  const recipientType = formValues?.[RECIPIENT_TYPE_FIELD.id];

  return [
    getScheduleDetailsSection(addCustomMessage),
    SHARE_DETAILS_SECTION,
    getScheduleSection(frequency, endsOnType),
    getRecipientSection(recipientType),
  ];
};

export default getSections;
