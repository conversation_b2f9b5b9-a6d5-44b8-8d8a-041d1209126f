// Readers
import roleReader from 'tbase/readers/Role';

// Utils
import standardFieldOptionMapper from 'tbase/utils/optionMappers/standardFieldMapper';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';

const ROLE_MAPPER = {
  value: roleReader.id,
  label: roleReader.name,
};

const makeRoleOptions = (rolesList = EMPTY_ARRAY) => {
  const roleOptions = standardFieldOptionMapper(undefined, rolesList, ROLE_MAPPER);
  return roleOptions;
};

export default makeRoleOptions;
