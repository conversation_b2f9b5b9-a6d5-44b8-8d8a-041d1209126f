// Lodash
import _size from 'lodash/size';

import * as DateUtils from 'tbase/utils/dateUtils';

// Constants
import { PASSWORD_SIZE as PASSCODE_SIZE } from 'twidgets/appServices/payroll/fieldRenderers/passwordEntryBoxField';
import FIELD_IDS from '../constants/reportSchedulerConfigForm.fieldIds';

const addHoursAndMinutesToDate = (startDate, startTime) => {
  const minutes = DateUtils.toMoment(startTime).minutes();
  const hour = DateUtils.toMoment(startTime).hour();
  return DateUtils.toMoment(startDate)
    .set('hour', hour)
    .set('minute', minutes)
    .set('seconds', 0)
    .set('millisecond', 0)
    .valueOf();
};

const getStartDateAndTime = (formValues, value) => {
  const startDate = formValues?.[FIELD_IDS.START_DATE];
  const startDateAndTime = addHoursAndMinutesToDate(startDate, value);
  return startDateAndTime;
};

export const isAfterDate = (fieldId, value, formValues) => {
  const currentDateAndTime = DateUtils.toMoment();
  const startDateAndTime = getStartDateAndTime(formValues, value);
  if (currentDateAndTime && startDateAndTime) {
    const startDateAndTimeValue = DateUtils.toMoment(startDateAndTime);
    if (DateUtils.isBefore(currentDateAndTime, startDateAndTimeValue, { unit: 'minute' })) {
      return {
        isValid: false,
        message: __('Start Date and Time field cannot be in the past'),
      };
    }
  }

  return { isValid: true };
};

export const isEndDateAfterStartDate = (fieldId, value, formValues) => {
  const startDate = formValues?.[FIELD_IDS.START_DATE];
  const endDate = DateUtils.toMoment(value);
  if (startDate && endDate) {
    if (DateUtils.isBefore(startDate, endDate, { unit: 'day' })) {
      return {
        isValid: false,
        message: __('End Date cannot be lesser than Start Date.'),
      };
    }
  }

  return { isValid: true };
};

const isValidPasscode = passcode => {
  const passcodeLength = _size(passcode);
  return passcodeLength === PASSCODE_SIZE;
};

const getPasscodeSizeValidationFailureMessage = () => {
  const passcodeSizeValidationFailureMessage = __('Passcode should be of {{numberOfDigits}} digits', {
    numberOfDigits: PASSCODE_SIZE,
  });
  return passcodeSizeValidationFailureMessage;
};

export const validatePasscodeLength = (fieldId, passcode) => {
  const isValid = isValidPasscode(passcode);
  return isValid ? { isValid } : { isValid, message: getPasscodeSizeValidationFailureMessage() };
};
