export const MOCK_SHARE_CONFIG_ACCESS_LIST = {
  NO_ACCESS: ['4_APClerk', '4_APPT_KIOSK_USER', '4_ARClerk'],
  READ_ONLY: [],
  MODIFY: [],
};

export const MOCK_CLICKED_CONTENT_ITEM = {
  id: '68397fa86d2fa9589a512a05',
  referenceId: '034aab15-4533-4789-86b7-c52e10b13d7d',
  name: 'Test RO Sharing Model Liveboard',
  description: '',
  originType: 'STANDARD',
  contentType: 'DASHBOARD',
  createdBy: '60a6485f-ea9f-47b1-9ce1-0f49c7ce774e',
  createdTime: 1748598696818,
  modifiedBy: '60a6485f-ea9f-47b1-9ce1-0f49c7ce774e',
  modifiedTime: 1748598696818,
  accessType: 'READ_ONLY',
  favSelected: true,
};

export const MOCK_SELECTED_DEALER_ID = 4;

export const MOCK_ACCESSIBLE_SHARE_CONFIGS = {
  permissions: [
    {
      dealerId: '4',
      workSpaceId: null,
      accessTypeToRoleMap: {
        READ_ONLY: [
          {
            roleId: '4_BACK_OFFICE_CLERK',
            roleName: 'Back Office Clerk',
            department: 'SALES',
          },
          {
            roleId: '4_BDCSuperAdmin',
            roleName: 'BDCSuperAdmin',
            department: 'SERVICE',
          },
          {
            roleId: '4_Cashier',
            roleName: 'Cashier',
            department: 'SALES',
          },
          {
            roleId: '4_ContractsDeskClerk',
            roleName: 'Contracts Desk Clerk',
            department: 'ACCOUNTING',
          },
        ],
      },
    },
  ],
};
