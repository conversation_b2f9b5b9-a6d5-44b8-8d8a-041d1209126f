import { mockGetState, mockSetState } from 'tbase/testTools/mocks/state';

import * as services from 'pages/analyticsPortal/services/analyticsPortal.home';
import * as notification from 'tcomponents/organisms/NotificationWrapper';
import * as readers from 'pages/analyticsPortal/organisms/analyticsPortal/analyticsPortalHome/readers/BaseContentsList';
import * as utils from 'tbase/utils/getDataFromResponse';
import { TABLE_ACTION_TYPES } from 'tcomponents/organisms/TableManager';
import PARENT_ACTION_TYPES from '../../baseContentsList/constants/baseContentsList.actionTypes';
import ACTION_HANDLERS from '../helpers/shareModal.actionHandlers';
import ACTION_TYPES from '../constants/shareModal.actionTypes';
import {
  MOCK_ACCESSIBLE_SHARE_CONFIGS,
  MOCK_CLICKED_CONTENT_ITEM,
  MOCK_SELECTED_DEALER_ID,
  MOCK_SHARE_CONFIG_ACCESS_LIST,
} from '../__mocks__/shareModal.actionHandlers.mock';
import { SHARE_MODAL_FORM_LEVEL } from '../constants/shareModal.FormTypes';
import { getContentPermissionForDealer } from '../helpers/shareModal.general';
import { MODIFY_ACCESS_TYPES_ID } from '../constants/shareModal.general';

// mock constants
const mockContentId = 'abc123';
const mockOriginType = 'dashboard';
const mockResponse = { data: 'mocked response' };

jest.mock('tcomponents/organisms/NotificationWrapper', () => ({
  toaster: jest.fn(),
  TOASTER_TYPE: {
    SUCCESS: 'success',
    ERROR: 'error',
  },
}));

describe('ACTION_HANDLERS', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('handleSubmitShareModal should save permission config and trigger success flow', async () => {
    const handleSubmitShareModal = ACTION_HANDLERS[ACTION_TYPES.SUBMIT_SHARE_MODAL];
    const mockOnParentAction = jest.fn();

    const getState = mockGetState.mockReturnValue({
      shareConfigAccessList: MOCK_SHARE_CONFIG_ACCESS_LIST,
      clickedContentItem: MOCK_CLICKED_CONTENT_ITEM,
      selectedDealerId: MOCK_SELECTED_DEALER_ID,
      onParentAction: mockOnParentAction,
    });

    readers.default.id = jest.fn(() => mockContentId);
    readers.default.originType = jest.fn(() => mockOriginType);

    jest.spyOn(services, 'saveContentPermissionConfig').mockResolvedValue(mockResponse);
    jest.spyOn(utils, 'default').mockResolvedValue(undefined); // getDataFromResponse
    jest.spyOn(notification, 'toaster');

    await handleSubmitShareModal({ getState, setState: mockSetState });

    // Assert
    expect(mockSetState).toHaveBeenCalledWith({ isShareInProgress: true });
    expect(services.saveContentPermissionConfig).toHaveBeenCalledWith(mockContentId, {
      rolesAccess: [
        {
          dealerId: MOCK_SELECTED_DEALER_ID,
          accessTypeToRoleMap: MOCK_SHARE_CONFIG_ACCESS_LIST,
        },
      ],
      originType: mockOriginType,
    });

    expect(notification.toaster).toHaveBeenCalledWith('success', expect.any(String));

    expect(mockOnParentAction).toHaveBeenCalledWith({ type: TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH });
    expect(mockOnParentAction).toHaveBeenCalledWith({ type: PARENT_ACTION_TYPES.CANCEL_SHARE_MODAL });

    expect(mockSetState).toHaveBeenLastCalledWith({ isShareInProgress: false });
  });

  it('handleSubmitShareModal should handle error scenario and show error toaster', async () => {
    const handleSubmitShareModal = ACTION_HANDLERS[ACTION_TYPES.SUBMIT_SHARE_MODAL];
    const mockOnParentAction = jest.fn();
    const mockError = new Error('Error in Saving Share Permission Config');

    const getState = mockGetState.mockReturnValue({
      shareConfigAccessList: MOCK_SHARE_CONFIG_ACCESS_LIST,
      clickedContentItem: MOCK_CLICKED_CONTENT_ITEM,
      selectedDealerId: MOCK_SELECTED_DEALER_ID,
      onParentAction: mockOnParentAction,
    });

    readers.default.id = jest.fn(() => mockContentId);
    readers.default.originType = jest.fn(() => mockOriginType);

    jest.spyOn(services, 'saveContentPermissionConfig').mockRejectedValue(mockError);
    jest.spyOn(notification, 'toaster');

    await handleSubmitShareModal({ getState, setState: mockSetState });

    expect(mockSetState).toHaveBeenCalledWith({ isShareInProgress: true });

    expect(notification.toaster).toHaveBeenCalledWith('error', 'Error in Saving Share Permission Config');

    expect(mockSetState).toHaveBeenLastCalledWith({ isShareInProgress: false });
  });

  it('should set accessList to ADD_ROLES_FORM', () => {
    const handleAddRoles = ACTION_HANDLERS[ACTION_TYPES.ADD_ROLES];
    const mockSetState = jest.fn();

    handleAddRoles({ setState: mockSetState });

    expect(mockSetState).toHaveBeenCalledWith({
      accessList: SHARE_MODAL_FORM_LEVEL.ADD_ROLES_FORM,
    });
  });

  it('should fetch and set share configs successfully', async () => {
    const handleFetchAccessibleShareConfigs = ACTION_HANDLERS[ACTION_TYPES.FETCH_ACCESSIBLE_SHARE_CONFIGS];

    const getState = mockGetState.mockReturnValue({
      clickedContentItem: MOCK_CLICKED_CONTENT_ITEM,
      selectedDealerId: MOCK_SELECTED_DEALER_ID,
    });

    readers.default.id = jest.fn(() => mockContentId);

    const mockFilteredData = { view: ['role-1'] };

    // ✅ Proper mocks
    jest.spyOn(services, 'fetchContentPermissionConfig').mockResolvedValue({ data: MOCK_ACCESSIBLE_SHARE_CONFIGS });
    jest.spyOn(utils, 'default').mockResolvedValue(MOCK_ACCESSIBLE_SHARE_CONFIGS);
    getContentPermissionForDealer.mockReturnValue(mockFilteredData); // ✅ mocked version

    await handleFetchAccessibleShareConfigs({ getState, setState: mockSetState });

    expect(mockSetState).toHaveBeenCalledWith({ isFetchingConfigs: true });
    expect(services.fetchContentPermissionConfig).toHaveBeenCalledWith(mockContentId, MODIFY_ACCESS_TYPES_ID.join(','));
    expect(utils.default).toHaveBeenCalledWith({ data: MOCK_ACCESSIBLE_SHARE_CONFIGS });
    expect(getContentPermissionForDealer).toHaveBeenCalledWith(
      MOCK_ACCESSIBLE_SHARE_CONFIGS.permissions,
      MOCK_SELECTED_DEALER_ID
    );
    expect(mockSetState).toHaveBeenCalledWith({
      fetchedShareConfigs: mockFilteredData,
      shareConfigs: mockFilteredData,
    });
    expect(mockSetState).toHaveBeenLastCalledWith({ isFetchingConfigs: false });
  });
});
