import { mockGetState, mockSetState } from 'tbase/testTools/mocks/state';

import * as services from 'pages/analyticsPortal/services/analyticsPortal.home';
import * as notification from 'tcomponents/organisms/NotificationWrapper';
import * as readers from 'pages/analyticsPortal/organisms/analyticsPortal/analyticsPortalHome/readers/BaseContentsList';
import * as utils from 'tbase/utils/getDataFromResponse';
import { TABLE_ACTION_TYPES } from 'tcomponents/organisms/TableManager';
import * as shareModalGeneral from '../helpers/shareModal.general'; // step 1
import PARENT_ACTION_TYPES from '../../baseContentsList/constants/baseContentsList.actionTypes';
import ACTION_HANDLERS from '../helpers/shareModal.actionHandlers';
import ACTION_TYPES from '../constants/shareModal.actionTypes';
import {
  MOCK_ACCESSIBLE_SHARE_CONFIGS,
  MOCK_CLICKED_CONTENT_ITEM,
  MOCK_RESTRICTED_SHARE_CONFIGS,
  MOCK_SELECTED_DEALER_ID,
  MOCK_SHARE_CONFIG,
  MOCK_SHARE_CONFIG_ACCESS_LIST,
} from '../__mocks__/shareModal.actionHandlers.mock';
import { SHARE_MODAL_FORM_LEVEL } from '../constants/shareModal.FormTypes';
import { getContentPermissionForDealer } from '../helpers/shareModal.general';
import { MODIFY_ACCESS_TYPES_ID, NO_ACCESS_TYPE_ID } from '../constants/shareModal.general';
import { EMPTY_ARRAY } from 'tbase/app.constants';
import { EDIT, NO_ACCESS, VIEW } from '../molecules/shareContenInputTable/constants/accessSettings.accessTypes';

// mock constants
const mockContentId = 'abc123';
const mockOriginType = 'dashboard';
const mockResponse = { data: 'mocked response' };

jest.mock('tcomponents/organisms/NotificationWrapper', () => ({
  toaster: jest.fn(),
  TOASTER_TYPE: {
    SUCCESS: 'success',
    ERROR: 'error',
  },
}));

describe('ACTION_HANDLERS', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('handleSubmitShareModal should save permission config and trigger success flow', async () => {
    const handleSubmitShareModal = ACTION_HANDLERS[ACTION_TYPES.SUBMIT_SHARE_MODAL];
    const mockOnParentAction = jest.fn();

    const getState = mockGetState.mockReturnValue({
      shareConfigAccessList: MOCK_SHARE_CONFIG_ACCESS_LIST,
      clickedContentItem: MOCK_CLICKED_CONTENT_ITEM,
      selectedDealerId: MOCK_SELECTED_DEALER_ID,
      onParentAction: mockOnParentAction,
    });

    readers.default.id = jest.fn(() => mockContentId);
    readers.default.originType = jest.fn(() => mockOriginType);

    jest.spyOn(services, 'saveContentPermissionConfig').mockResolvedValue(mockResponse);
    jest.spyOn(utils, 'default').mockResolvedValue(undefined); // getDataFromResponse
    jest.spyOn(notification, 'toaster');

    await handleSubmitShareModal({ getState, setState: mockSetState });

    // Assert
    expect(mockSetState).toHaveBeenCalledWith({ isShareInProgress: true });
    expect(services.saveContentPermissionConfig).toHaveBeenCalledWith(mockContentId, {
      rolesAccess: [
        {
          dealerId: MOCK_SELECTED_DEALER_ID,
          accessTypeToRoleMap: MOCK_SHARE_CONFIG_ACCESS_LIST,
        },
      ],
      originType: mockOriginType,
    });

    expect(notification.toaster).toHaveBeenCalledWith('success', expect.any(String));

    expect(mockOnParentAction).toHaveBeenCalledWith({ type: TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH });
    expect(mockOnParentAction).toHaveBeenCalledWith({ type: PARENT_ACTION_TYPES.CANCEL_SHARE_MODAL });

    expect(mockSetState).toHaveBeenLastCalledWith({ isShareInProgress: false });
  });

  it('handleSubmitShareModal should handle error scenario and show error toaster', async () => {
    const handleSubmitShareModal = ACTION_HANDLERS[ACTION_TYPES.SUBMIT_SHARE_MODAL];
    const mockOnParentAction = jest.fn();
    const mockError = new Error('Error in Saving Share Permission Config');

    const getState = mockGetState.mockReturnValue({
      shareConfigAccessList: MOCK_SHARE_CONFIG_ACCESS_LIST,
      clickedContentItem: MOCK_CLICKED_CONTENT_ITEM,
      selectedDealerId: MOCK_SELECTED_DEALER_ID,
      onParentAction: mockOnParentAction,
    });

    readers.default.id = jest.fn(() => mockContentId);
    readers.default.originType = jest.fn(() => mockOriginType);

    jest.spyOn(services, 'saveContentPermissionConfig').mockRejectedValue(mockError);
    jest.spyOn(notification, 'toaster');

    await handleSubmitShareModal({ getState, setState: mockSetState });

    expect(mockSetState).toHaveBeenCalledWith({ isShareInProgress: true });

    expect(notification.toaster).toHaveBeenCalledWith('error', 'Error in Saving Share Permission Config');

    expect(mockSetState).toHaveBeenLastCalledWith({ isShareInProgress: false });
  });

  it('should set accessList to ADD_ROLES_FORM', () => {
    const handleAddRoles = ACTION_HANDLERS[ACTION_TYPES.ADD_ROLES];
    const mockSetState = jest.fn();

    handleAddRoles({ setState: mockSetState });

    expect(mockSetState).toHaveBeenCalledWith({
      accessList: SHARE_MODAL_FORM_LEVEL.ADD_ROLES_FORM,
    });
  });

  it('handleFetchAccessibleShareConfigs should fetch and set share configs successfully', async () => {
    const handleFetchAccessibleShareConfigs = ACTION_HANDLERS[ACTION_TYPES.FETCH_ACCESSIBLE_SHARE_CONFIGS];

    const getState = mockGetState.mockReturnValue({
      clickedContentItem: MOCK_CLICKED_CONTENT_ITEM,
      selectedDealerId: MOCK_SELECTED_DEALER_ID,
    });

    readers.default.id = jest.fn(() => mockContentId);

    jest.spyOn(services, 'fetchContentPermissionConfig').mockResolvedValue({ data: MOCK_ACCESSIBLE_SHARE_CONFIGS });
    jest.spyOn(utils, 'default').mockResolvedValue(MOCK_ACCESSIBLE_SHARE_CONFIGS);
    jest
      .spyOn(shareModalGeneral, 'getContentPermissionForDealer')
      .mockReturnValue(MOCK_ACCESSIBLE_SHARE_CONFIGS.permissions);
    const filteredData = getContentPermissionForDealer(MOCK_ACCESSIBLE_SHARE_CONFIGS, MOCK_SELECTED_DEALER_ID);

    await handleFetchAccessibleShareConfigs({ getState, setState: mockSetState });

    expect(mockSetState).toHaveBeenCalledWith({ isFetchingConfigs: true });
    expect(services.fetchContentPermissionConfig).toHaveBeenCalledWith(mockContentId, MODIFY_ACCESS_TYPES_ID.join(','));
    expect(utils.default).toHaveBeenCalledWith({ data: MOCK_ACCESSIBLE_SHARE_CONFIGS });
    expect(getContentPermissionForDealer).toHaveBeenCalledWith(
      MOCK_ACCESSIBLE_SHARE_CONFIGS?.permissions,
      MOCK_SELECTED_DEALER_ID
    );
    expect(mockSetState).toHaveBeenCalledWith({
      fetchedShareConfigs: filteredData,
      shareConfigs: filteredData,
    });
    expect(mockSetState).toHaveBeenLastCalledWith({ isFetchingConfigs: false });
  });

  it('handleFetchAccessibleShareConfigs should show error toaster if fetchContentPermissionConfig fails', async () => {
    const handleFetchAccessibleShareConfigs = ACTION_HANDLERS[ACTION_TYPES.FETCH_ACCESSIBLE_SHARE_CONFIGS];

    const getState = mockGetState.mockReturnValue({
      clickedContentItem: MOCK_CLICKED_CONTENT_ITEM,
      selectedDealerId: MOCK_SELECTED_DEALER_ID,
    });

    readers.default.id = jest.fn(() => mockContentId);

    const mockError = new Error('Error in Fetching Share Configs');

    jest.spyOn(services, 'fetchContentPermissionConfig').mockRejectedValue(mockError);
    jest.spyOn(utils, 'default');
    jest.spyOn(shareModalGeneral, 'getContentPermissionForDealer');
    jest.spyOn(notification, 'toaster');

    await handleFetchAccessibleShareConfigs({ getState, setState: mockSetState });

    expect(mockSetState).toHaveBeenCalledWith({ isFetchingConfigs: true });

    expect(notification.toaster).toHaveBeenCalledWith('error', 'Error in Fetching Share Configs');

    expect(mockSetState).toHaveBeenLastCalledWith({ isFetchingConfigs: false });

    expect(utils.default).not.toHaveBeenCalled();
    expect(shareModalGeneral.getContentPermissionForDealer).not.toHaveBeenCalled();
  });

  it('handleFetchRestrictedShareConfigs should fetch and set restricted share configs successfully', async () => {
    const handleFetchRestrictedShareConfigs = ACTION_HANDLERS[ACTION_TYPES.FETCH_RESTRICTED_SHARE_CONFIGS];

    const getState = mockGetState.mockReturnValue({
      clickedContentItem: MOCK_CLICKED_CONTENT_ITEM,
      selectedDealerId: MOCK_SELECTED_DEALER_ID,
    });

    readers.default.id = jest.fn(() => mockContentId);

    jest.spyOn(services, 'fetchContentPermissionConfig').mockResolvedValue({ data: MOCK_RESTRICTED_SHARE_CONFIGS });
    jest.spyOn(utils, 'default').mockResolvedValue(MOCK_RESTRICTED_SHARE_CONFIGS);
    jest
      .spyOn(shareModalGeneral, 'getContentPermissionForDealer')
      .mockReturnValue(MOCK_RESTRICTED_SHARE_CONFIGS.permissions);

    const filteredData = getContentPermissionForDealer(MOCK_ACCESSIBLE_SHARE_CONFIGS, MOCK_SELECTED_DEALER_ID);

    await handleFetchRestrictedShareConfigs({ getState, setState: mockSetState });

    expect(services.fetchContentPermissionConfig).toHaveBeenCalledWith(mockContentId, NO_ACCESS_TYPE_ID.join(','));

    expect(utils.default).toHaveBeenCalledWith({ data: MOCK_RESTRICTED_SHARE_CONFIGS });
    expect(shareModalGeneral.getContentPermissionForDealer).toHaveBeenCalledWith(
      MOCK_RESTRICTED_SHARE_CONFIGS.permissions,
      MOCK_SELECTED_DEALER_ID
    );

    expect(mockSetState).toHaveBeenCalledWith({
      fetchedShareConfigs: filteredData,
    });
  });

  it('handleFetchRestrictedShareConfigs should show error toaster if fetchContentPermissionConfig fails', async () => {
    const handleFetchRestrictedShareConfigs = ACTION_HANDLERS[ACTION_TYPES.FETCH_RESTRICTED_SHARE_CONFIGS];

    const getState = mockGetState.mockReturnValue({
      clickedContentItem: MOCK_CLICKED_CONTENT_ITEM,
      selectedDealerId: MOCK_SELECTED_DEALER_ID,
    });

    readers.default.id = jest.fn(() => mockContentId);
    const mockError = new Error('Error in Fetching No Access Share Configs');

    jest.spyOn(services, 'fetchContentPermissionConfig').mockRejectedValue(mockError);

    await handleFetchRestrictedShareConfigs({ getState, setState: mockSetState });

    expect(notification.toaster).toHaveBeenCalledWith('error', 'Error in Fetching No Access Share Configs');
  });

  it('handleUpdateShareConfigs should update shareConfigs and shareConfigAccessList in state', () => {
    const handleUpdateShareConfigs = ACTION_HANDLERS[ACTION_TYPES.UPDATE_SHARE_CONFIGS];

    const mockParams = {
      shareConfigs: MOCK_SHARE_CONFIG,
      shareConfigAccessList: MOCK_SHARE_CONFIG_ACCESS_LIST,
    };

    handleUpdateShareConfigs({ setState: mockSetState, params: mockParams });

    expect(mockSetState).toHaveBeenCalledWith({
      shareConfigs: mockParams.shareConfigs,
      shareConfigAccessList: mockParams.shareConfigAccessList,
    });
  });

  it('should reset the share modal state to default values', () => {
    const handleResetState = ACTION_HANDLERS[ACTION_TYPES.RESET_STATE];

    handleResetState({ setState: mockSetState });

    expect(mockSetState).toHaveBeenCalledWith({
      formLevel: SHARE_MODAL_FORM_LEVEL.CONTENT_SHARE_FORM,
      isShareInProgress: false,
      isFetchingConfigs: false,
      fetchedShareConfigs: EMPTY_ARRAY,
      shareConfigs: EMPTY_ARRAY,
      shareConfigAccessList: {
        [NO_ACCESS.id]: EMPTY_ARRAY,
        [VIEW.id]: EMPTY_ARRAY,
        [EDIT.id]: EMPTY_ARRAY,
      },
    });
  });
});
