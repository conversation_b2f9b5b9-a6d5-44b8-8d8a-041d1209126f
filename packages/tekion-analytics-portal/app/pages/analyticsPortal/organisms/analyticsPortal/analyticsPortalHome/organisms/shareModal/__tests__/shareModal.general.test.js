import { getUserDealerInfo } from 'tbase/helpers/user.helper';

// Helpers
import { getDealerId, getContentPermissionForDealer, isAccessListModified } from '../helpers/shareModal.general';

jest.mock('tbase/helpers/user.helper', () => ({
  getUserDealerInfo: jest.fn(),
}));

afterEach(() => {
  jest.clearAllMocks();
});

describe('getDealerId', () => {
  it('should return dealerId from getUserDealerInfo', () => {
    // Arrange
    const mockDealerId = '123';
    getUserDealerInfo.mockReturnValue({ dealerId: mockDealerId });

    // Act
    const result = getDealerId();

    // Assert
    expect(getUserDealerInfo).toHaveBeenCalledTimes(1);
    expect(result).toBe(mockDealerId);
  });

  it('should return undefined when getUserDealerInfo returns object without dealerId', () => {
    // Arrange
    getUserDealerInfo.mockReturnValue({});

    // Act
    const result = getDealerId();

    // Assert
    expect(getUserDealerInfo).toHaveBeenCalledTimes(1);
    expect(result).toBeUndefined();
  });

  it('should handle when getUserDealerInfo returns null', () => {
    // Arrange
    getUserDealerInfo.mockReturnValue(null);

    // Act & Assert
    expect(() => getDealerId()).toThrow();
  });

  it('should return numeric dealerId correctly', () => {
    // Arrange
    const mockDealerId = 456;
    getUserDealerInfo.mockReturnValue({ dealerId: mockDealerId });

    // Act
    const result = getDealerId();

    // Assert
    expect(result).toBe(456);
  });
});

describe('getContentPermissionForDealer', () => {
  it('should return roles with access types for matching dealer', () => {
    // Arrange
    const contentPermissionData = [
      {
        dealerId: '123',
        accessTypeToRoleMap: {
          READ_ONLY: [{ roleId: 'role1', roleName: 'Viewer', department: 'SALES' }],
          MODIFY: [{ roleId: 'role2', roleName: 'Editor', department: 'ACCOUNTING' }],
        },
      },
    ];
    const selectedDealerId = '123';

    // Act
    const result = getContentPermissionForDealer(contentPermissionData, selectedDealerId);

    // Assert
    expect(result).toEqual([
      { roleId: 'role1', roleName: 'Viewer', department: 'SALES', accessType: 'READ_ONLY' },
      { roleId: 'role2', roleName: 'Editor', department: 'ACCOUNTING', accessType: 'MODIFY' },
    ]);
  });

  it('should return empty array when dealer not found', () => {
    // Arrange
    const contentPermissionData = [
      {
        dealerId: '123',
        accessTypeToRoleMap: {
          READ_ONLY: [{ roleId: 'role1', roleName: 'Viewer' }],
        },
      },
    ];
    const selectedDealerId = '456';

    // Act
    const result = getContentPermissionForDealer(contentPermissionData, selectedDealerId);

    // Assert
    expect(result).toEqual([]);
  });

  it('should handle empty content permission data', () => {
    // Arrange
    const contentPermissionData = [];
    const selectedDealerId = '123';

    // Act
    const result = getContentPermissionForDealer(contentPermissionData, selectedDealerId);

    // Assert
    expect(result).toEqual([]);
  });

  it('should handle undefined content permission data', () => {
    // Arrange
    const contentPermissionData = undefined;
    const selectedDealerId = '123';

    // Act
    const result = getContentPermissionForDealer(contentPermissionData, selectedDealerId);

    // Assert
    expect(result).toEqual([]);
  });
});

describe('isAccessListModified', () => {
  it('should return true when access list has non-empty values', () => {
    // Arrange
    const shareConfigAccessList = {
      READ_ONLY: ['role1'],
      MODIFY: [],
      NO_ACCESS: ['role2', 'role3'],
    };

    // Act
    const result = isAccessListModified({ shareConfigAccessList });

    // Assert
    expect(result).toBe(true);
  });

  it('should return false when all access list values are empty', () => {
    // Arrange
    const shareConfigAccessList = {
      READ_ONLY: [],
      MODIFY: [],
      NO_ACCESS: [],
    };

    // Act
    const result = isAccessListModified({ shareConfigAccessList });

    // Assert
    expect(result).toBe(false);
  });

  it('should return false when access list is empty object', () => {
    // Arrange
    const shareConfigAccessList = {};

    // Act
    const result = isAccessListModified({ shareConfigAccessList });

    // Assert
    expect(result).toBe(false);
  });
});
