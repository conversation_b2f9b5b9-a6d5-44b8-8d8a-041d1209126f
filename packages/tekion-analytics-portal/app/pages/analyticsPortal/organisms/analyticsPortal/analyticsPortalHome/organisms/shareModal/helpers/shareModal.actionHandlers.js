/* eslint-disable import/order */
import {
  fetchContentPermissionConfig,
  saveContentPermissionConfig,
} from 'pages/analyticsPortal/services/analyticsPortal.home';

import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';

import shareModalMetadataReader from 'pages/analyticsPortal/organisms/analyticsPortal/analyticsPortalHome/readers/BaseContentsList';

// helpers
import { getErrorMessage } from 'tbase/utils/errorUtils';
import getDataFromResponse from 'tbase/utils/getDataFromResponse';
import { getContentPermissionForDealer } from './shareModal.general';

// constants
import ACTION_TYPES from '../constants/shareModal.actionTypes';
import { SHARE_MODAL_FORM_LEVEL } from '../constants/shareModal.FormTypes';
import { MODIFY_ACCESS_TYPES_ID, NO_ACCESS_TYPE_ID } from '../constants/shareModal.general';
import { EDIT, NO_ACCESS, VIEW } from '../molecules/shareContenInputTable/constants/accessSettings.accessTypes';
import PARENT_ACTION_TYPES from '../../baseContentsList/constants/baseContentsList.actionTypes';
import { TABLE_ACTION_TYPES } from 'tcomponents/organisms/TableManager';
import { EMPTY_ARRAY } from 'tbase/app.constants';

const handleSubmitShareModal = async ({ getState, setState }) => {
  const { shareConfigAccessList, clickedContentItem, selectedDealerId, onParentAction } = getState();
  const id = shareModalMetadataReader.id(clickedContentItem);
  const originType = shareModalMetadataReader.originType(clickedContentItem);
  try {
    setState({ isShareInProgress: true });
    const payload = {
      rolesAccess: [{ dealerId: selectedDealerId, accessTypeToRoleMap: shareConfigAccessList }],
      originType,
    };
    const updatedContentPermissionResponse = await saveContentPermissionConfig(id, payload);
    await getDataFromResponse(updatedContentPermissionResponse);

    toaster(TOASTER_TYPE.SUCCESS, __('Share Permission Config Saved Successfully'));
    onParentAction({ type: TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH });
    onParentAction({
      type: PARENT_ACTION_TYPES.CANCEL_SHARE_MODAL,
    });
    handleResetState({ setState });
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Error in Saving Share Permission Config')));
  } finally {
    setState({
      isShareInProgress: false,
    });
  }
};

const handleAddRoles = ({ setState }) => {
  setState({
    accessList: SHARE_MODAL_FORM_LEVEL.ADD_ROLES_FORM,
  });
};

const handleFetchAccessibleShareConfigs = async ({ getState, setState }) => {
  const { clickedContentItem, selectedDealerId } = getState();
  const id = shareModalMetadataReader.id(clickedContentItem);

  try {
    setState({ isFetchingConfigs: true });
    const accessTypes = MODIFY_ACCESS_TYPES_ID.join(',');
    const contentPermissionResponse = await fetchContentPermissionConfig(id, accessTypes);
    const contentPermissionData = await getDataFromResponse(contentPermissionResponse);

    const contentPermissionForDealer = getContentPermissionForDealer(
      contentPermissionData?.permissions,
      selectedDealerId
    );

    setState({
      fetchedShareConfigs: contentPermissionForDealer,
      shareConfigs: contentPermissionForDealer,
    });
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Error in Fetching Share Configs')));
  } finally {
    setState({
      isFetchingConfigs: false,
    });
  }
};

const handleFetchRestrictedShareConfigs = async ({ getState, setState }) => {
  const { clickedContentItem, selectedDealerId } = getState();
  const id = shareModalMetadataReader.id(clickedContentItem);

  try {
    const accessTypes = NO_ACCESS_TYPE_ID.join(',');
    const contentPermissionResponse = await fetchContentPermissionConfig(id, accessTypes);
    const contentPermissionData = await getDataFromResponse(contentPermissionResponse);
    const contentPermissionForDealer = getContentPermissionForDealer(
      contentPermissionData?.permissions,
      selectedDealerId
    );

    setState({
      fetchedShareConfigs: contentPermissionForDealer,
      // todo: need to remove this
      // shareConfigs: contentPermissionForDealer,
    });
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Error in Fetching No Access Share Configs')));
  }
};

const handleUpdateShareConfigs = ({ setState, params }) => {
  const { shareConfigs, shareConfigAccessList } = params;
  setState({
    shareConfigs,
    shareConfigAccessList,
  });
};

const handleResetState = ({ setState }) => {
  setState({
    formLevel: SHARE_MODAL_FORM_LEVEL.CONTENT_SHARE_FORM,
    isShareInProgress: false,
    isFetchingConfigs: false,
    fetchedShareConfigs: EMPTY_ARRAY,
    shareConfigs: EMPTY_ARRAY,
    shareConfigAccessList: {
      [NO_ACCESS.id]: EMPTY_ARRAY,
      [VIEW.id]: EMPTY_ARRAY,
      [EDIT.id]: EMPTY_ARRAY,
    },
  });
};

const ACTION_HANDLERS = {
  [ACTION_TYPES.SUBMIT_SHARE_MODAL]: handleSubmitShareModal,
  [ACTION_TYPES.ADD_ROLES]: handleAddRoles,
  [ACTION_TYPES.FETCH_ACCESSIBLE_SHARE_CONFIGS]: handleFetchAccessibleShareConfigs,
  [ACTION_TYPES.FETCH_RESTRICTED_SHARE_CONFIGS]: handleFetchRestrictedShareConfigs,
  [ACTION_TYPES.UPDATE_SHARE_CONFIGS]: handleUpdateShareConfigs,
  [ACTION_TYPES.RESET_STATE]: handleResetState,
};

export default ACTION_HANDLERS;
