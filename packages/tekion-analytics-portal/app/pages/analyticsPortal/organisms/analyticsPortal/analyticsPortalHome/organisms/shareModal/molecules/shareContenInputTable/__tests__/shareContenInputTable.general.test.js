import {
  MOCK_ROW_DATA,
  MOCK_SHARE_CONFIG,
  MOCK_SHARE_CONFIG_ACCESS_LIST,
} from '../__mocks__/shareContenInputTable.general.mock';
import ROW_ACTIONS from '../constants/shareContenInputTable.rowActions';
import {
  getIndexToUpdate,
  getRowAction,
  getRowId,
  getTableData,
  getUpdatedShareConfigAccessList,
  isTableDataBeingCleared,
  updateConfigAccessAtIndex,
} from '../helpers/shareContenInputTable.general';

describe('getRowId', () => {
  it('should return the roleId from the rowData', () => {
    const result = getRowId(MOCK_ROW_DATA);
    expect(result).toBe('4_Controller');
  });
});

describe('getRowAction', () => {
  it('should return the ROW_ACTIONS constant', () => {
    const result = getRowAction();
    expect(result).toBe(ROW_ACTIONS);
  });

  it('should always return the same value on multiple calls', () => {
    const first = getRowAction();
    const second = getRowAction();
    expect(first).toBe(second);
  });
});

describe('getTableData', () => {
  it('should return all shareConfigs when searchText is empty', () => {
    const result = getTableData({ shareConfigs: MOCK_SHARE_CONFIG, searchText: '' });
    expect(result).toEqual(MOCK_SHARE_CONFIG);
  });

  it('should return filtered roles matching lowercase query', () => {
    const result = getTableData({ shareConfigs: MOCK_SHARE_CONFIG, searchText: 'Controller' });
    expect(result).toEqual([
      { roleId: '4_Controller', roleName: 'Controller', department: 'ACCOUNTING', accessType: 'MODIFY' },
    ]);
  });

  it('should trim and lowercase the search text before filtering', () => {
    const result = getTableData({ shareConfigs: MOCK_SHARE_CONFIG, searchText: '   Contr   ' });
    expect(result).toEqual([
      { roleId: '4_Controller', roleName: 'Controller', department: 'ACCOUNTING', accessType: 'MODIFY' },
    ]);
  });

  it('should return empty array if no match found', () => {
    const result = getTableData({ shareConfigs: MOCK_SHARE_CONFIG, searchText: 'random' });
    expect(result).toEqual([]);
  });
});

describe('getIndexToUpdate', () => {
  it('should return the first element of nestingPath', () => {
    const result = getIndexToUpdate({ nestingPath: [1, 2, 3] });
    expect(result).toBe(1);
  });

  it('should return undefined if nestingPath is an empty array', () => {
    const result = getIndexToUpdate({ nestingPath: [] });
    expect(result).toBeUndefined();
  });

  it('should return undefined if params is undefined', () => {
    const result = getIndexToUpdate(undefined);
    expect(result).toBeUndefined();
  });

  it('should return undefined if nestingPath is not provided in params', () => {
    const result = getIndexToUpdate({});
    expect(result).toBeUndefined();
  });
});

describe('isTableDataBeingCleared', () => {
  it('should return true when indexToRemove is 0 and shareFormState has only one item', () => {
    const result = isTableDataBeingCleared(0, [{ roleId: '4_Controller' }]);
    expect(result).toBe(true);
  });

  it('should return false when indexToRemove is not 0', () => {
    const result = isTableDataBeingCleared(1, [{ roleId: '4_Controller' }]);
    expect(result).toBe(false);
  });

  it('should return false when shareFormState has more than one item', () => {
    const result = isTableDataBeingCleared(0, [{ roleId: '4_Controller' }, { roleId: 'viewer' }]);
    expect(result).toBe(false);
  });

  it('should return false when shareFormState is empty', () => {
    const result = isTableDataBeingCleared(0, []);
    expect(result).toBe(false);
  });

  it('should return false when indexToRemove is 0 but shareFormState is undefined', () => {
    const result = isTableDataBeingCleared(0, undefined);
    expect(result).toBe(false);
  });
});

describe('getUpdatedShareConfigAccessList', () => {
  it('should move the roleId from previousAccessType to updatedAccessType', () => {
    const result = getUpdatedShareConfigAccessList({
      shareConfigs: MOCK_SHARE_CONFIG,
      indexToUpdate: 0,
      shareConfigAccessList: MOCK_SHARE_CONFIG_ACCESS_LIST,
      updatedAccessType: 'READ_ONLY',
      previousConfigAccessValue: 'MODIFY',
    });

    expect(result).toEqual({
      READ_ONLY: ['4_Controller'],
      MODIFY: [],
      NO_ACCESS: ['4_APClerk', '4_APPT_KIOSK_USER', '4_ARClerk'],
    });
  });
});

describe('updateConfigAccessAtIndex', () => {
  it('should update accessType at the given index', () => {
    const result = updateConfigAccessAtIndex({
      shareConfigs: MOCK_SHARE_CONFIG,
      indexToUpdate: 0,
      updatedAccessType: 'NO_ACCESS',
    });

    expect(result).toEqual([
      {
        roleId: '4_Controller',
        roleName: 'Controller',
        department: 'ACCOUNTING',
        accessType: 'NO_ACCESS',
      },
    ]);
  });
});
