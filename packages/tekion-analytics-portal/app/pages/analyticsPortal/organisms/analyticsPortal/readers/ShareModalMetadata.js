import _property from 'lodash/property';

export const SHARE_MODA_METADATA_KEYS = {
  id: 'id',
  authorDisplayName: 'authorDisplayName',
  dashboardName: 'displayName',
  description: 'description',
  views: 'views',
  lastAccessed: 'lastAccessed',
  tags: 'tags',
  type: 'type',
  dependentObjects: 'dependentObjects',
  hasAccess: 'hasAccess',
  accessType: 'accessType',
  tekionObjectType: 'tekionObjectType',
  sourceType: 'originType',
  department: 'department',
  roleName: 'roleName',
};

const id = _property(SHARE_MODA_METADATA_KEYS.id);
const authorDisplayName = _property(SHARE_MODA_METADATA_KEYS.authorDisplayName);
const dashboardName = _property(SHARE_MODA_METADATA_KEYS.dashboardName);
const description = _property(SHARE_MODA_METADATA_KEYS.description);
const views = _property(SHARE_MODA_METADATA_KEYS.views);
const lastAccessed = _property(SHARE_MODA_METADATA_KEYS.lastAccessed);
const tags = _property(SHARE_MODA_METADATA_KEYS.tags);
const type = _property(SHARE_MODA_METADATA_KEYS.type);
const dependentObjects = _property(SHARE_MODA_METADATA_KEYS.dependentObjects);
const hasAccess = _property(SHARE_MODA_METADATA_KEYS.hasAccess);
const accessType = _property(SHARE_MODA_METADATA_KEYS.accessType);
const tekionObjectType = _property(SHARE_MODA_METADATA_KEYS.tekionObjectType);
const sourceType = _property(SHARE_MODA_METADATA_KEYS.sourceType);
const department = _property(SHARE_MODA_METADATA_KEYS.department);
const roleName = _property(SHARE_MODA_METADATA_KEYS.roleName);

const READER = {
  id,
  authorDisplayName,
  dashboardName,
  description,
  views,
  lastAccessed,
  tags,
  type,
  dependentObjects,
  hasAccess,
  accessType,
  tekionObjectType,
  sourceType,
  department,
  roleName,
};

export default READER;
