import produce from 'immer';

// Lodash
import _set from 'lodash/set';
import _groupBy from 'lodash/groupBy';
import _orderBy from 'lodash/orderBy';
import _reduce from 'lodash/reduce';
import _curry from 'lodash/curry';
import _isNil from 'lodash/isNil';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _reject from 'lodash/reject';

// Helpers
import { isRefIdPresent } from '@tekion/tekion-base/helpers/accounting/postingRefTypesAndRefIds';
import Process from '@tekion/tekion-base/utils/Process';

// Readers
import lookupReader from '@tekion/tekion-base/readers/lookup';
import dealReader from '@tekion/tekion-base/readers/Deal';
import noteReader from '@tekion/tekion-base/readers/accounting/Note';

// Services
import { fetchBulkNotesByDealer } from '@tekion/tekion-base/services/notesServices';
import { resolveNotes } from 'services/notes';

// Constants
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import NOTES_ASSET_TYPES from '@tekion/tekion-base/constants/notesAssetTypes';
import { CONTROL_TYPES } from '@tekion/tekion-base/constants/accounting/controlTypes';
import SCHEDULE_COLUMN from '@tekion/tekion-base/helpers/accounting/schedules/scheduleColumns';
import DealNotesService from 'services/dealNotes.service';

const CONTROL_TYPE_TO_NOTE_ASSET_TYPE = {
  [CONTROL_TYPES.CUSTOMER]: NOTES_ASSET_TYPES.CUSTOMER,
  [CONTROL_TYPES.VEHICLE]: NOTES_ASSET_TYPES.CONTRACT,
  [CONTROL_TYPES.VIN_LAST_8]: NOTES_ASSET_TYPES.CONTRACT,
};

const getAssetIdPrefix = scheduleId => {
  if (scheduleId) {
    return `${scheduleId}_`;
  }
  return '';
};

export const getControlBookGroupAssetId = _curry((scheduleId, controlBookGroup) => {
  const { refText } = controlBookGroup || EMPTY_OBJECT;
  const assetIdPrefix = getAssetIdPrefix(scheduleId);

  if (isRefIdPresent(refText)) {
    return `${assetIdPrefix}${refText}`;
  }
  return assetIdPrefix;
});

export const getControlBookGroupAssetIds = (controlBookGroups, scheduleId) =>
  _map(controlBookGroups, getControlBookGroupAssetId(scheduleId));

const getAssetTypeAndId = (assetType, assetId) => ({
  assetType,
  assetId,
});

const getAssetTypeAndIdForContractEntity = (next, params = EMPTY_OBJECT) => {
  const { assetType } = params;
  if (assetType === NOTES_ASSET_TYPES.CONTRACT) {
    const { controlBookGroup = EMPTY_OBJECT } = params;
    const { refId } = controlBookGroup;
    const dealLookup = controlBookGroup?._lookup?.DEALS_BY_VEHICLEID?.[refId] || EMPTY_OBJECT;
    const assetId = lookupReader.id(dealLookup);
    if (!_isNil(assetId)) {
      return getAssetTypeAndId(assetType, assetId);
    }
    return next(params);
  }
  return next(params);
};

const getAssetTypeAndIdForCustomerEntity = (next, params = EMPTY_OBJECT) => {
  const { assetType } = params;
  if (assetType === NOTES_ASSET_TYPES.CUSTOMER) {
    const { controlBookGroup = EMPTY_OBJECT } = params;
    const { refId } = controlBookGroup;
    if (!_isNil(refId)) {
      return getAssetTypeAndId(assetType, refId);
    }
    return next(params);
  }
  return next(params);
};

const getControlBookGroupAssetTypeAndIdProcess = new Process()
  .addHandler(getAssetTypeAndIdForContractEntity)
  .addHandler(getAssetTypeAndIdForCustomerEntity);

const getControlBookGroupAssetTypeAndIdForEntity = (controlBookGroup = EMPTY_OBJECT) => {
  const { refType } = controlBookGroup;
  const assetType = CONTROL_TYPE_TO_NOTE_ASSET_TYPE[refType];

  return getControlBookGroupAssetTypeAndIdProcess.run({
    controlBookGroup,
    assetType,
  });
};

const getControlBookGroupAssetIdForScheduleAssetType = (controlBookGroup = EMPTY_OBJECT, scheduleAssetType) => {
  const { refId, refType } = controlBookGroup;
  const assetType = CONTROL_TYPE_TO_NOTE_ASSET_TYPE[refType];
  if (scheduleAssetType === assetType && isRefIdPresent(refId)) {
    const { assetId } = getControlBookGroupAssetTypeAndIdForEntity(controlBookGroup) || EMPTY_OBJECT;
    return assetId;
  }
  return undefined;
};

const addControlBookGroupAssetIdForScheduleAssetType = _curry(
  (scheduleAssetType, controlBookGroupAssetIdsForScheduleAssetType, controlBookGroup) => {
    const controlBookGroupAssetIdForScheduleAssetType = getControlBookGroupAssetIdForScheduleAssetType(
      controlBookGroup,
      scheduleAssetType
    );
    if (controlBookGroupAssetIdForScheduleAssetType) {
      controlBookGroupAssetIdsForScheduleAssetType.push(controlBookGroupAssetIdForScheduleAssetType);
    }
    return controlBookGroupAssetIdsForScheduleAssetType;
  }
);

export const getControlBookGroupAssetIdsForScheduleAssetType = (controlBookGroups, scheduleAssetType) =>
  _reduce(controlBookGroups, addControlBookGroupAssetIdForScheduleAssetType(scheduleAssetType), []);

export const updateDealDataInControlBookGroup = produce((controlBookGroup, deal) => {
  _set(controlBookGroup, 'additional.deal', deal);
});

const getNotesModalTitle = (controlBookGroup = EMPTY_OBJECT) => {
  const { refText } = controlBookGroup;
  if (!isRefIdPresent(refText)) {
    return '';
  }
  return refText;
};

export const getNotesOptions = controlBookGroup => {
  const notesTitleLabel = getNotesModalTitle(controlBookGroup);
  const notesOptions = {
    titleLabel: notesTitleLabel,
  };
  return notesOptions;
};

export const hasSameAssetId = (assetId, scheduleId) => controlBookGroup => {
  const controlBookGroupAssetId = getControlBookGroupAssetId(scheduleId, controlBookGroup);
  return assetId === controlBookGroupAssetId;
};

export const hasSameAssetIdForScheduleAssetType = (assetId, scheduleAssetType) => controlBookGroup => {
  const controlBookGroupAssetId = getControlBookGroupAssetIdForScheduleAssetType(controlBookGroup, scheduleAssetType);
  return assetId === controlBookGroupAssetId;
};

export const hasSameAssetIdFromDeal = (assetId, dealByRefId) => controlBookGroup => {
  const { assetId: assetIdFromDeal } =
    getControlBookGroupAssetTypeAndIdFromDeals(dealByRefId, controlBookGroup) || EMPTY_OBJECT;
  return assetId === assetIdFromDeal;
};

const addCustomerIdForDeal = (customerIds, controlBookGroup = EMPTY_OBJECT) => {
  const { refId, refType } = controlBookGroup;
  if (refType === CONTROL_TYPES.CUSTOMER && isRefIdPresent(refId)) {
    customerIds.push(refId);
  }
  return customerIds;
};

export const getCustomerIdsForDeals = controlBookGroups => _reduce(controlBookGroups, addCustomerIdForDeal, []);

const addDealByRefId = (dealByRefId, dealBucket = EMPTY_OBJECT) => {
  const { key: refId } = dealBucket;
  const deal = dealBucket?.hits?.[0];

  // eslint-disable-next-line no-param-reassign
  dealByRefId[refId] = deal;
  return dealByRefId;
};

export const getDealByRefId = linkedWithDealsData => _reduce(linkedWithDealsData, addDealByRefId, {});

const getControlBookGroupAssetTypeAndIdFromDeals = (dealByRefId = EMPTY_OBJECT, controlBookGroup) => {
  const { refId } = controlBookGroup;
  const { [refId]: deal } = dealByRefId;
  const dealId = dealReader.id(deal);

  if (isRefIdPresent(dealId)) {
    return getAssetTypeAndId(NOTES_ASSET_TYPES.CONTRACT, dealId);
  }
  return getControlBookGroupAssetTypeAndIdForEntity(controlBookGroup);
};

export const getControlBookGroupAssetIdsForDeals = dealByRefId => _map(dealByRefId, dealReader.id);

export const getNotesAssetIdAndType = (dealByRefId, controlBookGroup) => {
  if (!_isEmpty(dealByRefId)) {
    return getControlBookGroupAssetTypeAndIdFromDeals(dealByRefId, controlBookGroup);
  }
  return getControlBookGroupAssetTypeAndIdForEntity(controlBookGroup);
};

const ENTITY_NOTE_COLUMN_IDS = new Set([
  SCHEDULE_COLUMN[CONTROL_TYPES.CUSTOMER].CUSTOMER_NOTES.id,
  SCHEDULE_COLUMN[CONTROL_TYPES.VEHICLE].DEAL_NOTES.id,
]);

const isEntityNoteColumn = (column = EMPTY_OBJECT) => {
  const { id } = column;
  return ENTITY_NOTE_COLUMN_IDS.has(id);
};

export const removeEntityNoteColumns = columns => _reject(columns, isEntityNoteColumn);

export const updateNotesData = produce((controlBookGroup, notes) => {
  _set(controlBookGroup, 'additional.notes', notes);
});

export const updateEntityNotesData = produce((controlBookGroup, notes) => {
  _set(controlBookGroup, 'additional.entityNotes', notes);
});

const addNotesInControlBook =
  ({ scheduleId, scheduleAssetType, dealByRefId, controlBookGroupNotesMap, dealNotesMap, entityNotesMap }) =>
  (controlGroup = EMPTY_OBJECT) => {
    let controlGroupWithNote = controlGroup;

    // add notes data for control number column
    const controlGroupNotesAssetId = getControlBookGroupAssetId(scheduleId, controlGroup);
    controlGroupWithNote = updateNotesData(controlGroupWithNote, controlBookGroupNotesMap?.[controlGroupNotesAssetId]);

    // add notes data for entity columns
    if (scheduleAssetType) {
      const entityNotesAssetId = getControlBookGroupAssetIdForScheduleAssetType(controlGroup, scheduleAssetType);
      controlGroupWithNote = updateEntityNotesData(controlGroupWithNote, entityNotesMap?.[entityNotesAssetId]);
    }

    // add notes and deal data for deal columns
    if (dealByRefId) {
      const { refId } = controlGroup;
      const { [refId]: deal } = dealByRefId;
      const dealId = dealReader.id(deal);
      controlGroupWithNote = updateEntityNotesData(controlGroupWithNote, dealNotesMap?.[dealId]);
      controlGroupWithNote = updateDealDataInControlBookGroup(controlGroupWithNote, deal);
    }

    return controlGroupWithNote;
  };

const sortNotesByCreatedTime = (sortedNotesMap, notes, assetId) => {
  // eslint-disable-next-line no-param-reassign
  sortedNotesMap[assetId] = _orderBy(notes, noteReader.createdTime, ['desc']);

  return sortedNotesMap;
};

/**
 * Returns a promise that resolves with assetId vs notes map.
 * Notes in this map will be sorted by `createdTime` in descending order.
 */
const getNotesMapPromise = async (assetType, assetIds, dealerId) => {
  if (_isEmpty(assetIds)) {
    return Promise.resolve(EMPTY_OBJECT);
  }

  let notes;

  try {
    notes = await fetchBulkNotesByDealer(assetType, assetIds, dealerId);
    notes = await resolveNotes(notes);
  } catch (err) {
    return EMPTY_OBJECT;
  }

  // group notes by assetId
  notes = _groupBy(notes, noteReader.assetId);

  // sort notes by time
  notes = _reduce(notes, sortNotesByCreatedTime, {});

  return notes;
};

const getDealNotesPromise = async (assetIds, dealerId) => {
  if (_isEmpty(assetIds)) {
    return Promise.resolve(EMPTY_OBJECT);
  }

  let notes;

  try {
    notes = await DealNotesService.fetchBulkNotes(assetIds, dealerId);
    notes = await resolveNotes(notes);
  } catch (err) {
    return EMPTY_OBJECT;
  }

  // group notes by assetId
  notes = _groupBy(notes, 'parentAssetId');

  // sort notes by time
  notes = _reduce(notes, sortNotesByCreatedTime, {});

  return notes;
};

/**
 * Returns a promise that resolves with controlBookGroups with added notes and deals details.
 * It will add the following properties in each control groups:
 *   - additional.notes           : Notes shown in control column
 *   - additional.entityNotes     : Notes shown in Deal Notes or Customer Notes columns
 *   - additional.deal            : Deal data if available
 */
export const getControlBookGroupsWithNotesData = async ({
  controlBookGroups,
  scheduleId,
  scheduleAssetType,
  dealByRefId,
  dealerId,
}) => {
  const controlBookGroupNotesAssetIds = getControlBookGroupAssetIds(controlBookGroups, scheduleId);
  const dealNotesAssetIds = dealByRefId && getControlBookGroupAssetIdsForDeals(dealByRefId);
  const entityNotesAssetIds =
    scheduleAssetType && getControlBookGroupAssetIdsForScheduleAssetType(controlBookGroups, scheduleAssetType);

  const [controlBookGroupNotesMap, dealNotesMap, entityNotesMap] = await Promise.all([
    getNotesMapPromise(NOTES_ASSET_TYPES.CONTROL_BOOK, controlBookGroupNotesAssetIds, dealerId),
    getDealNotesPromise(dealNotesAssetIds, dealerId),
    getNotesMapPromise(scheduleAssetType, entityNotesAssetIds, dealerId),
  ]);

  const controlBookGroupsWithNotes = _map(
    controlBookGroups,
    addNotesInControlBook({
      scheduleId,
      scheduleAssetType,
      dealByRefId,
      controlBookGroupNotesMap,
      dealNotesMap,
      entityNotesMap,
    })
  );

  return controlBookGroupsWithNotes;
};
