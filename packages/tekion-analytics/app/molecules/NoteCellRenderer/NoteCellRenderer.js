import React from 'react';
import PropTypes from 'prop-types';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';

// Helpers
import { translateUserMentionedToJSX } from 'tcomponents/molecules/Note/Note.helpers';
import { getNoteItemData } from 'tcomponents/molecules/Note/NoteItem/noteItem.helpers';

const NoteCellRenderer = props => {
  const { value: noteItem } = props;

  const { noteText, associatedUsers } = getNoteItemData(noteItem);
  const noteTextWithTaggedUser = translateUserMentionedToJSX(noteText, associatedUsers);

  return <div>{noteTextWithTaggedUser}</div>;
};

NoteCellRenderer.propTypes = {
  value: PropTypes.object,
};

NoteCellRenderer.defaultProps = {
  value: EMPTY_OBJECT,
};

export default NoteCellRenderer;
