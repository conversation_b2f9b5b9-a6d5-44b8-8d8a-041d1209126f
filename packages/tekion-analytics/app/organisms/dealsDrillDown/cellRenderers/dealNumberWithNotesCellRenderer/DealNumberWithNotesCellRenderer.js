import React, { useRef, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { NOTES_ASSET_TYPE } from 'tbase/constants/desking/contracts';
import MODES from 'tbusiness/constants/routeModes';
import { SALES } from 'tbase/constants/appServices';
import { DEALS } from 'tbase/constants/appConfigs/salesAppConfigs';
import SALES_SOURCE_TYPES from 'twidgets/organisms/notificationCenter/utils/routes/platform/notes/sourceTypes/sales';

// Hooks
import useDealerRedirection from 'customHooks/useDealerRedirection';

// Factories
import getRoute from 'tbusiness/factories/route';

// Selectors
import { getLoginData } from 'tcomponents/pages/authentication/reducers/authentication.selectors';

// Helpers
import { getUserDealerInfo } from 'tbase/helpers/user.helper';

// Utils
import { tget } from 'tbase/utils/general';
import openWindowInNewTab from 'tbase/utils/openWindowInNewTab';

// Components
import Content from 'tcomponents/atoms/Content';
import NotesModalWithActions from 'pages/vehicleInventory/vehicleInventoryDashboard/molecules/cellRenderers/NotesModalWithActions';

// Styles
import styles from './dealNumberWithNotesCellRenderer.module.scss';

const DealNumberCellRenderer = props => {
  const {
    value: dealNumber,
    original = EMPTY_OBJECT,
    dealerRedirection = false,
    loginData,
    contractNotesByAssetid,
  } = props;
  const dealId = tget(original, 'id') || tget(original, 'dealId');

  const { dealerId: currentDealerId } = getUserDealerInfo();
  const dealerId = tget(original, 'dealerId', currentDealerId);
  const soldAtSiteId = tget(original, 'soldAtSiteId');
  const enterpriseEnabled = tget(loginData, 'enterpriseEnabled', false);
  const assetNotes = tget(contractNotesByAssetid, dealId, EMPTY_ARRAY);

  const popupTriggerRef = useRef(null);

  const handleExistingDealerRedirection = useCallback(() => {
    if (dealNumber) {
      const route = getRoute(SALES, DEALS.getKey(), { mode: MODES.VIEW, isExternal: true, dealNumber });
      openWindowInNewTab(route);
    }
  }, [dealNumber]);

  const routeParams = useMemo(
    () => ({
      mode: MODES.VIEW,
      isExternal: true,
      dealNumber,
    }),
    [dealNumber]
  );

  const handleNewDealerRedirection = useDealerRedirection({
    dealerId,
    dashboardId: SALES,
    routeParams,
    addMicroServiceAppPath: false,
    dashboardKey: DEALS.getKey(),
    enterpriseEnabled,
    siteId: soldAtSiteId,
  });

  const handleRedirection = dealerRedirection ? handleNewDealerRedirection : handleExistingDealerRedirection;

  return (
    <div className={`${styles.dealNoWrapper} relative`} ref={popupTriggerRef}>
      <NotesModalWithActions
        titleLabel={__(`Deal #${dealNumber}`)}
        assetType={NOTES_ASSET_TYPE.CONTRACTS}
        assetId={dealId}
        dealerId={dealerId}
        sourceId={dealNumber}
        sourceType={SALES_SOURCE_TYPES.DEAL}
        existingNotes={assetNotes}
      />
      <Content onClick={handleRedirection} highlight className={styles.dealNoLink}>
        {dealNumber}
      </Content>
    </div>
  );
};

const mapStateToProps = state => ({
  loginData: getLoginData(state),
  contractNotesByAssetid: tget(state, ['notes', NOTES_ASSET_TYPE.CONTRACTS]),
});

DealNumberCellRenderer.propTypes = {
  value: PropTypes.string,
  loginData: PropTypes.object,
  original: PropTypes.object.isRequired,
  dealerRedirection: PropTypes.bool,
  contractNotesByAssetid: PropTypes.object,
};

DealNumberCellRenderer.defaultProps = {
  value: '',
  loginData: EMPTY_OBJECT,
  contractNotesByAssetid: EMPTY_OBJECT,
  dealerRedirection: false,
};

export default connect(mapStateToProps)(DealNumberCellRenderer);
