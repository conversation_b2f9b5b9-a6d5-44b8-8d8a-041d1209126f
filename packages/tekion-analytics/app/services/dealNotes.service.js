import _reduce from 'lodash/reduce';
import _map from 'lodash/map';
import _set from 'lodash/set';
import _identity from 'lodash/identity';

import dealerHttpClient from 'services/dealerHttpClient';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';

const parseNotes = notesByAssedId =>
  _reduce(
    notesByAssedId,
    (notesWithAssetId, notes, assetId) => {
      const notesWithParentAssetId = _map(notes, (note = EMPTY_OBJECT) => _set(note, 'parentAssetId', assetId));
      notesWithAssetId.push(...notesWithParentAssetId);
      return notesWithAssetId;
    },
    []
  );

export default class DealNotesService {
  static fetchDealNotes = ({ dealNumber, dealerId }) =>
    dealerHttpClient(dealerId).get(`sales/core/u/deal/notes/${dealNumber}`).then(_identity);

  static fetchBulkNotes = (assetIds, dealerId) =>
    dealerHttpClient(dealerId).post(`/sales/core/u/deal/notes/fetchAll`, assetIds).then(parseNotes);
}
