// Builders
import AppConfigBuilder from './builder/appConfig.builder';

// Constants
import { CORE } from '../appServices';

const { GLOBAL_MENU_ITEM_TYPE, GLOBAL_MENU_ITEM_GROUP } = AppConfigBuilder;

export const KEY_LOUNGE_MANAGEMENT = new AppConfigBuilder('key-lounge', CORE)
  .setTitle(__('Key Lounge'))
  .setPath('key-lounge')
  .setType(GLOBAL_MENU_ITEM_TYPE.PAGE)
  .setGroup(GLOBAL_MENU_ITEM_GROUP.APPS)
  .setOverride('core');

const KEY_LOUNGE_CONFIGS = [KEY_LOUNGE_MANAGEMENT];

export default KEY_LOUNGE_CONFIGS;
