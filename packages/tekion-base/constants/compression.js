// Constants
import extVsMime from './ExtensionVsMIME';

// Helpers
import TEnvReader from '../readers/Env';
import { getEnvironmentVariables } from '../helpers/envHelper';

export const CDN_URL = `${TEnvReader.cdnUrl(getEnvironmentVariables())}/libraries/browser-image-compression/2.0.1/browser-image-compression.js`;

export const DEFAULT_COMPRESSION_OPTIONS = {
  maxSizeMB: 1,
  maxWidthOrHeight: 1920,
  useWebWorker: true,
};

export const COMPRESSIBLE_IMAGE_TYPES = [extVsMime.jpg, extVsMime.jpeg, extVsMime.png, extVsMime.webp];
