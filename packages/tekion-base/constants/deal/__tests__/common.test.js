import {
  GROSS_TYPES,
  YES,
  NO,
  YES_OR_NO_OPTIONS,
  DEAL_CONFIRMED_FILTER,
  SEARCH_USER_BY_ALL_PERSONAS,
} from '../common';

// Mock the i18n function
jest.mock('@tekion/tekion-i18n', () => ({
  __: jest.fn(text => text),
}));

describe('Deal Common Constants', () => {
  describe('GROSS_TYPES', () => {
    it('should have correct FRONT and BACK values', () => {
      expect(GROSS_TYPES.FRONT).toBe('FRONT');
      expect(GROSS_TYPES.BACK).toBe('BACK');
    });

    it('should be an object with two properties', () => {
      expect(Object.keys(GROSS_TYPES)).toHaveLength(2);
      expect(GROSS_TYPES).toHaveProperty('FRONT');
      expect(GROSS_TYPES).toHaveProperty('BACK');
    });
  });

  describe('YES and NO constants', () => {
    it('should have correct YES value', () => {
      expect(YES).toBe('Yes');
    });

    it('should have correct NO value', () => {
      expect(NO).toBe('No');
    });
  });

  describe('YES_OR_NO_OPTIONS', () => {
    it('should have correct structure and values', () => {
      expect(YES_OR_NO_OPTIONS).toEqual([
        { value: true, label: 'Yes' },
        { value: false, label: 'No' },
      ]);
    });

    it('should have two options', () => {
      expect(YES_OR_NO_OPTIONS).toHaveLength(2);
    });

    it('should have boolean values and string labels', () => {
      YES_OR_NO_OPTIONS.forEach(option => {
        expect(typeof option.value).toBe('boolean');
        expect(typeof option.label).toBe('string');
      });
    });
  });

  describe('DEAL_CONFIRMED_FILTER', () => {
    it('should have correct structure', () => {
      expect(DEAL_CONFIRMED_FILTER).toEqual({
        id: 'dealConfirmed',
        name: 'Deal Confirmed',
        type: 'SINGLE_SELECT',
        additional: {
          options: YES_OR_NO_OPTIONS,
        },
      });
    });

    it('should use YES_OR_NO_OPTIONS in additional.options', () => {
      expect(DEAL_CONFIRMED_FILTER.additional.options).toEqual(YES_OR_NO_OPTIONS);
    });

    it('should have correct filter properties', () => {
      expect(DEAL_CONFIRMED_FILTER.id).toBe('dealConfirmed');
      expect(DEAL_CONFIRMED_FILTER.name).toBe('Deal Confirmed');
      expect(DEAL_CONFIRMED_FILTER.type).toBe('SINGLE_SELECT');
    });
  });

  describe('SEARCH_USER_BY_ALL_PERSONAS', () => {
    it('should be an array with "all" value', () => {
      expect(SEARCH_USER_BY_ALL_PERSONAS).toEqual(['all']);
    });

    it('should be an array', () => {
      expect(Array.isArray(SEARCH_USER_BY_ALL_PERSONAS)).toBe(true);
    });

    it('should have exactly one element', () => {
      expect(SEARCH_USER_BY_ALL_PERSONAS).toHaveLength(1);
    });

    it('should contain "all" as the only element', () => {
      expect(SEARCH_USER_BY_ALL_PERSONAS[0]).toBe('all');
    });

    it('should be immutable (not accidentally modified)', () => {
      const originalValue = SEARCH_USER_BY_ALL_PERSONAS.slice();
      expect(SEARCH_USER_BY_ALL_PERSONAS).toEqual(originalValue);
    });
  });

  describe('Constants integration', () => {
    it('should export all expected constants', () => {
      expect(GROSS_TYPES).toBeDefined();
      expect(YES).toBeDefined();
      expect(NO).toBeDefined();
      expect(YES_OR_NO_OPTIONS).toBeDefined();
      expect(DEAL_CONFIRMED_FILTER).toBeDefined();
      expect(SEARCH_USER_BY_ALL_PERSONAS).toBeDefined();
    });

    it('should maintain consistency between related constants', () => {
      // YES_OR_NO_OPTIONS should use YES and NO constants
      expect(YES_OR_NO_OPTIONS[0].label).toBe(YES);
      expect(YES_OR_NO_OPTIONS[1].label).toBe(NO);
    });

    it('should have proper types for all constants', () => {
      expect(typeof GROSS_TYPES).toBe('object');
      expect(typeof YES).toBe('string');
      expect(typeof NO).toBe('string');
      expect(Array.isArray(YES_OR_NO_OPTIONS)).toBe(true);
      expect(typeof DEAL_CONFIRMED_FILTER).toBe('object');
      expect(Array.isArray(SEARCH_USER_BY_ALL_PERSONAS)).toBe(true);
    });
  });

  describe('SEARCH_USER_BY_ALL_PERSONAS usage scenarios', () => {
    it('should be suitable for user search API calls', () => {
      // Test that the format is suitable for API calls that expect personas array
      const apiPayload = {
        personas: SEARCH_USER_BY_ALL_PERSONAS,
        searchText: 'test user',
      };

      expect(apiPayload.personas).toEqual(['all']);
      expect(Array.isArray(apiPayload.personas)).toBe(true);
    });

    it('should work with spread operator', () => {
      const extendedPersonas = [...SEARCH_USER_BY_ALL_PERSONAS, 'additional'];
      expect(extendedPersonas).toEqual(['all', 'additional']);
    });

    it('should work with array methods', () => {
      expect(SEARCH_USER_BY_ALL_PERSONAS.includes('all')).toBe(true);
      expect(SEARCH_USER_BY_ALL_PERSONAS.indexOf('all')).toBe(0);
      expect(SEARCH_USER_BY_ALL_PERSONAS.length).toBe(1);
    });
  });
});
