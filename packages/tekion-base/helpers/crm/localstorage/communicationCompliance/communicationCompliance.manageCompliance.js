import { produce } from 'immer';
import _set from 'lodash/set';
import _get from 'lodash/get';

import { COMMUNICATION_COMPLIANCE_BANNER } from '../../../../constants/crm/localstorage';
import {
  getCRMLocalStorageValue,
  makeUpdatedCRMLocalStorageValue,
  removeKeyFromCRMLocalStorage,
  setCRMLocalStorageValue,
} from '../localStorage.general';

import { EMPTY_OBJECT } from '../../../../app.constants';

const DEFAULT_SITE_ID = 'default';

export const getSiteId = tekSiteId => tekSiteId || DEFAULT_SITE_ID;

export const createCommunicationComplianceData = produce((prevCommunicationComplianceData, payload) => {
  const { dealerId, tekSiteId, communicationComplianceData } = payload;
  const siteId = getSiteId(tekSiteId);
  _set(prevCommunicationComplianceData, [dealerId, siteId], communicationComplianceData);
});

export const makeCommunicationComplianceData = isHidden => ({
  isHidden,
});

const getComplianceBannerDataFromLS = () => {
  const crmLSValue = getCRMLocalStorageValue();
  const complianceBannerDataFromLS = _get(crmLSValue, COMMUNICATION_COMPLIANCE_BANNER, EMPTY_OBJECT);
  return complianceBannerDataFromLS;
};

export const getCommunicationComplianceDataByDealerInfoFromLS = (dealerId, tekSiteId) => {
  const complianceBannerData = getComplianceBannerDataFromLS() || EMPTY_OBJECT;
  const siteId = getSiteId(tekSiteId);
  const communicationComplianceData = _get(complianceBannerData, [dealerId, siteId], EMPTY_OBJECT);
  return communicationComplianceData;
};

export const setCommunicationComplianceDataInLS = (dealerId, tekSiteId, isHidden) => {
  const prevCommunicationComplianceData = getComplianceBannerDataFromLS() || EMPTY_OBJECT;
  const communicationComplianceData = makeCommunicationComplianceData(isHidden);
  const updatedCommunicationCompliance = createCommunicationComplianceData(prevCommunicationComplianceData, {
    dealerId,
    tekSiteId,
    communicationComplianceData,
  });

  const updatedCRMLocalStorageValue = makeUpdatedCRMLocalStorageValue(
    COMMUNICATION_COMPLIANCE_BANNER,
    updatedCommunicationCompliance
  );

  setCRMLocalStorageValue(updatedCRMLocalStorageValue);
};

export const removeCommunicationComplianceDataFromLS = () => {
  removeKeyFromCRMLocalStorage(COMMUNICATION_COMPLIANCE_BANNER);
};
