import _omit from 'lodash/omit';

import { CRM } from '../../../constants/crm/localstorage';
import { getValueFromLocalStorage, setValueInLocalStorage } from '../../../utils/localStorage';
import { EMPTY_OBJECT } from '../../../app.constants';

export const getCRMLocalStorageValue = () => {
  const crmLSValue = getValueFromLocalStorage(CRM);
  try {
    return JSON.parse(crmLSValue);
  } catch {
    return undefined;
  }
};

export const setCRMLocalStorageValue = value => {
  try {
    const stringifyValue = JSON.stringify(value);
    setValueInLocalStorage(CRM, stringifyValue);
  } catch {
    // do nothing
  }
};

export const makeUpdatedCRMLocalStorageValue = (key, value) => {
  const crmLSValue = getCRMLocalStorageValue() || EMPTY_OBJECT;
  const updatedCRMLocalStorageValue = {
    ...crmLSValue,
    [key]: value,
  };
  return updatedCRMLocalStorageValue;
};

export const removeKeyFromCRMLocalStorage = key => {
  const crmLSValue = getCRMLocalStorageValue() || EMPTY_OBJECT;
  const updatedCRMLocalStorageValue = _omit(crmLSValue, key);
  return setCRMLocalStorageValue(updatedCRMLocalStorageValue);
};
