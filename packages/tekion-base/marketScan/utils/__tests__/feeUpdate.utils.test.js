import { SOURCES_OF_FEES } from '../../constants/desking.constants';
import {
  removeDeletedFees,
  removeDeletedFeesWhenVehicleProfileEnabled,
  getUpdatedManuallyDeletedFees,
  getVIFeeCodes,
} from '../feeUpdate.utils';

jest.mock('../../readers/deal.reader');
jest.mock('../desking.utils', () => ({
  ...jest.requireActual('../desking.utils'),
  filterOptionCodesBasedOnCategroryTypeAndSource: jest.fn(() => []),
}));
jest.mock('lodash/uniq', () => arr => [...new Set(arr)]);

// Import the mocked function after the mock
import { filterOptionCodesBasedOnCategroryTypeAndSource } from '../desking.utils';

describe('removeDeletedFees', () => {
  describe('when vehicle profile is disabled or vehicle targeting is disabled', () => {
    it('should remove fees with feeCodes that match the provided ids', () => {
      const fees = [
        { feeCode: 'FEE001', amount: 100, source: 'SALES_SETUP' },
        { feeCode: 'FEE002', amount: 200, source: 'VEHICLE' },
        { feeCode: 'FEE003', amount: 300, source: 'SALES_SETUP' },
      ];
      const ids = ['FEE001', 'FEE003'];
      const isVehicleProfileEnabled = false;
      const isVehicleTargeting = false;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual([{ feeCode: 'FEE002', amount: 200, source: 'VEHICLE' }]);
    });

    it('should return all fees when no ids match', () => {
      const fees = [
        { feeCode: 'FEE001', amount: 100, source: 'SALES_SETUP' },
        { feeCode: 'FEE002', amount: 200, source: 'VEHICLE' },
      ];
      const ids = ['FEE999'];
      const isVehicleProfileEnabled = false;
      const isVehicleTargeting = false;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual(fees);
    });

    it('should return empty array when all fees match ids', () => {
      const fees = [
        { feeCode: 'FEE001', amount: 100 },
        { feeCode: 'FEE002', amount: 200 },
      ];
      const ids = ['FEE001', 'FEE002'];
      const isVehicleProfileEnabled = false;
      const isVehicleTargeting = false;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual([]);
    });

    it('should handle empty fees array', () => {
      const fees = [];
      const ids = ['FEE001'];
      const isVehicleProfileEnabled = false;
      const isVehicleTargeting = false;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual([]);
    });

    it('should handle empty ids array', () => {
      const fees = [
        { feeCode: 'FEE001', amount: 100 },
        { feeCode: 'FEE002', amount: 200 },
      ];
      const ids = [];
      const isVehicleProfileEnabled = false;
      const isVehicleTargeting = false;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual(fees);
    });

    it('should use simple logic when vehicle profile enabled but targeting disabled', () => {
      const fees = [
        { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
        { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
      ];
      const ids = ['FEE001'];
      const isVehicleProfileEnabled = true;
      const isVehicleTargeting = false;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual([{ feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP }]);
    });
  });

  describe('when vehicle profile is enabled and vehicle targeting is enabled', () => {
    it('should call removeDeletedFeesWhenVehicleProfileEnabled for complex logic', () => {
      const fees = [
        { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
        { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
        { feeCode: 'FEE003', amount: 300, source: 'PRE_CONFIGURED' },
      ];
      const ids = ['FEE001', 'FEE002'];
      const isVehicleProfileEnabled = true;
      const isVehicleTargeting = true;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual([
        { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
        { feeCode: 'FEE003', amount: 300, source: 'PRE_CONFIGURED' },
      ]);
    });

    it('should preserve PRE_CONFIGURED fees in ids and keep fees not in ids', () => {
      const fees = [
        { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
        { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.VEHICLE },
        { feeCode: 'FEE003', amount: 300, source: SOURCES_OF_FEES.SALES_SETUP },
        { feeCode: 'FEE004', amount: 400, source: 'PRE_CONFIGURED' },
      ];
      const ids = ['FEE001', 'FEE003'];
      const isVehicleProfileEnabled = true;
      const isVehicleTargeting = true;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual([
        { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
        { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.VEHICLE },
        { feeCode: 'FEE004', amount: 400, source: 'PRE_CONFIGURED' },
      ]);
    });

    it('should handle empty ids with vehicle profile and targeting enabled', () => {
      const fees = [
        { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
        { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
      ];
      const ids = [];
      const isVehicleProfileEnabled = true;
      const isVehicleTargeting = true;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual(fees);
    });

    it('should handle all fees in ids with mixed sources', () => {
      const fees = [
        { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
        { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
        { feeCode: 'FEE003', amount: 300, source: SOURCES_OF_FEES.VEHICLE },
      ];
      const ids = ['FEE001', 'FEE002', 'FEE003'];
      const isVehicleProfileEnabled = true;
      const isVehicleTargeting = true;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual([{ feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' }]);
    });
  });

  describe('edge cases and parameter validation', () => {
    it('should handle undefined parameters gracefully', () => {
      const fees = [{ feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' }];
      const ids = ['FEE001'];

      const result = removeDeletedFees(fees, ids, undefined, undefined);

      expect(result).toEqual([]);
    });

    it('should handle null parameters gracefully', () => {
      const fees = [{ feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' }];
      const ids = ['FEE001'];

      const result = removeDeletedFees(fees, ids, null, null);

      expect(result).toEqual([]);
    });

    it('should handle fees without feeCode property', () => {
      const fees = [
        { amount: 100, source: 'PRE_CONFIGURED' }, // no feeCode
        { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
      ];
      const ids = ['FEE002'];
      const isVehicleProfileEnabled = false;
      const isVehicleTargeting = false;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result).toEqual([{ amount: 100, source: 'PRE_CONFIGURED' }]);
    });

    it('should handle large arrays efficiently', () => {
      const fees = Array.from({ length: 1000 }, (_, i) => ({
        feeCode: `FEE${i.toString().padStart(3, '0')}`,
        amount: i * 10,
        source: i % 2 === 0 ? 'PRE_CONFIGURED' : SOURCES_OF_FEES.SALES_SETUP,
      }));
      const ids = ['FEE000', 'FEE002', 'FEE004'];
      const isVehicleProfileEnabled = false;
      const isVehicleTargeting = false;

      const result = removeDeletedFees(fees, ids, isVehicleProfileEnabled, isVehicleTargeting);

      expect(result.length).toBe(997); // 1000 - 3 removed fees
      expect(result.find(fee => fee.feeCode === 'FEE000')).toBeUndefined();
      expect(result.find(fee => fee.feeCode === 'FEE001')).toBeDefined();
    });
  });
});

describe('removeDeletedFeesWhenVehicleProfileEnabled', () => {
  it('should keep PRE_CONFIGURED fees that are included in ids', () => {
    const fees = [
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE003', amount: 300, source: SOURCES_OF_FEES.SALES_SETUP },
    ];
    const ids = ['FEE001', 'FEE003'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
    ]);
  });

  it('should keep PRE_CONFIGURED fees that are not included in ids', () => {
    const fees = [
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE003', amount: 300, source: SOURCES_OF_FEES.VEHICLE },
    ];
    const ids = ['FEE002'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE003', amount: 300, source: SOURCES_OF_FEES.VEHICLE },
    ]);
  });

  it('should keep SALES_SETUP fees that are not in ids', () => {
    const fees = [
      { feeCode: 'FEE001', amount: 100, source: SOURCES_OF_FEES.SALES_SETUP },
      { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
      { feeCode: 'FEE003', amount: 300, source: SOURCES_OF_FEES.VEHICLE },
    ];
    const ids = ['FEE001'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([
      { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
      { feeCode: 'FEE003', amount: 300, source: SOURCES_OF_FEES.VEHICLE },
    ]);
  });

  it('should remove SALES_SETUP fees that are in ids but not PRE_CONFIGURED', () => {
    const fees = [
      { feeCode: 'FEE001', amount: 100, source: SOURCES_OF_FEES.SALES_SETUP },
      { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
    ];
    const ids = ['FEE001', 'FEE002'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([]);
  });

  it('should keep non-PRE_CONFIGURED fees that are not in ids', () => {
    const fees = [
      { feeCode: 'FEE001', amount: 100, source: SOURCES_OF_FEES.VEHICLE },
      { feeCode: 'FEE002', amount: 200, source: 'OTHER_SOURCE' },
      { feeCode: 'FEE003', amount: 300, source: SOURCES_OF_FEES.SALES_SETUP },
    ];
    const ids = ['FEE999'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([
      { feeCode: 'FEE001', amount: 100, source: SOURCES_OF_FEES.VEHICLE },
      { feeCode: 'FEE002', amount: 200, source: 'OTHER_SOURCE' },
      { feeCode: 'FEE003', amount: 300, source: SOURCES_OF_FEES.SALES_SETUP },
    ]);
  });

  it('should handle mixed PRE_CONFIGURED and other fees correctly', () => {
    const fees = [
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
      { feeCode: 'FEE003', amount: 300, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE004', amount: 400, source: SOURCES_OF_FEES.VEHICLE },
    ];
    const ids = ['FEE001', 'FEE002', 'FEE004'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE003', amount: 300, source: 'PRE_CONFIGURED' },
    ]);
  });

  it('should handle multiple PRE_CONFIGURED fees with mixed ids', () => {
    const fees = [
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE003', amount: 300, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE004', amount: 400, source: SOURCES_OF_FEES.SALES_SETUP },
    ];
    const ids = ['FEE001', 'FEE003', 'FEE004'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE003', amount: 300, source: 'PRE_CONFIGURED' },
    ]);
  });

  it('should handle empty fees array', () => {
    const fees = [];
    const ids = ['FEE001'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([]);
  });

  it('should handle empty ids array - keep all fees', () => {
    const fees = [
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
    ];
    const ids = [];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: SOURCES_OF_FEES.SALES_SETUP },
    ]);
  });

  it('should handle fees without source property', () => {
    const fees = [
      { feeCode: 'FEE001', amount: 100 }, // no source property
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
    ];
    const ids = ['FEE001', 'FEE002'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([{ feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' }]);
  });

  it('should handle fees without feeCode property', () => {
    const fees = [
      { amount: 100, source: 'PRE_CONFIGURED' }, // no feeCode property
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
    ];
    const ids = ['FEE002'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([
      { amount: 100, source: 'PRE_CONFIGURED' }, // kept because feeCode is undefined, not in ids
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
    ]);
  });

  it('should keep all PRE_CONFIGURED fees when none are in ids', () => {
    const fees = [
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE003', amount: 300, source: 'PRE_CONFIGURED' },
    ];
    const ids = ['FEE999'];

    const result = removeDeletedFeesWhenVehicleProfileEnabled(fees, ids);

    expect(result).toEqual([
      { feeCode: 'FEE001', amount: 100, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE002', amount: 200, source: 'PRE_CONFIGURED' },
      { feeCode: 'FEE003', amount: 300, source: 'PRE_CONFIGURED' },
    ]);
  });
});

describe('getUpdatedManuallyDeletedFees', () => {
  it('should return original manuallyDeletedFees when vehicle profile is disabled', () => {
    const manuallyDeletedFees = ['FEE001', 'FEE002'];
    const columnFees = [{ feeCode: 'FEE001', amount: 150 }];
    const isVehicleProfileEnabled = false;
    const isVehicleTargeting = true;

    const result = getUpdatedManuallyDeletedFees(
      manuallyDeletedFees,
      isVehicleProfileEnabled,
      isVehicleTargeting,
      columnFees
    );

    expect(result).toEqual(manuallyDeletedFees);
  });

  it('should return original manuallyDeletedFees when vehicle targeting is disabled', () => {
    const manuallyDeletedFees = ['FEE001', 'FEE002'];
    const columnFees = [{ feeCode: 'FEE001', amount: 150 }];
    const isVehicleProfileEnabled = true;
    const isVehicleTargeting = false;

    const result = getUpdatedManuallyDeletedFees(
      manuallyDeletedFees,
      isVehicleProfileEnabled,
      isVehicleTargeting,
      columnFees
    );

    expect(result).toEqual(manuallyDeletedFees);
  });

  it('should filter out fees that exist in columnFees when both flags are true', () => {
    const manuallyDeletedFees = ['FEE001', 'FEE002', 'FEE003'];
    const columnFees = [
      { feeCode: 'FEE001', amount: 150 },
      { feeCode: 'FEE003', amount: 350 },
    ];
    const isVehicleProfileEnabled = true;
    const isVehicleTargeting = true;

    const result = getUpdatedManuallyDeletedFees(
      manuallyDeletedFees,
      isVehicleProfileEnabled,
      isVehicleTargeting,
      columnFees
    );

    expect(result).toEqual(['FEE002']);
  });

  it('should return all manuallyDeletedFees when none exist in columnFees', () => {
    const manuallyDeletedFees = ['FEE001', 'FEE002'];
    const columnFees = [
      { feeCode: 'FEE003', amount: 300 },
      { feeCode: 'FEE004', amount: 400 },
    ];
    const isVehicleProfileEnabled = true;
    const isVehicleTargeting = true;

    const result = getUpdatedManuallyDeletedFees(
      manuallyDeletedFees,
      isVehicleProfileEnabled,
      isVehicleTargeting,
      columnFees
    );

    expect(result).toEqual(manuallyDeletedFees);
  });

  it('should handle empty arrays correctly', () => {
    const manuallyDeletedFees = [];
    const columnFees = [];
    const isVehicleProfileEnabled = true;
    const isVehicleTargeting = true;

    const result = getUpdatedManuallyDeletedFees(
      manuallyDeletedFees,
      isVehicleProfileEnabled,
      isVehicleTargeting,
      columnFees
    );

    expect(result).toEqual([]);
  });

  it('should filter out all fees when all exist in columnFees', () => {
    const manuallyDeletedFees = ['FEE001', 'FEE002', 'FEE003'];
    const columnFees = [
      { feeCode: 'FEE001', amount: 100 },
      { feeCode: 'FEE002', amount: 200 },
      { feeCode: 'FEE003', amount: 300 },
      { feeCode: 'FEE004', amount: 400 },
    ];
    const isVehicleProfileEnabled = true;
    const isVehicleTargeting = true;

    const result = getUpdatedManuallyDeletedFees(
      manuallyDeletedFees,
      isVehicleProfileEnabled,
      isVehicleTargeting,
      columnFees
    );

    expect(result).toEqual([]);
  });

  it('should filter out partial fees when some exist in columnFees', () => {
    const manuallyDeletedFees = ['FEE001', 'FEE002', 'FEE003', 'FEE004', 'FEE005'];
    const columnFees = [
      { feeCode: 'FEE002', amount: 200 },
      { feeCode: 'FEE004', amount: 400 },
      { feeCode: 'FEE006', amount: 600 },
    ];
    const isVehicleProfileEnabled = true;
    const isVehicleTargeting = true;

    const result = getUpdatedManuallyDeletedFees(
      manuallyDeletedFees,
      isVehicleProfileEnabled,
      isVehicleTargeting,
      columnFees
    );

    expect(result).toEqual(['FEE001', 'FEE003', 'FEE005']);
  });
});

describe('getVIFeeCodes', () => {
  beforeEach(() => {
    // Reset the mock before each test
    filterOptionCodesBasedOnCategroryTypeAndSource.mockReset();
  });

  it('should return empty array when vehicle profile is disabled', () => {
    const deal = {
      vehicles: [
        {
          options: [
            { type: 'FEE', source: 'PRE_CONFIGURED', optionCode: 'FEE001' },
            { type: 'FEE', source: 'PRE_CONFIGURED', optionCode: 'FEE002' },
          ],
        },
      ],
    };
    const isVehicleProfileEnabled = false;

    const result = getVIFeeCodes(deal, isVehicleProfileEnabled);

    expect(result).toEqual([]);
  });

  it('should extract fee codes from single vehicle with FEE type and PRE_CONFIGURED source', () => {
    // Mock the function to return the expected fee codes
    filterOptionCodesBasedOnCategroryTypeAndSource.mockReturnValue(['FEE001', 'FEE002']);

    const deal = {
      vehicles: [
        {
          options: [
            { type: 'FEE', source: 'PRE_CONFIGURED', optionCode: 'FEE001' },
            { type: 'FEE', source: 'PRE_CONFIGURED', optionCode: 'FEE002' },
            { type: 'ACCESSORY', source: 'PRE_CONFIGURED', optionCode: 'ACC001' }, // should be filtered out
            { type: 'FEE', source: 'MANUAL', optionCode: 'FEE003' }, // should be filtered out
          ],
        },
      ],
    };
    const isVehicleProfileEnabled = true;

    const result = getVIFeeCodes(deal, isVehicleProfileEnabled);

    expect(result).toEqual(['FEE001', 'FEE002']);
    expect(filterOptionCodesBasedOnCategroryTypeAndSource).toHaveBeenCalledWith(deal.vehicles[0].options, 'FEE');
    expect(filterOptionCodesBasedOnCategroryTypeAndSource).toHaveBeenCalledTimes(1);
  });

  it('should extract fee codes from multiple vehicles and return unique codes', () => {
    // Mock the function to return different codes for each vehicle
    filterOptionCodesBasedOnCategroryTypeAndSource
      .mockReturnValueOnce(['FEE001', 'FEE002'])
      .mockReturnValueOnce(['FEE002', 'FEE003']); // FEE002 is duplicate

    const deal = {
      vehicles: [
        {
          options: [
            { type: 'FEE', source: 'PRE_CONFIGURED', optionCode: 'FEE001' },
            { type: 'FEE', source: 'PRE_CONFIGURED', optionCode: 'FEE002' },
          ],
        },
        {
          options: [
            { type: 'FEE', source: 'PRE_CONFIGURED', optionCode: 'FEE002' }, // duplicate
            { type: 'FEE', source: 'PRE_CONFIGURED', optionCode: 'FEE003' },
          ],
        },
      ],
    };
    const isVehicleProfileEnabled = true;

    const result = getVIFeeCodes(deal, isVehicleProfileEnabled);

    expect(result).toEqual(['FEE001', 'FEE002', 'FEE003']);
    expect(filterOptionCodesBasedOnCategroryTypeAndSource).toHaveBeenCalledTimes(2);
    expect(filterOptionCodesBasedOnCategroryTypeAndSource).toHaveBeenNthCalledWith(1, deal.vehicles[0].options, 'FEE');
    expect(filterOptionCodesBasedOnCategroryTypeAndSource).toHaveBeenNthCalledWith(2, deal.vehicles[1].options, 'FEE');
  });

  it('should handle deal with no vehicles or undefined vehicles', () => {
    filterOptionCodesBasedOnCategroryTypeAndSource.mockReturnValue([]);

    // Test with empty vehicles array
    const dealWithEmptyVehicles = { vehicles: [] };
    const result1 = getVIFeeCodes(dealWithEmptyVehicles, true);
    expect(result1).toEqual([]);

    // Test with undefined vehicles
    const dealWithUndefinedVehicles = {};
    const result2 = getVIFeeCodes(dealWithUndefinedVehicles, true);
    expect(result2).toEqual([]);

    // Test with null deal
    const result3 = getVIFeeCodes(null, true);
    expect(result3).toEqual([]);

    // Should not call the filter function when there are no vehicles
    expect(filterOptionCodesBasedOnCategroryTypeAndSource).not.toHaveBeenCalled();
  });

  it('should handle vehicles with empty or undefined options', () => {
    // Mock the function to return empty arrays for vehicles with no valid options
    filterOptionCodesBasedOnCategroryTypeAndSource
      .mockReturnValueOnce([]) // for { options: [] }
      .mockReturnValueOnce(['FEE001']) // for vehicle with valid options
      .mockReturnValueOnce([]) // for { options: undefined }
      .mockReturnValueOnce([]); // for {} (no options property)

    const deal = {
      vehicles: [
        { options: [] }, // empty options
        {
          options: [{ type: 'FEE', source: 'PRE_CONFIGURED', optionCode: 'FEE001' }],
        },
        { options: undefined }, // undefined options
        {}, // no options property
      ],
    };
    const isVehicleProfileEnabled = true;

    const result = getVIFeeCodes(deal, isVehicleProfileEnabled);

    expect(result).toEqual(['FEE001']);
  });
});
