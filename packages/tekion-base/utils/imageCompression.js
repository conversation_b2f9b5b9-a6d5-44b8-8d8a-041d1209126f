// Lodash
import _map from 'lodash/map';
import _toArray from 'lodash/toArray';
import _get from 'lodash/get';
import _isFunction from 'lodash/isFunction';
import _round from 'lodash/round';

// Constants
import { ONE_MB_IN_BYTES } from '../constants/fileSizeUnits';
import { DEFAULT_COMPRESSION_OPTIONS, COMPRESSIBLE_IMAGE_TYPES, CDN_URL } from '../constants/compression';

// Helpers
import { injectScript } from '../scripts/scriptInjector';
import { createFileWithCompressionInfo } from './file';

let scriptLoadingPromise = null;

export const loadCompressionLibrary = () => {
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return Promise.reject(new Error('Compression only supported in browsers.'));
  }

  if (window.imageCompression) return Promise.resolve();
  if (scriptLoadingPromise) return scriptLoadingPromise;

  scriptLoadingPromise = injectScript({
    scriptSrc: CDN_URL,
    scriptTagId: 'browser-image-compression',
    onLoad: () => {
      if (!window.imageCompression) {
        scriptLoadingPromise = null;
        console.error('[loadCompressionLibrary] imageCompression not available after script load.');
        throw new Error('imageCompression not available.');
      }
    },
  });

  return scriptLoadingPromise;
};

export const shouldSkipCompression = file => {
  if (!(file instanceof File)) return true;

  const isCompressibleType = file.type.startsWith('image/') && COMPRESSIBLE_IMAGE_TYPES.includes(file.type);
  const isLargerThan1MB = file.size > ONE_MB_IN_BYTES;

  return !(isCompressibleType && isLargerThan1MB);
};

export const updateProgressHandler = (onProgress, completed, total) => {
  if (_isFunction(onProgress)) {
    const percent = _round((completed / total) * 100);
    onProgress(percent);
  }
};

export const compressImages = async (
  files,
  {
    maxSizeMB = DEFAULT_COMPRESSION_OPTIONS.maxSizeMB,
    maxWidthOrHeight = DEFAULT_COMPRESSION_OPTIONS.maxWidthOrHeight,
    onProgress,
  } = {}
) => {
  const inputFiles = _toArray(files);
  const total = inputFiles.length || 1;
  let completed = 0;

  try {
    await loadCompressionLibrary();
  } catch {
    return Promise.resolve(inputFiles);
  }

  const imageCompression = _get(window, 'imageCompression');
  if (!imageCompression) return Promise.resolve(inputFiles);

  const compressionOptions = {
    maxSizeMB,
    maxWidthOrHeight,
    useWebWorker: true,
  };

  const compressFile = async file => {
    const skip = shouldSkipCompression(file);
    if (skip) {
      completed += 1;
      updateProgressHandler(onProgress, completed, total);
      return file;
    }

    try {
      const compressedBlob = await imageCompression(file, compressionOptions);
      return createFileWithCompressionInfo(compressedBlob, file);
    } catch {
      return file;
    } finally {
      completed += 1;
      updateProgressHandler(onProgress, completed, total);
    }
  };

  return Promise.all(_map(inputFiles, compressFile));
};

export { loadCompressionLibrary as preloadCompressionLibrary };
