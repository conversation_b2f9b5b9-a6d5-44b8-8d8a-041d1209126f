// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';

class CustomerBuilder {
  constructor() {
    this.customerId = '';
    this.firstName = '';
    this.middleName = '';
    this.lastName = '';
    this.customerNumber = '';
    this.email = '';
    this.primaryPhone = EMPTY_OBJECT;
    this.customerType = '';
    this.isCharge = false;
    this.status = '';
  }

  setCustomerId(customerId) {
    this.customerId = customerId;
    return this;
  }

  setFirstName(firstName) {
    this.firstName = firstName;
    return this;
  }

  setMiddleName(middleName) {
    this.middleName = middleName;
    return this;
  }

  setLastName(lastName) {
    this.lastName = lastName;
    return this;
  }

  setCustomerNumber(customerNumber) {
    this.customerNumber = customerNumber;
    return this;
  }

  setEmail(email) {
    this.email = email;
    return this;
  }

  setPrimaryPhone(primaryPhone) {
    this.primaryPhone = primaryPhone;
    return this;
  }

  setCustomerType(customerType) {
    this.customerType = customerType;
    return this;
  }

  setIsCharge(isCharge) {
    this.isCharge = isCharge;
    return this;
  }

  setStatus(status) {
    this.status = status;
    return this;
  }

  getCustomerId() {
    return this.customerId;
  }

  getFirstName() {
    return this.firstName;
  }

  getMiddleName() {
    return this.middleName;
  }

  getLastName() {
    return this.lastName;
  }

  getCustomerNumber() {
    return this.customerNumber;
  }

  getEmail() {
    return this.email;
  }

  getPrimaryPhone() {
    return this.primaryPhone;
  }

  getCustomerType() {
    return this.customerType;
  }

  getIsCharge() {
    return this.isCharge;
  }

  getStatus() {
    return this.status;
  }
}

export default CustomerBuilder;
