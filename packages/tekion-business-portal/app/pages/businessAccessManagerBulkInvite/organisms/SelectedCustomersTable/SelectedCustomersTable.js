import React, { useMemo, memo } from 'react';
import PropTypes from 'prop-types';
import compose from 'recompose/compose';

// Lodash
import _noop from 'lodash/noop';
import _size from 'lodash/size';

// Containers
import withLocalTableActions from 'tcomponents/connectors/withLocalTableActions';
import withCustomSortTable from 'tcomponents/organisms/withCustomSortTable';
import withActions from 'tcomponents/connectors/withActions';

// Components
import TableManager from 'twidgets/organisms/tableManager';
import { BasicTable } from 'twidgets/appServices/accounting/organisms/table';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import {
  LOCAL_ACTION_TYPES,
  TABLE_MANAGER_PROPS,
  TABLE_PROPS,
  INITIAL_STATE,
} from './constants/selectedCustomersTable.general';

// Helpers
import { makeSubHeaderProps, getColumns } from './helpers/selectedCustomersTable.general';

// Builders
import CustomerBuilder from '../../builders/CustomerBuilder';

// Action Handlers
import ACTION_HANDLERS from './selectedCustomersTable.actionHandlers';

// Styles
import styles from './selectedCustomersTable.module.scss';

const LocalTableManager = compose(withLocalTableActions, withCustomSortTable)(TableManager);

const SelectedCustomersTable = ({ invitedCustomers, onRemoveAll, onAction, onRemoveCustomer, sortDetails }) => {
  const selectedCount = _size(invitedCustomers);

  const columns = useMemo(() => getColumns(onRemoveCustomer), [onRemoveCustomer]);

  const subHeaderProps = useMemo(() => makeSubHeaderProps(selectedCount, onRemoveAll), [selectedCount, onRemoveAll]);

  return (
    <div className={`${styles.tableSection} p-b-16`}>
      <LocalTableManager
        tableComponent={BasicTable}
        columns={columns}
        data={invitedCustomers}
        onAction={onAction}
        subHeaderProps={subHeaderProps}
        tableProps={TABLE_PROPS}
        tableManagerProps={TABLE_MANAGER_PROPS}
        localActionTypes={LOCAL_ACTION_TYPES}
        sortDetails={sortDetails}
        handleOnAction={onAction}
        noDataText={__('No customers selected')}
      />
    </div>
  );
};

SelectedCustomersTable.propTypes = {
  invitedCustomers: PropTypes.arrayOf(PropTypes.instanceOf(CustomerBuilder)),
  onRemoveAll: PropTypes.func,
  onAction: PropTypes.func,
  onRemoveCustomer: PropTypes.func,
  sortDetails: PropTypes.object,
};

SelectedCustomersTable.defaultProps = {
  invitedCustomers: EMPTY_ARRAY,
  onRemoveAll: _noop,
  onAction: _noop,
  onRemoveCustomer: _noop,
  sortDetails: EMPTY_OBJECT,
};

export default compose(memo, withActions(INITIAL_STATE, ACTION_HANDLERS))(SelectedCustomersTable);
