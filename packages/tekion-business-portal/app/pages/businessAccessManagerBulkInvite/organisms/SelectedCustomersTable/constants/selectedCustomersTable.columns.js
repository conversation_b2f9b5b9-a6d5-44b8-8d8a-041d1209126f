/* eslint-disable react/prop-types */
// React
import React from 'react';

// Lodash
import _noop from 'lodash/noop';

// Components
import Text<PERSON>enderer from 'tcomponents/molecules/CellRenderers/TextRenderer';
import PhoneNumberDisplayCellRenderer from 'twidgets/cellRenderers/PhoneNumberDisplayCellRenderer';
import IconAsBtn from 'tcomponents/atoms/iconAsBtn';

// Constants
import { SIZES } from 'tcomponents/atoms/FontIcon';
import { EMPTY_OBJECT } from 'tbase/app.constants';

// Helpers
import {
  getCustomerDisplayName,
  getCustomerType,
  getChargeCustomer,
  getCustomerPhoneNumberProps,
} from '../helpers/selectedCustomersTable.columns';

const TrashIconCell = ({ original = EMPTY_OBJECT, onRemoveCustomer = _noop }) => (
  <IconAsBtn onClick={() => onRemoveCustomer(original)} size={SIZES.MD} containerClassName="flex-center">
    icon-trash
  </IconAsBtn>
);

export const COLUMN_IDS = {
  CUSTOMER: 'customer',
  EMAIL: 'email',
  MOBILE_NO: 'mobileNo',
  CHARGE_CUSTOMER: 'chargeCustomer',
  CUSTOMER_TYPE: 'customerType',
  ACTIONS: 'actions',
};

const CUSTOMER_COLUMN = {
  Header: __('Customer'),
  accessor: getCustomerDisplayName,
  id: COLUMN_IDS.CUSTOMER,
  key: COLUMN_IDS.CUSTOMER,
  Cell: TextRenderer,
  sortable: false,
  minWidth: 200,
  width: 250,
};

const EMAIL_COLUMN = {
  Header: __('Email'),
  accessor: customer => customer.getEmail(),
  id: COLUMN_IDS.EMAIL,
  key: COLUMN_IDS.EMAIL,
  sortAccessor: customer => customer.getEmail(),
  Cell: TextRenderer,
  sortable: true,
  minWidth: 180,
  width: 200,
};

const MOBILE_NO_COLUMN = {
  Header: __('Mobile No.'),
  id: COLUMN_IDS.MOBILE_NO,
  key: COLUMN_IDS.MOBILE_NO,
  Cell: PhoneNumberDisplayCellRenderer,
  getProps: getCustomerPhoneNumberProps,
  sortable: false,
  minWidth: 130,
  width: 160,
};

const CHARGE_CUSTOMER_COLUMN = {
  Header: __('Charge Customer'),
  accessor: getChargeCustomer,
  id: COLUMN_IDS.CHARGE_CUSTOMER,
  key: COLUMN_IDS.CHARGE_CUSTOMER,
  sortAccessor: customer => customer.getIsCharge(),
  Cell: TextRenderer,
  sortable: true,
  minWidth: 120,
  width: 140,
};

const CUSTOMER_TYPE_COLUMN = {
  Header: __('Customer Type'),
  accessor: getCustomerType,
  id: COLUMN_IDS.CUSTOMER_TYPE,
  key: COLUMN_IDS.CUSTOMER_TYPE,
  sortAccessor: customer => customer.getCustomerType(),
  Cell: TextRenderer,
  sortable: true,
  minWidth: 120,
  width: 140,
};

const ACTIONS_COLUMN = {
  Header: '',
  accessor: COLUMN_IDS.ACTIONS,
  id: COLUMN_IDS.ACTIONS,
  key: COLUMN_IDS.ACTIONS,
  Cell: TrashIconCell,
  sortable: false,
  width: 50,
  fixed: 'right',
};

const COLUMNS = [
  CUSTOMER_COLUMN,
  EMAIL_COLUMN,
  MOBILE_NO_COLUMN,
  CHARGE_CUSTOMER_COLUMN,
  CUSTOMER_TYPE_COLUMN,
  ACTIONS_COLUMN,
];

export default COLUMNS;
