// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { TABLE_ACTION_TYPES } from 'twidgets/organisms/tableManager';

export const INITIAL_STATE = {
  sortDetails: EMPTY_OBJECT,
};

export const LOCAL_ACTION_TYPES = [TABLE_ACTION_TYPES.TABLE_ITEMS_SORT];

export const TABLE_MANAGER_PROPS = {
  showSearch: false,
  showFilter: false,
  showHeader: false,
  showSubHeader: true,
  showTotalCount: false,
};

export const TABLE_PROPS = {
  showPagination: false,
  loading: false,
};
