// Constants
import { CUSTOMER_TYPES } from 'twidgets/appServices/core/constants/customerCommonConstants';

// Helpers
import { getCustomerFullName } from '../../../helpers/businessAccessManagerBulkInvite.general';

export const getCustomerType = customer => {
  const type = customer.getCustomerType();
  return CUSTOMER_TYPES[type] || type;
};

export const getChargeCustomer = customer => {
  const isCharge = customer.getIsCharge();
  return isCharge ? __('Yes') : __('No');
};

export const getCustomerDisplayName = customer => {
  const customerName = getCustomerFullName(customer);
  const customerNumber = customer.getCustomerNumber();
  return `${customerNumber} - ${customerName}`;
};

export const getCustomerPhoneNumberProps = rowInfo => {
  const { original: customer } = rowInfo;
  const primaryPhone = customer.getPrimaryPhone();
  if (!primaryPhone) {
    return null;
  }
  return {
    ...primaryPhone,
    value: primaryPhone.number,
  };
};
