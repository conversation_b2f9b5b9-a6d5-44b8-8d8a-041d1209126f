// Lodash
import _map from 'lodash/map';

// Components
import SelectCustomersTableSubHeaderLeftSection from '../molecules/SelectedCustomersTableSubHeaderLeftSection';

// Constants
import BASE_COLUMNS, { COLUMN_IDS } from '../constants/selectedCustomersTable.columns';

export const makeSubHeaderProps = (selectedCount, onRemoveAll) => ({
  subHeaderLeftActions: [
    {
      renderer: SelectCustomersTableSubHeaderLeftSection,
      renderOptions: {
        selectedCount,
        onRemoveAll,
      },
    },
  ],
});

const getUpdatedColumns = onRemoveCustomer => column => {
  if (column.id === COLUMN_IDS.ACTIONS) {
    return {
      ...column,
      Cell: props => column.Cell({ ...props, onRemoveCustomer }),
    };
  }
  return column;
};

export const getColumns = onRemoveCustomer => _map(BASE_COLUMNS, getUpdatedColumns(onRemoveCustomer));
