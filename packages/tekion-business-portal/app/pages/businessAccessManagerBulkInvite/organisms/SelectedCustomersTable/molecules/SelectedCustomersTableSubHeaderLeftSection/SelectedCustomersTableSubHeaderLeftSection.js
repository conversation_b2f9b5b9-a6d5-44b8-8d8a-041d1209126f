import React, { memo } from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Components
import Heading from 'tcomponents/atoms/Heading';
import Button from 'tcomponents/atoms/Button';

// Styles
import styles from './selectedCustomersTableSubHeaderLeftSection.module.scss';

const SelectedCustomersTableSubHeaderLeftSection = ({ selectedCount, onRemoveAll }) => {
  const isDisabled = selectedCount === 0;

  return (
    <div className={`${styles.container} p-y-24`}>
      <Heading size={4}>{__('Selected Customers')}</Heading>
      <Button
        view={Button.VIEW.TERTIARY}
        disabled={isDisabled}
        onClick={onRemoveAll}
        className={styles.removeAllButton}
        highlightOnHover={false}>
        {__('Remove all')}
      </Button>
      <span className={styles.separator}>|</span>
      <span className={styles.resultCount}>
        {selectedCount} {__('result(s)')}
      </span>
    </div>
  );
};

SelectedCustomersTableSubHeaderLeftSection.propTypes = {
  selectedCount: PropTypes.number,
  onRemoveAll: PropTypes.func,
};

SelectedCustomersTableSubHeaderLeftSection.defaultProps = {
  selectedCount: 0,
  onRemoveAll: _noop,
};

export default memo(SelectedCustomersTableSubHeaderLeftSection);
