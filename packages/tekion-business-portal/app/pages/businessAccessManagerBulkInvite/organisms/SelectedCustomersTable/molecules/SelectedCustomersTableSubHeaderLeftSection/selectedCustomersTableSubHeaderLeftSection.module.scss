@use "tstyles/component.scss" as component;
@use "tstyles/mixins/layout" as layout;
@use "tstyles/mixins/typography" as typography;

.container {
  @include layout.flex($align-items: center);
  gap: 2.4rem;
  background-color: component.$white;
}

.removeAllButton {
  color: component.$primary-color;
  font-size: component.$font-medium;
  font-family: component.$font-semi-bold;
}

.separator {
  @include typography.heading5($font-family: component.$font-medium, $color: component.$platinum);
  font-size: 3rem;
}

.resultCount {
  @include typography.heading4($font-family: component.$font-regular, $color: component.$atomic);
}
