/* eslint-disable import/order */
import React from 'react';
import PropTypes from 'prop-types';
import compose from 'recompose/compose';
import { connect } from 'react-redux';

// Lodash
import _get from 'lodash/get';
import _noop from 'lodash/noop';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { BUSINESS_PORTAL } from 'tbase/constants/appServices';
import ASSET_TYPES_BY_MODULE from 'tbusiness/constants/configurableEntity/assetTypesByModule';
import {
  BUSINESS_ACCESS_MANAGER_ASSET_TYPE,
  BUSINESS_ACCESS_MANAGER_ITEM_TYPE,
  DEFAULT_SORT_DETAIL,
} from './constants/businessAccessManagerList.general';
import { COLUMN_CONFIG_BY_KEY } from './helpers/businessAccessManagerList.columnConfig';

// Components
import BusinessAccessManagerList from './BusinessAccessMangerList';
import Loader from 'tcomponents/molecules/loader';

// Table reducers
import {
  getAllItems,
  getAppliedSortDetails,
  getFilterValue,
  isLoading,
  getSearchQuery,
  getSearchField,
  getSelectedFilters,
  getNextPageToken,
} from 'tcomponents/reducers/tableItemReducer';

// HOCs
import withAsyncReducer from 'tcomponents/connectors/withAsyncReducer';
import withUserPreferenceColumn from 'tcomponents/connectors/withUserPreferenceColumn';

// Table Generator
import { tableGeneratorReducer } from 'tcomponents/organisms/TableGenerator';

// Actions
import { getConfigurableEntityMetadata as getConfigurableEntityMetadataAction } from 'tbusiness/actions/configurableEntity';

// hooks
import useConfigurableEntityMetadata from 'twidgets/hooks/useConfigurableEntityMetadata';

import { getConfigurableEntityMetadata as getConfigurableEntityMetadataSelector } from 'tbusiness/reducers/configurableEntity';

const BusinessAccessManagerListContainer = props => {
  const {
    businessAccessManagerList,
    loading,
    sortDetails,
    selectedFilters,
    searchText,
    searchField,
    selectedItems,
    columns,
    columnConfigurator,
    isFetchingNextPage,
    nextPageToken,
    entityMetadata,
    getConfigurableEntityMetadata,
  } = props;

  const { isLoading: isMetadataLoading } = useConfigurableEntityMetadata(
    entityMetadata,
    getConfigurableEntityMetadata,
    ASSET_TYPES_BY_MODULE[BUSINESS_PORTAL].BUSINESS_ACCESS_MANAGER_LIST
  );

  if (isMetadataLoading) return <Loader />;

  return (
    <BusinessAccessManagerList
      businessAccessManagerList={businessAccessManagerList}
      loading={loading}
      sortDetails={sortDetails}
      selectedFilters={selectedFilters}
      searchText={searchText}
      searchField={searchField}
      columns={columns}
      columnConfigurator={columnConfigurator}
      selectedItems={selectedItems}
      isFetchingNextPage={isFetchingNextPage}
      nextPageToken={nextPageToken}
      entityMetadata={entityMetadata}
    />
  );
};

BusinessAccessManagerListContainer.propTypes = {
  businessAccessManagerList: PropTypes.array,
  loading: PropTypes.bool,
  sortDetails: PropTypes.object,
  selectedFilters: PropTypes.array,
  searchText: PropTypes.string,
  searchField: PropTypes.string,
  selectedItems: PropTypes.array,
  columns: PropTypes.array,
  columnConfigurator: PropTypes.object,
  isFetchingNextPage: PropTypes.bool,
  nextPageToken: PropTypes.string,
  entityMetadata: PropTypes.object,
  getConfigurableEntityMetadata: PropTypes.func,
};

BusinessAccessManagerListContainer.defaultProps = {
  businessAccessManagerList: EMPTY_ARRAY,
  loading: true,
  sortDetails: DEFAULT_SORT_DETAIL,
  selectedFilters: EMPTY_ARRAY,
  searchText: '',
  searchField: '',
  selectedItems: EMPTY_ARRAY,
  columns: EMPTY_ARRAY,
  columnConfigurator: null,
  isFetchingNextPage: false,
  nextPageToken: '',
  entityMetadata: undefined,
  getConfigurableEntityMetadata: _noop,
};

const mapStateToProps = state => {
  const data = _get(state, `${BUSINESS_PORTAL}.${BUSINESS_ACCESS_MANAGER_ITEM_TYPE}.tableItems`);
  const filterValue = getFilterValue(data);
  const additionalData = _get(data, 'additional', EMPTY_OBJECT);

  return {
    entityMetadata: getConfigurableEntityMetadataSelector(
      state,
      ASSET_TYPES_BY_MODULE[BUSINESS_PORTAL].BUSINESS_ACCESS_MANAGER_LIST
    ),
    businessAccessManagerList: getAllItems(data),
    sortDetails: getAppliedSortDetails(data) || DEFAULT_SORT_DETAIL,
    selectedItems: _get(additionalData, 'selectedItems', EMPTY_ARRAY),
    loading: isLoading(data, filterValue),
    searchText: getSearchQuery(data),
    searchField: getSearchField(data),
    selectedFilters: getSelectedFilters(data, filterValue) || EMPTY_ARRAY,
    nextPageToken: getNextPageToken(data),
  };
};

const mapDispatchToProps = {
  getConfigurableEntityMetadata: getConfigurableEntityMetadataAction,
};

export default compose(
  withAsyncReducer({
    storeKey: `${BUSINESS_PORTAL}.${BUSINESS_ACCESS_MANAGER_ITEM_TYPE}`,
    reducer: tableGeneratorReducer(BUSINESS_ACCESS_MANAGER_ITEM_TYPE),
  }),
  connect(mapStateToProps, mapDispatchToProps),
  withUserPreferenceColumn(
    BUSINESS_ACCESS_MANAGER_ASSET_TYPE,
    undefined,
    COLUMN_CONFIG_BY_KEY,
    true,
    undefined,
    undefined,
    undefined,
    true,
    true,
    true
  )
)(BusinessAccessManagerListContainer);
