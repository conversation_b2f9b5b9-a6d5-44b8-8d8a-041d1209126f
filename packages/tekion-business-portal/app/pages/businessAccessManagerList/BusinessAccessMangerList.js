/* eslint-disable import/order */
import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { HEADER_PROPS, TABLE_MANAGER_PROPS } from './constants/businessAccessManagerList.tableManagerProps';
import { BASE_INITIAL_STATE } from './constants/businessAccessManagerList.general';
import ACTION_HANDLERS from './businessAccessManagerList.actionHandlers';

// Components
import TableManager from 'twidgets/organisms/tableManager';
import { BasicTable } from 'twidgets/appServices/accounting/organisms/table';
import withCheckboxTable from 'tcomponents/organisms/withCheckboxTable';

// helpers
import { getTableProps } from './helpers/businessAccessManagerList.tableProps';
import { getFilterProps } from './helpers/businessAccessManagerList.filters';
import withActionHandlers from 'tcomponents/connectors/withActionHandlers';

const BasicCheckboxTable = withCheckboxTable(BasicTable);
function BusinessAccessManagerList({
  businessAccessManagerList,
  loading,
  onAction,
  sortDetails,
  selectedFilters,
  searchText,
  searchField,
  columns,
  columnConfigurator,
  selectedItems,
  nextPageToken,
  isFetchingNextPage,
  entityMetadata,
  selectedFilterGroup,
}) {
  const tableProps = useMemo(
    () =>
      getTableProps({
        loading,
        selectedItems,
        nextPageToken,
        columnConfigurator,
        isFetchingNextPage,
        sortDetails,
      }),
    [loading, selectedItems, nextPageToken, columnConfigurator, isFetchingNextPage, sortDetails]
  );

  const filterProps = useMemo(
    () =>
      getFilterProps({
        selectedFilters,
        entityMetadata,
        selectedFilterGroup,
      }),
    [selectedFilters, entityMetadata, selectedFilterGroup]
  );

  return (
    <div className="full-height full-width">
      <TableManager
        data={businessAccessManagerList}
        onAction={onAction}
        columns={columns}
        columnConfigurator={columnConfigurator}
        tableProps={tableProps}
        tableManagerProps={TABLE_MANAGER_PROPS}
        headerProps={HEADER_PROPS}
        selectedFilters={selectedFilters}
        searchText={searchText}
        searchField={searchField}
        sortDetails={sortDetails}
        filterProps={filterProps}
        selection={selectedItems}
        tableComponent={BasicCheckboxTable}
      />
    </div>
  );
}

BusinessAccessManagerList.propTypes = {
  businessAccessManagerList: PropTypes.array,
  loading: PropTypes.bool,
  onAction: PropTypes.func,
  sortDetails: PropTypes.object,
  selectedFilters: PropTypes.array,
  searchText: PropTypes.string,
  searchField: PropTypes.string,
  columns: PropTypes.array,
  columnConfigurator: PropTypes.object,
  selectedItems: PropTypes.array,
  isFetchingNextPage: PropTypes.bool,
  nextPageToken: PropTypes.object,
  selectedFilterGroup: PropTypes.string,
  entityMetadata: PropTypes.object,
};

BusinessAccessManagerList.defaultProps = {
  businessAccessManagerList: EMPTY_ARRAY,
  loading: false,
  onAction: _noop,
  sortDetails: EMPTY_OBJECT,
  selectedFilters: EMPTY_ARRAY,
  searchText: '',
  searchField: '',
  columns: null,
  columnConfigurator: null,
  selectedItems: EMPTY_ARRAY,
  isFetchingNextPage: false,
  nextPageToken: EMPTY_OBJECT,
  selectedFilterGroup: undefined,
  entityMetadata: undefined,
};

export default withActionHandlers(ACTION_HANDLERS, BASE_INITIAL_STATE)(BusinessAccessManagerList);
