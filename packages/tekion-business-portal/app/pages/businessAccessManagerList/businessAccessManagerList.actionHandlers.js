/* eslint-disable import/order */
// Lodash
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import { TABLE_ACTION_TYPES } from 'twidgets/organisms/tableManager';
import { FILTER_KEYS } from 'tcomponents/reducers/tableItemReducer';
import TABLE_ACTION from 'twidgets/appServices/accounting/organisms/table/constants/actionTypes';
import { BUSINESS_ACCESS_MANAGER_ITEM_TYPE } from './constants/businessAccessManagerList.general';

// helpers
import { buildRequest } from './helpers/businessAccessManagerList.request';

// table action creators
import {
  getErrorItemsAction,
  getSetAdditionalDataAction,
  getClearAction,
  getAdditionalDataClearAction,
  getLoadingItemsAction,
} from 'tbase/actionCreators/tableItems';
import { getBatchedActionsToDispatch } from 'tcomponents/helpers/tableItemHelper';

// Readers
import businessAccessMangerListReader from './readers/BusinessAccessManagerList';

// services
import { fetchCustomerList } from './services/businessAccessManagerList.service';

const fetchBusinessAccessManagerItems = async ({ getState, dispatch, ...overrides }) => {
  const { searchText, selectedFilters, sortDetails, nextPageToken } = getState();

  // Use overrides if provided, otherwise use state values
  const params = {
    searchText,
    selectedFilters,
    sortDetails,
    nextPageToken,
    refreshData: true,
    ...overrides,
  };

  const filterValue = _isEmpty(params?.selectedFilters) ? FILTER_KEYS.ALL : FILTER_KEYS.FILTER;

  if (params.refreshData) {
    dispatch([
      getLoadingItemsAction(BUSINESS_ACCESS_MANAGER_ITEM_TYPE, {
        filterValue,
        selectedFilters: params?.selectedFilters,
      }),
    ]);
  }
  const request = buildRequest({
    searchText: params?.searchText,
    selectedFilters: params?.selectedFilters,
    sortDetails: params?.sortDetails,
    nextPageToken: params?.nextPageToken,
  });
  try {
    const customerList = await fetchCustomerList(request);
    if (params.refreshData) {
      dispatch([
        getClearAction(BUSINESS_ACCESS_MANAGER_ITEM_TYPE),
        getAdditionalDataClearAction(BUSINESS_ACCESS_MANAGER_ITEM_TYPE),
      ]);
    }
    const response = customerList?.data;
    // API consumer code to change post testing
    if (customerList?.data) {
      dispatch(
        getBatchedActionsToDispatch({
          itemType: BUSINESS_ACCESS_MANAGER_ITEM_TYPE,
          items: businessAccessMangerListReader.customerPortalInfos(response) || EMPTY_ARRAY,
          filterValue,
          selectedFilters: params?.selectedFilters,
          sortDetails: params?.sortDetails,
          searchQuery: params?.searchText,
          nextPageToken: businessAccessMangerListReader.nextPageToken(response),
          idGetter: 'customerId',
          additional: { selectedItems: EMPTY_ARRAY },
        })
      );
    }
  } catch (error) {
    dispatch(
      getErrorItemsAction(BUSINESS_ACCESS_MANAGER_ITEM_TYPE, {
        filterValue,
        selectedFilters: params?.selectedFilters,
        error,
        itemType: BUSINESS_ACCESS_MANAGER_ITEM_TYPE,
      })
    );
  }
};

const handleTableItemsFetch = async (action, { dispatch, getState, setState }) => {
  const filterDetails = _get(action, 'payload.value.filterDetails');
  const { selectedFilterGroupId, selectedGroupFilters } = filterDetails;
  const { selectedFilters: prevSelectedFilters } = getState();

  const selectedFilters = _isEmpty(selectedGroupFilters) ? prevSelectedFilters : selectedGroupFilters;

  await fetchBusinessAccessManagerItems({
    getState,
    dispatch,
    selectedFilters,
  });
  setState({ selectedFilterGroup: selectedFilterGroupId });
};

const handlePageUpdate = async (_action, { dispatch, getState, setState }) => {
  setState({ isFetchingNextPage: true });
  await fetchBusinessAccessManagerItems({ getState, dispatch, refreshData: false, setState });
  setState({ isFetchingNextPage: false });
};

const handleFilterChange = async (action, { dispatch, getState, setState }) => {
  const selectedFilters = _get(action, 'payload.value', EMPTY_ARRAY);
  const selectedFilterGroup = _get(action, 'payload.selectedFilterGroup');
  const isInitialFilterApply = _get(action, 'payload.additional.isInitialFilterApply');
  if (isInitialFilterApply) return;
  await fetchBusinessAccessManagerItems({
    getState,
    dispatch,
    selectedFilters,
    selectedFilterGroup,
  });
  setState({ selectedFilterGroup });
};

const handleSearch = async (action, { dispatch, getState }) => {
  const searchText = _get(action, 'payload.value', '');

  await fetchBusinessAccessManagerItems({
    getState,
    dispatch,
    searchText,
  });
};

const handleRefresh = async (_action, { dispatch, getState }) => {
  await fetchBusinessAccessManagerItems({ getState, dispatch, nextPageToken: undefined });
};

const handleTableItemSelect = (action, { dispatch }) => {
  const selectedItems = _get(action, 'payload.value', EMPTY_ARRAY);
  dispatch(getSetAdditionalDataAction(BUSINESS_ACCESS_MANAGER_ITEM_TYPE, { additional: { selectedItems } }));
};

const handleSortChange = async (action, { dispatch, getState }) => {
  const sortDetails = _get(action, 'payload.sortDetails');
  const { sortField, sortOrder } = sortDetails;
  await fetchBusinessAccessManagerItems({ getState, dispatch, sortDetails: { [sortField]: sortOrder } });
};

const ACTION_HANDLERS = {
  [TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH_WITH_FILTER_AND_SORT_PREFERENCES]: handleTableItemsFetch,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_PAGE_UPDATE]: handlePageUpdate,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_SET_FILTER]: handleFilterChange,
  [TABLE_ACTION_TYPES.TABLE_SEARCH]: handleSearch,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_REFRESH]: handleRefresh,
  [TABLE_ACTION_TYPES.TABLE_ITEM_SELECT]: handleTableItemSelect,
  [TABLE_ACTION.TABLE_ELEMENTS_SORT]: handleSortChange,
};

export default ACTION_HANDLERS;
