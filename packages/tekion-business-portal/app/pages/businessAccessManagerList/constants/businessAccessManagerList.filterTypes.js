// Utils
import { getAsyncSelectResourceProps } from 'tcomponents/utils/filterUtils';

// Constants
import { FILTER_SECTION_FILTER_TYPE } from 'tcomponents/organisms/filterSection';
import { RESOURCE_TYPE } from 'tbase/bulkResolvers/constants/resourceType';
import OPERATORS from 'tbase/constants/filterOperators';
import { PORTAL_STATUS_TYPES, PORTAL_STATUS_LABELS } from './businessAccessManagerList.portalStatus';

const FILTER_TYPE_IDS = {
  CUSTOMER: 'customerId',
  PORTAL_STATUS: 'portalStatus',
};

const CUSTOMER_FILTER = {
  id: FILTER_TYPE_IDS.CUSTOMER,
  key: FILTER_TYPE_IDS.CUSTOMER,
  name: __('Customer'),
  type: FILTER_SECTION_FILTER_TYPE.ASYNC_MULTI_SELECT,
  resourceType: RESOURCE_TYPE.CUSTOMER_MINIMAL,
  resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.CUSTOMER),
  additional: {
    shouldFetchOnValueChange: true,
  },
};

const PORTAL_STATUS_FILTER = {
  id: FILTER_TYPE_IDS.PORTAL_STATUS,
  key: FILTER_TYPE_IDS.PORTAL_STATUS,
  name: __('Portal Status'),
  type: FILTER_SECTION_FILTER_TYPE.MULTI_SELECT,
  additional: {
    options: [
      {
        label: PORTAL_STATUS_LABELS[PORTAL_STATUS_TYPES.INVITE_INITIATED],
        value: PORTAL_STATUS_TYPES.INVITE_INITIATED,
      },
      {
        label: PORTAL_STATUS_LABELS[PORTAL_STATUS_TYPES.INVITE_FAILED],
        value: PORTAL_STATUS_TYPES.INVITE_FAILED,
      },
      {
        label: PORTAL_STATUS_LABELS[PORTAL_STATUS_TYPES.PORTAL_ACCESSED],
        value: PORTAL_STATUS_TYPES.PORTAL_ACCESSED,
      },
      {
        label: PORTAL_STATUS_LABELS[PORTAL_STATUS_TYPES.PORTAL_DISABLED],
        value: PORTAL_STATUS_TYPES.PORTAL_DISABLED,
      },
    ],
  },
};

export const FILTER_CONFIG_BY_ID = {
  [FILTER_TYPE_IDS.CUSTOMER]: CUSTOMER_FILTER,
  [FILTER_TYPE_IDS.PORTAL_STATUS]: PORTAL_STATUS_FILTER,
};

export const DEFAULT_FILTER_VALUES = [
  {
    type: FILTER_TYPE_IDS.PORTAL_STATUS,
    operator: OPERATORS.IN,
    values: [],
  },
];
