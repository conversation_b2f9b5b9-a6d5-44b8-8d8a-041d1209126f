// constants
import SORT_ORDER_KEY from 'tbase/constants/sortOrder';
import COLUMN_IDS from './businessAccessManagerList.columnIds';

export const BUSINESS_ACCESS_MANAGER_ITEM_TYPE = 'businessAccessManager';
export const BUSINESS_ACCESS_MANAGER_ASSET_TYPE = 'BUSINESS_ACCESS_MANAGER';

export const SORT_FIELD_MAPPER = {
  [COLUMN_IDS.CUSTOMER]: 'customerNumber',
};

export const DEFAULT_SORT_DETAIL = {
  [COLUMN_IDS.CUSTOMER]: SORT_ORDER_KEY.ASC,
};

export const BASE_INITIAL_STATE = {
  isFetchingNextPage: false,
};

export const PAGE_SIZE = 500;
