// constants
import { COLOR_MAP } from 'tbase/constants/statusColors';

export const PORTAL_STATUS_TYPES = {
  INVITE_INITIATED: 'INVITE_INITIATED',
  INVITE_FAILED: 'INVITE_FAILED',
  PORTAL_ACCESSED: 'PORTAL_ACCESSED',
  PORTAL_DISABLED: 'PORTAL_DISABLED',
};

export const PORTAL_STATUS_LABELS = {
  [PORTAL_STATUS_TYPES.INVITE_INITIATED]: __('Invite Initiated'),
  [PORTAL_STATUS_TYPES.INVITE_FAILED]: __('Invite Failed'),
  [PORTAL_STATUS_TYPES.PORTAL_ACCESSED]: __('Portal Accessed'),
  [PORTAL_STATUS_TYPES.PORTAL_DISABLED]: __('Portal Disabled'),
};

export const PORTAL_STATUS_COLOR_MAP = {
  [PORTAL_STATUS_TYPES.INVITE_INITIATED]: COLOR_MAP.BLACK_OUTLINED,
  [PORTAL_STATUS_TYPES.INVITE_FAILED]: COLOR_MAP.RED,
  [PORTAL_STATUS_TYPES.PORTAL_ACCESSED]: COLOR_MAP.GREEN,
  [PORTAL_STATUS_TYPES.PORTAL_DISABLED]: COLOR_MAP.GREY,
};
