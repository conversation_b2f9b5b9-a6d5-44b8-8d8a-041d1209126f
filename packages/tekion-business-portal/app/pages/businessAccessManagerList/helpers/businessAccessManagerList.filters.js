// Builders
import ConfigurableEntityMetadata from 'tbusiness/builders/ConfigurableEntityMetadata';

// helpers
import { getFilterProps as buildConfigurableFilterProps } from 'twidgets/appServices/accounting/helpers/configurableTable';

// Constants
import { DEFAULT_FILTER_BEHAVIOR } from 'tcomponents/organisms/filterSection/constants/filterSection.constants';
import MODULES from 'tbase/constants/modules';
import MODULE_ASSET_TYPES from 'tbase/constants/moduleAssetTypes';
import { DEFAULT_FILTER_VALUES, FILTER_CONFIG_BY_ID } from '../constants/businessAccessManagerList.filterTypes';

export const getFilterProps = ({
  entityMetadata = new ConfigurableEntityMetadata(),
  selectedFilters,
  selectedFilterGroup,
}) =>
  buildConfigurableFilterProps(
    entityMetadata,
    selectedFilters,
    FILTER_CONFIG_BY_ID,
    true,
    DEFAULT_FILTER_VALUES,
    false,
    selectedFilterGroup,
    MODULE_ASSET_TYPES.BUSINESS_ACCESS_MANAGER_LIST,
    MODULES.BUSINESS_ACCESS_MANAGER,
    true,
    false,
    DEFAULT_FILTER_BEHAVIOR.PERSIST_LOCAL
  );
