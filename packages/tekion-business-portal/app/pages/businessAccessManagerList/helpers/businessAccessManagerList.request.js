// lodash
import _head from 'lodash/head';
import _entries from 'lodash/entries';
import _reduce from 'lodash/reduce';

// Builders
import Request from 'tbase/builders/request/Request';
import { addFilterToRequest } from 'tbase/builders/request/addFilterToRequest';

// constants
import { DEFAULT_SORT_DETAIL, SORT_FIELD_MAPPER, PAGE_SIZE } from '../constants/businessAccessManagerList.general';

const getRequestSortInfo = (sortDetails = DEFAULT_SORT_DETAIL) => {
  const [sortField, sortOrder] = _head(_entries(sortDetails));
  const mappedSortValue = SORT_FIELD_MAPPER[sortField] || sortField;
  return {
    sortBy: mappedSortValue,
    sortOrder,
  };
};

export const buildRequest = ({ searchText, selectedFilters, sortDetails, nextPageToken }) => {
  const request = new Request();

  request.setSearchString(searchText).setNextPageToken(nextPageToken).setPage(0, PAGE_SIZE);

  _reduce(selectedFilters, addFilterToRequest, request);

  if (sortDetails) {
    const sortInfo = getRequestSortInfo(sortDetails);
    request.addSort(sortInfo.sortBy, sortInfo.sortOrder);
  }
  return request;
};
