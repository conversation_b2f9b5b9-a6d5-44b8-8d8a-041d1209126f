// Lodash
import _isNull from 'lodash/isNull';
import _stubTrue from 'lodash/stubTrue';
import _isEmpty from 'lodash/isEmpty';

// Constants
import { TABLE_TYPES } from '@tekion/tekion-widgets/src/organisms/tableManager';
import { EMPTY_ARRAY } from 'tbase/app.constants';
import { BUSINESS_ACCESS_MANAGER_ITEM_TYPE } from '../constants/businessAccessManagerList.general';

export const getTableProps = ({
  loading,
  selectedItems = EMPTY_ARRAY,
  nextPageToken,
  columnConfigurator,
  isFetchingNextPage,
  sortDetails,
}) => ({
  isFetchingNextPage,
  hasInfiniteScroll: true,
  loading,
  itemType: BUSINESS_ACCESS_MANAGER_ITEM_TYPE,
  getIsAllDataFetched: _isEmpty(nextPageToken) ? _stubTrue : () => _isNull(nextPageToken),
  nextPageToken,
  type: TABLE_TYPES.WITH_CHECKBOX,
  isSelectionEnabled: true,
  selection: selectedItems,
  columnConfigurator,
  sortDetails,
});
