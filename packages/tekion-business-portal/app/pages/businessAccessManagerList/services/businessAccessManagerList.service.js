/* eslint-disable no-console */
/* eslint-disable no-promise-executor-return */
// HTTP Client
// import httpClient from '@tekion/tekion-base/services/apiService/httpClient';
// import { URL_TYPES } from '@tekion/tekion-base/constants/api';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';

// Dummy data utilities
import { DUMMY_CUSTOMERS, getPaginatedCustomers, filterCustomers, sortCustomers } from '../utils/dummyData';

// TODO: Replace with Actual api post integration Testing
export const fetchCustomerList = async (request = EMPTY_OBJECT) => {
  try {
    // Extract parameters from Request builder instance or fallback to direct object
    const searchText = request.searchText || '';
    const filters = request.filters || EMPTY_ARRAY;
    const sort = request.sort || EMPTY_ARRAY;
    const nextPageToken = request.nextPageToken || '';
    const size = 500; // Default batch size

    console.log('Fetching customer list with request:', {
      searchText,
      filters: filters.length,
      sort: sort.length,
      nextPageToken,
      size,
    });

    // Debug: Log the actual request structure
    if (process.env.NODE_ENV === 'development') {
      console.log('Request object structure:', JSON.stringify(request, null, 2));
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Apply filters and search to dummy data
    let filteredCustomers = filterCustomers(DUMMY_CUSTOMERS, searchText, filters);

    // Apply sorting
    filteredCustomers = sortCustomers(filteredCustomers, sort);

    // Get paginated results
    const paginatedResult = getPaginatedCustomers(filteredCustomers, nextPageToken, size);

    console.log(
      `Returning batch ${paginatedResult.currentBatch} of ${paginatedResult.totalBatches} (${paginatedResult.items.length} items)`
    );
    console.log('First customer in batch:', paginatedResult.items[0]);

    // Return in new expected API format with customerPortalInfos
    return {
      data: {
        customerPortalInfos: paginatedResult.items,
        totalCount: paginatedResult.totalCount,
        hasMore: paginatedResult.hasMore,
        nextPageToken:
          paginatedResult.totalBatches === paginatedResult.currentBatch ? null : paginatedResult.nextPageToken,
        currentPage: paginatedResult.currentBatch - 1,
        pageSize: size,
      },
      success: true,
    };
  } catch (error) {
    console.error('Error fetching customer list:', error);
    throw error;
  }
};
