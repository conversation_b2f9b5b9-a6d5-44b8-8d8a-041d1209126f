/* eslint-disable no-plusplus */
/* eslint-disable no-continue */
// Dummy data generator for Business Access Manager testing

import { PORTAL_STATUS_TYPES } from '../constants/businessAccessManagerList.portalStatus';

// Sample customer names and companies
const CUSTOMER_NAMES = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
];

// Generate a single dummy customer
const generateCustomer = index => {
  const customerName = CUSTOMER_NAMES[index % CUSTOMER_NAMES.length];
  const nameParts = customerName.split(' ');
  const firstName = nameParts[0];
  const lastName = nameParts.slice(1).join(' ') || 'Doe';

  // Generate 6-digit customer ID like in the image (109898, 407161, etc.)
  const customerId = String(Math.floor(Math.random() * 900000) + 100000);

  // Random portal status
  const statuses = Object.values(PORTAL_STATUS_TYPES);
  const portalStatus = statuses[Math.floor(Math.random() * statuses.length)];

  // Generate email
  const emailDomain = ['gmail.com', 'yahoo.com', 'outlook.com', 'company.com'][Math.floor(Math.random() * 4)];
  const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${emailDomain}`;

  // Generate phone number components
  const phoneNumber = String(Math.floor(Math.random() * 9000000) + 1000000); // 7-digit number
  const countryCode = '+91';
  const extension = String(Math.floor(Math.random() * 9) + 1);
  const ndc = Math.random() > 0.5;

  // Generate last activity timestamp (only for accessed portals)
  let lastActivity = null;
  if (portalStatus === PORTAL_STATUS_TYPES.PORTAL_ACCESSED) {
    const daysAgo = Math.floor(Math.random() * 30); // Last 30 days
    lastActivity = Date.now() - daysAgo * 24 * 60 * 60 * 1000;
  }

  // Create the new structure matching the required format
  const customerPortalInfo = {
    customerId,
    firstName,
    lastName,
    customerNumber: customerId,
    email,
    primaryPhone: {
      number: phoneNumber,
      countryCode,
      extension,
      ndc,
    },
    lastActivity,
    portalStatus,
  };

  // Also maintain backward compatibility fields for existing components
  const backwardCompatibleFields = {
    id: customerId,
    customerName,
    customer: `${customerId} – ${customerName}`,
    // moduleAccess: 'ACCOUNT_RECEIVABLE',
    mobileNo: `${countryCode} ${phoneNumber}`,
    starredMobile: `${countryCode} ${phoneNumber}`,
    lastLoginActivity: lastActivity ? new Date(lastActivity).toISOString() : null,
    createdDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
  };

  return {
    ...customerPortalInfo,
    ...backwardCompatibleFields,
  };
};

// Generate batch of customers
export const generateCustomerBatch = (batchSize = 100, startIndex = 0) => {
  const customers = [];
  for (let i = 0; i < batchSize; i++) {
    customers.push(generateCustomer(startIndex + i));
  }
  return customers;
};

// Generate multiple batches
export const generateMultipleBatches = (numberOfBatches = 5, batchSize = 100) => {
  const allCustomers = [];
  for (let batch = 0; batch < numberOfBatches; batch++) {
    const batchCustomers = generateCustomerBatch(batchSize, batch * batchSize);
    allCustomers.push(...batchCustomers);
  }
  return allCustomers;
};

// Get paginated data with afterKey simulation
export const getPaginatedCustomers = (allCustomers, nextPageToken = '', pageSize = 500) => {
  let startIndex = 0;

  if (nextPageToken) {
    // Find the index of the customer with the afterKey (using customerId)
    const afterIndex = allCustomers.findIndex(
      customer => customer.customerId === nextPageToken || customer.id === nextPageToken
    );
    startIndex = afterIndex >= 0 ? afterIndex + 1 : 0;
  }

  const endIndex = startIndex + pageSize;
  const items = allCustomers.slice(startIndex, endIndex);
  const hasMore = endIndex < allCustomers.length;
  const newAfterKey =
    hasMore && items.length > 0 ? items[items.length - 1].customerId || items[items.length - 1].id : '';

  return {
    items,
    totalCount: allCustomers.length,
    hasMore,
    nextPageToken: newAfterKey,
    currentBatch: Math.floor(startIndex / pageSize) + 1,
    totalBatches: Math.ceil(allCustomers.length / pageSize),
  };
};

// Filter customers based on search and filters
export const filterCustomers = (customers, searchText = '', filters = []) => {
  let filteredCustomers = [...customers];

  // Apply search filter (prefix search on Customer ID and Name)
  if (searchText) {
    const searchLower = searchText.toLowerCase();
    filteredCustomers = filteredCustomers.filter(customer => {
      const customerId = customer.customerId?.toLowerCase() || '';
      const customerName = customer.customerName?.toLowerCase() || '';
      const firstName = customer.firstName?.toLowerCase() || '';
      const lastName = customer.lastName?.toLowerCase() || '';
      const fullName = `${firstName} ${lastName}`.trim().toLowerCase();

      return (
        customerId.startsWith(searchLower) ||
        customerName.startsWith(searchLower) ||
        firstName.startsWith(searchLower) ||
        lastName.startsWith(searchLower) ||
        fullName.startsWith(searchLower)
      );
    });
  }

  // Apply filters from Request builder format
  if (filters && filters.length > 0) {
    filters.forEach(filter => {
      const { field, values, operator } = filter;

      if (!values || values.length === 0) return;

      // Handle different filter types
      if (field === 'portalStatus' && operator === 'IN') {
        filteredCustomers = filteredCustomers.filter(customer => values.includes(customer.portalStatus));
      } else if (field === 'customerId' && operator === 'IN') {
        filteredCustomers = filteredCustomers.filter(customer => values.includes(customer.customerId));
      }
    });
  }

  return filteredCustomers;
};

// Sort customers - handles both old format and Request builder format
export const sortCustomers = (customers, sortDetails = []) => {
  if (
    !sortDetails ||
    (Array.isArray(sortDetails) && sortDetails.length === 0) ||
    (!Array.isArray(sortDetails) && Object.keys(sortDetails).length === 0)
  ) {
    return customers;
  }

  return [...customers].sort((a, b) => {
    let sortArray = [];

    // Convert old format to array format
    if (!Array.isArray(sortDetails)) {
      sortArray = Object.entries(sortDetails).map(([field, direction]) => ({
        field,
        order: direction,
      }));
    } else {
      // Request builder format: array of {field, order} objects
      sortArray = sortDetails;
    }

    // eslint-disable-next-line no-restricted-syntax
    for (const sortItem of sortArray) {
      const { field, order: direction } = sortItem;
      let aValue = a[field];
      let bValue = b[field];

      // Handle special field mappings
      if (field === 'customerNumber') {
        // Map customerNumber to customerId for sorting
        aValue = a.customerId;
        bValue = b.customerId;
      } else if (field === 'customer') {
        // Use customerName if available, otherwise construct from firstName/lastName
        aValue = a.customerName || `${a.firstName || ''} ${a.lastName || ''}`.trim();
        bValue = b.customerName || `${b.firstName || ''} ${b.lastName || ''}`.trim();
      } else if (field === 'lastActivity' || field === 'lastLoginActivity') {
        // Handle both new and old field names for last activity
        aValue = a.lastActivity || a.lastLoginActivity;
        bValue = b.lastActivity || b.lastLoginActivity;
      }

      // Handle null/undefined values
      if (aValue == null && bValue == null) continue;
      if (aValue == null) return direction === 'ASC' || direction === 'asc' ? -1 : 1;
      if (bValue == null) return direction === 'ASC' || direction === 'asc' ? 1 : -1;

      // Handle dates
      if (aValue instanceof Date && bValue instanceof Date) {
        const comparison = aValue.getTime() - bValue.getTime();
        if (comparison !== 0) {
          return direction === 'ASC' || direction === 'asc' ? comparison : -comparison;
        }
        continue;
      }

      // Handle strings
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        if (comparison !== 0) {
          return direction === 'ASC' || direction === 'asc' ? comparison : -comparison;
        }
        continue;
      }

      // Handle numbers
      const comparison = aValue - bValue;
      if (comparison !== 0) {
        return direction === 'ASC' || direction === 'asc' ? comparison : -comparison;
      }
    }
    return 0;
  });
};

// Generate initial dummy data (1500 customers = 3 batches of 500)
export const DUMMY_CUSTOMERS = generateMultipleBatches(8, 500);

export default {
  generateCustomerBatch,
  generateMultipleBatches,
  getPaginatedCustomers,
  filterCustomers,
  sortCustomers,
  DUMMY_CUSTOMERS,
};
