import _reduce from 'lodash/reduce';
import _isFunction from 'lodash/isFunction';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _omit from 'lodash/omit';
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _pick from 'lodash/pick';
import _uniq from 'lodash/uniq';
import _isNil from 'lodash/isNil';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import {
  fetchLookupMultipleResource,
  handleLookupMultipleResourceSuccess,
  getResolvedData as getDefaultResolvedData,
} from '@tekion/tekion-base/bulkResolvers/bulkResolvers';
import getEntity from '@tekion/tekion-base/bulkResolvers/helpers/bulkResolvers.getEntity';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers/constants/resourceType';
import { getMasterPartLookupByKeys } from '../partsAndInventory/actions/partMaster.actions';
import { getMasterPartLookupByKeysPayload } from '../partsAndInventory/helpers/partMaster.helpers';

const RESOURCE_TYPE_VS_DEFAULT_SERVICE = {
  [RESOURCE_TYPE.PART]: (...args) =>
    getMasterPartLookupByKeys(...args).then(partResponse => ({
      [RESOURCE_TYPE.PART]: partResponse,
    })),
};

const RESOURCE_TYPE_VS_DEFAULT_PAYLOAD_GENERATOR = {
  [RESOURCE_TYPE.PART]: (keys, contextParams) => {
    const { warehouseId, isMultiWarehouseEnabled } = contextParams || EMPTY_OBJECT;
    return getMasterPartLookupByKeysPayload({ partIds: keys, warehouseId, isMultiWarehouseEnabled });
  },
};

// Extract excluded resources and create filtered resources
const extractExcludedResources = (toBeResolvedResources, resourcesToExclude = []) => {
  const filteredResources = _omit(toBeResolvedResources, resourcesToExclude);
  const excludedResources = _pick(toBeResolvedResources, resourcesToExclude);

  return { filteredResources, excludedResources };
};

// Create filtered entity props
const createFilteredEntityProps = (entityAdditionalProps, filteredResources) => ({
  ...entityAdditionalProps,
  getResourcesToBeResolved: () => ({
    toBeResolvedResources: filteredResources,
  }),
});

// Create non-excluded resources promise
const createMainResourcesPromise = (entityType, dataToBeResolved, params, filteredEntityAdditionalProps) => {
  const {
    localLookup,
    assetsToResolve,
    createLookupMapByResourceType,
    resolveBulkResourceService,
    resourcesToBeResolvedInBatches,
  } = params || EMPTY_OBJECT;

  return fetchLookupMultipleResource(entityType, dataToBeResolved, {
    localLookup,
    entityAdditionalProps: filteredEntityAdditionalProps,
    assetsToResolve,
    createLookupMapByResourceType,
    resolveBulkResourceService,
    resourcesToBeResolvedInBatches,
  });
};

// Create custom resource promise for a specific resource type
const createCustomResourcePromise = (resourceType, resourceData, resourceMapper, contextParams) => {
  if (!resourceData || _isEmpty(resourceData?.keys)) {
    return Promise.resolve(EMPTY_OBJECT);
  }

  const apiToCall =
    _get(resourceMapper, [resourceType, 'service']) || _get(RESOURCE_TYPE_VS_DEFAULT_SERVICE, resourceType) || _noop;

  const payloadGenerator =
    _get(resourceMapper, [resourceType, 'payloadGenerator']) ||
    _get(RESOURCE_TYPE_VS_DEFAULT_PAYLOAD_GENERATOR, resourceType) ||
    _noop;

  const payload = payloadGenerator(resourceData.keys, contextParams);
  return apiToCall(payload, contextParams);
};

export const getResolvedData = (entityType, dataToBeResolved, params = EMPTY_OBJECT) => {
  const {
    resourceTypeMapper = EMPTY_OBJECT,
    localLookup,
    entityAdditionalProps,
    assetsToResolve,
    lookupObjectAdder,
    excludeResourceTypes = EMPTY_ARRAY,
    contextParams = EMPTY_OBJECT,
  } = params || EMPTY_OBJECT;

  const { isMultiWarehouseEnabled, warehouseId } = contextParams || EMPTY_OBJECT;

  // If multi-warehouse is enabled, exclude PART resource type and use custom lookup
  const resourceTypesToExclude = _uniq([
    ...excludeResourceTypes,
    ...(isMultiWarehouseEnabled && !_isNil(warehouseId) ? [RESOURCE_TYPE.PART] : EMPTY_ARRAY),
  ]);

  if (_isEmpty(resourceTypesToExclude)) {
    return getDefaultResolvedData(entityType, dataToBeResolved, params);
  }

  const entity = getEntity(entityAdditionalProps, entityType);
  const { getResourcesToBeResolved } = entity;
  if (!_isFunction(getResourcesToBeResolved)) {
    return Promise.resolve(dataToBeResolved);
  }

  const { toBeResolvedResources } = getResourcesToBeResolved(dataToBeResolved, assetsToResolve);

  // Extract excluded resources and create filtered resources
  const { filteredResources, excludedResources } = extractExcludedResources(
    toBeResolvedResources,
    resourceTypesToExclude
  );

  // Create filtered entity props
  const filteredEntityAdditionalProps = createFilteredEntityProps(entityAdditionalProps, filteredResources);

  // Create promise for main resources
  const mainResourcesPromise = createMainResourcesPromise(
    entityType,
    dataToBeResolved,
    params,
    filteredEntityAdditionalProps
  );

  // Create promises for each excluded resource type
  const customResourcePromises = _map(excludedResources, (resourceData, resourceType) =>
    createCustomResourcePromise(resourceType, resourceData, _get(resourceTypeMapper, resourceType), contextParams)
  );

  return Promise.all([mainResourcesPromise, ...customResourcePromises]).then(responses => {
    // Merge all lookup data
    const mergedLookupData = _reduce(
      responses,
      (acc, response) => ({
        ...acc,
        ...(response || EMPTY_OBJECT),
      }),
      EMPTY_OBJECT
    );

    // Process the complete data with the original handler
    return handleLookupMultipleResourceSuccess({
      entityType,
      localLookup,
      entityAdditionalProps,
      dataToBeResolved,
      resourceTypeMapper,
      assetsToResolve,
      lookupObjectAdder,
    })(mergedLookupData);
  });
};
