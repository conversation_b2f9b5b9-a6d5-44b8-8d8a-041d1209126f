import { SEARCH_USER_BY_ALL_PERSONAS } from '@tekion/tekion-base/constants/deal/common';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

// Mock dependencies
jest.mock('@tekion/tekion-business/src/actions/userActions', () => ({
  fetchAndSearchUsersByPersona: jest.fn(),
  fetchUserByIds: jest.fn(),
  fetchUsers: jest.fn(),
}));

describe('User Helper Functions with SEARCH_USER_BY_ALL_PERSONAS', () => {
  describe('SEARCH_USER_BY_ALL_PERSONAS integration', () => {
    it('should have correct constant value', () => {
      expect(SEARCH_USER_BY_ALL_PERSONAS).toEqual(['all']);
    });

    it('should be an array suitable for API calls', () => {
      expect(Array.isArray(SEARCH_USER_BY_ALL_PERSONAS)).toBe(true);
      expect(SEARCH_USER_BY_ALL_PERSONAS).toHaveLength(1);
    });
  });

  describe('User lookup parameter generation', () => {
    it('should create lookup params with all personas when using SEARCH_USER_BY_ALL_PERSONAS', () => {
      const mockLookupParams = {
        lookUpSearchApi: jest.fn(),
        lookupByKeysApi: jest.fn(),
        filters: [
          { values: SEARCH_USER_BY_ALL_PERSONAS, field: 'personas' },
          { values: [true], field: 'isActive' },
        ],
      };

      expect(mockLookupParams.filters[0].values).toEqual(['all']);
      expect(mockLookupParams.filters[0].field).toBe('personas');
    });

    it('should handle empty personas gracefully', () => {
      const mockLookupParams = {
        filters: [
          { values: SEARCH_USER_BY_ALL_PERSONAS || EMPTY_ARRAY, field: 'personas' },
        ],
      };

      expect(mockLookupParams.filters[0].values).toEqual(['all']);
    });
  });

  describe('User search functionality', () => {
    it('should support searching users across all personas', () => {
      const searchConfig = {
        personas: SEARCH_USER_BY_ALL_PERSONAS,
        searchText: 'test user',
        isActive: true,
      };

      expect(searchConfig.personas).toEqual(['all']);
      expect(searchConfig.searchText).toBe('test user');
      expect(searchConfig.isActive).toBe(true);
    });

    it('should create proper filter structure for all personas search', () => {
      const filterConfig = {
        field: 'personas',
        operator: 'IN',
        values: SEARCH_USER_BY_ALL_PERSONAS,
      };

      expect(filterConfig.values).toEqual(['all']);
      expect(filterConfig.field).toBe('personas');
      expect(filterConfig.operator).toBe('IN');
    });
  });

  describe('API integration scenarios', () => {
    it('should work with fetchAndSearchUsersByPersona API', () => {
      const apiPayload = {
        personas: SEARCH_USER_BY_ALL_PERSONAS,
        searchText: 'john',
        pageSize: 10,
        currentPage: 0,
      };

      expect(apiPayload.personas).toEqual(['all']);
      expect(typeof apiPayload.searchText).toBe('string');
      expect(typeof apiPayload.pageSize).toBe('number');
    });

    it('should work with user lookup resource parameters', () => {
      const resourceParams = {
        getOptionLabel: user => user.name,
        getOptionValue: user => user.id,
        getOptionData: user => user,
        personas: SEARCH_USER_BY_ALL_PERSONAS,
      };

      expect(resourceParams.personas).toEqual(['all']);
      expect(typeof resourceParams.getOptionLabel).toBe('function');
      expect(typeof resourceParams.getOptionValue).toBe('function');
    });
  });

  describe('User filtering and selection', () => {
    const mockUsers = [
      { id: '1', name: 'John Doe', persona: 'SALES', isActive: true },
      { id: '2', name: 'Jane Smith', persona: 'SERVICE', isActive: true },
      { id: '3', name: 'Bob Johnson', persona: 'MANAGER', isActive: false },
    ];

    it('should filter active users when using all personas', () => {
      const activeUsers = mockUsers.filter(user => user.isActive);
      const searchConfig = {
        personas: SEARCH_USER_BY_ALL_PERSONAS,
        users: activeUsers,
      };

      expect(searchConfig.personas).toEqual(['all']);
      expect(searchConfig.users).toHaveLength(2);
      expect(searchConfig.users.every(user => user.isActive)).toBe(true);
    });

    it('should support user selection across all personas', () => {
      const selectionConfig = {
        availablePersonas: SEARCH_USER_BY_ALL_PERSONAS,
        selectedUsers: mockUsers.slice(0, 2),
        allowMultiSelect: true,
      };

      expect(selectionConfig.availablePersonas).toEqual(['all']);
      expect(selectionConfig.selectedUsers).toHaveLength(2);
    });
  });

  describe('Error handling and edge cases', () => {
    it('should handle undefined personas gracefully', () => {
      const fallbackPersonas = undefined || SEARCH_USER_BY_ALL_PERSONAS;
      expect(fallbackPersonas).toEqual(['all']);
    });

    it('should handle null personas gracefully', () => {
      const fallbackPersonas = null || SEARCH_USER_BY_ALL_PERSONAS;
      expect(fallbackPersonas).toEqual(['all']);
    });

    it('should handle empty array personas gracefully', () => {
      const fallbackPersonas = EMPTY_ARRAY.length > 0 ? EMPTY_ARRAY : SEARCH_USER_BY_ALL_PERSONAS;
      expect(fallbackPersonas).toEqual(['all']);
    });
  });

  describe('Performance and optimization', () => {
    it('should maintain reference equality for constant', () => {
      const ref1 = SEARCH_USER_BY_ALL_PERSONAS;
      const ref2 = SEARCH_USER_BY_ALL_PERSONAS;
      expect(ref1).toBe(ref2);
    });

    it('should be suitable for memoization', () => {
      const memoizedConfig = {
        personas: SEARCH_USER_BY_ALL_PERSONAS,
        timestamp: Date.now(),
      };

      // Simulate memoization check
      const isSamePersonas = memoizedConfig.personas === SEARCH_USER_BY_ALL_PERSONAS;
      expect(isSamePersonas).toBe(true);
    });
  });
});
