import _isEmpty from 'lodash/isEmpty';
import _forEach from 'lodash/forEach';

import { tget } from '@tekion/tekion-base/utils/general';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import UserApi from '@tekion/tekion-base/services/usersService';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import getArraySafeValue from '@tekion/tekion-base/utils/getArraySafeValue';

import { WORKSPACE_TYPES } from '../../../constants/workspace/workspaceTypes';

import { fetchUserWorkspaceAccess } from '../../../services/user/enterpriseUserAccessService';

export const fetchUserAccessInfo = async () => {
  try {
    const response = await fetchUserWorkspaceAccess();
    return tget(response, 'workspaces', EMPTY_ARRAY);
  } catch {
    return EMPTY_ARRAY;
  }
};

const getAccessibleDealers = (userAccessDataList, parentDealerId, accessibleDealers) => {
  _forEach(userAccessDataList, userAccessData => {
    const workspaceId = tget(userAccessData, 'workspaceId');
    const parentWorkspaceId = tget(userAccessData, 'parentWorkspaceId');
    const userAccessChildList = tget(userAccessData, 'userAccess', EMPTY_ARRAY);
    if (workspaceId === parentDealerId) {
      accessibleDealers.push(parentDealerId);
      getAccessibleDealers(userAccessChildList, parentDealerId, accessibleDealers);
    } else if (parentWorkspaceId === parentDealerId) {
      accessibleDealers.push(workspaceId);
      getAccessibleDealers(userAccessChildList, workspaceId, accessibleDealers);
    } else {
      getAccessibleDealers(userAccessChildList, parentDealerId, accessibleDealers);
    }
  });
};

export const handleFetchUsersToTagInNotes =
  (isEnterpriseV2Enabled = false) =>
  async ({ personas, tekSearchAndAggregationRequest = EMPTY_OBJECT }) => {
    const filters = tget(tekSearchAndAggregationRequest, 'filters', []);
    if (isEnterpriseV2Enabled) {
      const userInfo = TEnvReader.userInfo();
      const workspaceType = tget(getArraySafeValue(tget(userInfo, 'workspaces', EMPTY_ARRAY)), 'workspaceType');
      const dealerId = userInfo?.dealerId;
      const userAccessDataList = await fetchUserAccessInfo();
      let accessibleDealers = [];

      if (workspaceType === WORKSPACE_TYPES.DEALER || _isEmpty(userAccessDataList)) {
        accessibleDealers = [dealerId];
      } else {
        getAccessibleDealers(userAccessDataList, dealerId, accessibleDealers);
      }

      filters.push({
        operator: 'BOOL',
        orFilters: [
          {
            field: 'employeeDealerMaps.dealerId',
            operator: 'IN',
            values: [...accessibleDealers],
          },
          {
            field: 'homeDealerId',
            operator: 'IN',
            values: [...accessibleDealers],
          },
        ],
      });
    }
    return UserApi.searchUserByPersona({
      personas,
      tekSearchAndAggregationRequest: { ...tekSearchAndAggregationRequest, filters },
    });
  };
