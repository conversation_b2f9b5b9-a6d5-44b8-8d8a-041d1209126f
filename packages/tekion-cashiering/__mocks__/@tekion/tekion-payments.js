// Mock for @tekion/tekion-payments package
// This mock provides all the exports that are used in tekion-cashiering

// Mock constants
const SUPPORTED_PAYMENT_METHODS = {
  CARD_BASED: 'CARD_BASED',
  LINK_BASED: 'LINK_BASED',
  TERMINAL_BASED: 'TERMINAL_BASED',
};

const SUPPORTED_CURRENCY = {
  USD: 'USD',
  CAD: 'CAD',
};

const PAYFAC_ERROR_CODES = {
  CARD_DECLINED: 'card_declined',
  INSUFFICIENT_FUNDS: 'insufficient_funds',
  INVALID_CARD: 'invalid_card',
};

const BILLING_INFO = {
  NAME: 'name',
  EMAIL: 'email',
  PHONE: 'phone',
  ADDRESS: 'address',
};

// Mock components
const OnlinePayments = jest.fn(() => null);
OnlinePayments.displayName = 'OnlinePayments';

const CardPayment = jest.fn(() => null);
CardPayment.displayName = 'CardPayment';

const PaymentRequestButton = jest.fn(() => null);
PaymentRequestButton.displayName = 'PaymentRequestButton';

const TerminalPayment = jest.fn(() => null);
TerminalPayment.displayName = 'TerminalPayment';

const TerminalGuide = jest.fn(() => null);
TerminalGuide.displayName = 'TerminalGuide';

const TokenizationPayment = jest.fn(() => null);
TokenizationPayment.displayName = 'TokenizationPayment';

// Mock HOCs
const withBootstrap = jest.fn(Component => {
  const WrappedComponent = props => Component(props);
  WrappedComponent.displayName = `withBootstrap(${Component.displayName || Component.name})`;
  return WrappedComponent;
});

// Mock services
const PaymentService = jest.fn().mockImplementation(() => ({
  stripe: {
    createPaymentMethod: jest.fn().mockResolvedValue({ paymentMethod: { id: 'pm_test' } }),
    confirmCardPayment: jest.fn().mockResolvedValue({ paymentIntent: { status: 'succeeded' } }),
    confirmPayment: jest.fn().mockResolvedValue({ paymentIntent: { status: 'succeeded' } }),
  },
  createPaymentIntent: jest.fn().mockResolvedValue({ client_secret: 'pi_test_client_secret' }),
  createPaymentMethod: jest.fn().mockResolvedValue({ paymentMethod: { id: 'pm_test' } }),
  confirmCardPayment: jest.fn().mockResolvedValue({ paymentIntent: { status: 'succeeded' } }),
  confirmPayment: jest.fn().mockResolvedValue({ paymentIntent: { status: 'succeeded' } }),
  capturePaymentWithTransactionId: jest.fn().mockResolvedValue({ success: true }),
  fetchCharge: jest.fn().mockResolvedValue({ id: 'ch_test' }),
  fetchSurchargeDetails: jest.fn().mockResolvedValue({ surcharge: 0 }),
  processTransactions: jest.fn().mockResolvedValue({ success: true }),
  getStatus: jest.fn().mockReturnValue(jest.fn().mockResolvedValue({ status: 'succeeded' })),
}));

const ReaderService = jest.fn().mockImplementation(() => ({
  discoverReaders: jest.fn().mockResolvedValue([]),
  connectReader: jest.fn().mockResolvedValue({ reader: { id: 'reader_test' } }),
  disconnectReader: jest.fn().mockResolvedValue({ success: true }),
  collectPayment: jest.fn().mockResolvedValue({ paymentIntent: { status: 'succeeded' } }),
  cancelCollectPayment: jest.fn().mockResolvedValue({ success: true }),
}));

const ACHService = jest.fn().mockImplementation(() => ({
  createACHPayment: jest.fn().mockResolvedValue({ success: true }),
  verifyACHPayment: jest.fn().mockResolvedValue({ verified: true }),
}));

// Mock readers
const SurchargeReader = {
  amount: jest.fn().mockReturnValue(0),
  percentage: jest.fn().mockReturnValue(0),
  isApplicable: jest.fn().mockReturnValue(false),
  getDetails: jest.fn().mockReturnValue({}),
};

// Mock theme function
const createCustomTheme = jest.fn().mockReturnValue({
  palette: {
    primary: { main: '#1976d2' },
    secondary: { main: '#dc004e' },
  },
});

// Default export
const defaultExport = OnlinePayments;

// Named exports
module.exports = {
  // Default export
  default: defaultExport,
  
  // Constants
  SUPPORTED_PAYMENT_METHODS,
  SUPPORTED_CURRENCY,
  PAYFAC_ERROR_CODES,
  BILLING_INFO,
  
  // Components
  OnlinePayments,
  CardPayment,
  PaymentRequestButton,
  TerminalPayment,
  TerminalGuide,
  TokenizationPayment,
  
  // HOCs
  withBootstrap,
  
  // Services
  PaymentService,
  ReaderService,
  ACHService,
  
  // Readers
  SurchargeReader,
  
  // Theme
  createCustomTheme,
};
