import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { compose, mapProps } from 'recompose';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';

import Modal from '@tekion/tekion-components/src/molecules/Modal';
import Loader from '@tekion/tekion-components/src/molecules/loader';

import { PropertyControlledComponent } from 'tcomponents/molecules';
import GMAdyenWidget from './GMAdyenWidget';
import { loadGMWalletScript } from './services/epgScriptLoader';
import { getEpgEnvironment } from './epg.helpers';
import { MODE_OF_PAYMENT_ACTION_TYPES } from '../../../modeOfPayment.actionTypes';
import styles from './epg.module.scss';

const MODAL_HEIGHT = '56vh';
const BODY_STYLE = {
  padding: 0,
  display: 'flex',
  minHeight: MODAL_HEIGHT,
  flexFlow: 'column nowrap',
  height: MODAL_HEIGHT,
};

function EPG(props) {
  const {
    currency,
    dealershipName,
    gatewayAuthToken,
    gatewayConfig,
    transactionMetadata,
    parentOnAction,
    paymentMode,
    value,
  } = props;

  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const [scriptLoadError, setScriptLoadError] = useState(null);
  const widgetRef = useRef(null);
  useEffect(() => {
    parentOnAction({ type: MODE_OF_PAYMENT_ACTION_TYPES.FETCH_GATEWAY_CONFIG_DATA });
  }, [parentOnAction]);

  useEffect(() => {
    loadGMWalletScript(getEpgEnvironment(gatewayConfig))
      .then(() => {
        setIsScriptLoaded(true);
        setScriptLoadError(null);
      })
      .catch(error => {
        setScriptLoadError(error);
        setIsScriptLoaded(false);
      });
  }, [gatewayConfig]);

  const handlePaymentSuccess = useCallback(
    data => {
      parentOnAction({ type: MODE_OF_PAYMENT_ACTION_TYPES.COLLECT_PAYMENT_FOR_GM_ADYEN_WIDGET });
    },
    [parentOnAction]
  );

  const handlePaymentError = useCallback(
    error => {
      parentOnAction({ type: MODE_OF_PAYMENT_ACTION_TYPES.CLOSE_EPG_PAYMENT_MODAL, payload: { error } });
    },
    [parentOnAction]
  );

  const handleOnCancel = useCallback(() => {
    parentOnAction({ type: MODE_OF_PAYMENT_ACTION_TYPES.CLOSE_EPG_PAYMENT_MODAL });
  }, [parentOnAction]);

  const widgetContent = useMemo(() => {
    if (scriptLoadError) {
      return (
        <div className={styles.errorContainer}>
          <p className={styles.errorMessage}>Payment widget is currently unavailable. Please try again later.</p>
        </div>
      );
    }

    if (!isScriptLoaded) {
      return (
        <div className={styles.loadingContainer}>
          <p className={styles.loadingMessage}>Loading payment options...</p>
        </div>
      );
    }

    return (
      <GMAdyenWidget
        ref={widgetRef}
        paymentMode={paymentMode}
        currency={currency}
        dealershipName={dealershipName}
        gatewayAuthToken={gatewayAuthToken}
        transactionMetadata={transactionMetadata}
        gatewayConfig={gatewayConfig}
        onSuccess={handlePaymentSuccess}
        onError={handlePaymentError}
      />
    );
  }, [
    scriptLoadError,
    isScriptLoaded,
    paymentMode,
    currency,
    dealershipName,
    gatewayAuthToken,
    transactionMetadata,
    gatewayConfig,
    handlePaymentSuccess,
    handlePaymentError,
  ]);

  if (_isEmpty(value)) return null;
  return (
    <Modal
      visible
      height={MODAL_HEIGHT}
      bodyStyle={BODY_STYLE}
      title={__('EPG Payment')}
      footer={null}
      onCancel={handleOnCancel}
      onSubmit={_noop}
      destroyOnClose
      maskClosable={false}
      centered>
      <PropertyControlledComponent controllerProperty={!value.isInitializing} fallback={<Loader />}>
        <div className={styles.epgContainer}>{widgetContent}</div>
      </PropertyControlledComponent>
    </Modal>
  );
}

EPG.propTypes = {
  currency: PropTypes.string,
  dealershipName: PropTypes.string,
  gatewayAuthToken: PropTypes.string,
  gatewayConfig: PropTypes.object.isRequired,
};

EPG.defaultProps = {
  currency: 'USD',
  dealershipName: '',
  gatewayAuthToken: '',
};

export default compose(mapProps(({ onAction, ...rest }) => ({ ...rest, parentOnAction: onAction })))(EPG);
