import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle, useCallback } from 'react';
import PropTypes from 'prop-types';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { isGMWalletScriptLoaded } from '../services/epgScriptLoader';
import { EPG_WIDGET_CONTAINER_ID, EPG_ERROR_MESSAGES } from '../epg.constants';
import { buildEPGConfig, validateEPGConfig } from '../services/epgConfigBuilder';
import styles from '../epg.module.scss';

const GMAdyenWidget = forwardRef((props, ref) => {
  const {
    paymentMode,
    currency,
    transactionMetadata,
    onSuccess,
    onError,
    onStatusChange,
    epgConfig: customEPGConfig,
    gatewayConfig,
  } = props;

  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const containerRef = useRef(null);
  const widgetInstanceRef = useRef(null);

  useImperativeHandle(ref, () => ({
    getWidget: () => widgetInstanceRef.current,
    reinitialize: () => initializeWidget(),
    destroy: () => destroyWidget(),
  }));

  const initializeWidget = useCallback(async () => {
    try {
      if (!isGMWalletScriptLoaded()) {
        throw new Error('GM Wallet script not loaded');
      }

      if (!containerRef.current) {
        return;
      }

      if (widgetInstanceRef.current) {
        destroyWidget();
      }

      const epgConfig = buildEPGConfig({
        customConfig: customEPGConfig,
        gatewayConfig,
        transactionMetadata,
        currency,
        containerId: EPG_WIDGET_CONTAINER_ID,
      });

      const validationResult = validateEPGConfig(epgConfig);
      if (!validationResult.isValid) {
        throw new Error(`${EPG_ERROR_MESSAGES.INVALID_CONFIG}: ${validationResult.errors.join(', ')}`);
      }

      const widgetConfig = {
        ...epgConfig,
        onSuccessCallback: handleSuccessCallback,
        onErrorCallback: handleErrorCallback,
        statusCallback: handleStatusCallback,
      };

      const gmWallet = new window.GMWallet(widgetConfig);

      widgetInstanceRef.current = gmWallet;
      setIsInitialized(true);
      setError(null);
    } catch (err) {
      setError(err.message);
      setIsInitialized(false);

      if (onError) {
        onError(err);
      }
    }
  }, [
    currency,
    gatewayConfig,
    transactionMetadata,
    customEPGConfig,
    onError,
    handleSuccessCallback,
    handleErrorCallback,
    handleStatusCallback,
  ]);

  const destroyWidget = () => {
    if (widgetInstanceRef.current) {
      widgetInstanceRef.current = null;
    }
    setIsInitialized(false);
  };

  const handleSuccessCallback = useCallback(
    data => {
      if (onSuccess) {
        onSuccess({
          ...data,
          paymentMode,
          widget: widgetInstanceRef.current,
        });
      }
    },
    [onSuccess, paymentMode]
  );

  const handleErrorCallback = useCallback(
    errorData => {
      if (onError) {
        onError({
          ...errorData,
          paymentMode,
          widget: widgetInstanceRef.current,
        });
      }
    },
    [onError, paymentMode]
  );

  const handleStatusCallback = useCallback(
    status => {
      if (onStatusChange) {
        onStatusChange({
          ...status,
          paymentMode,
          widget: widgetInstanceRef.current,
        });
      }
    },
    [onStatusChange, paymentMode]
  );

  useEffect(() => {
    const shouldInitialize = containerRef.current && isGMWalletScriptLoaded();
    if (shouldInitialize) {
      initializeWidget();
    }

    return () => {
      destroyWidget();
    };
  }, [paymentMode, initializeWidget]);

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <p className={styles.errorMessage}>{error}</p>
      </div>
    );
  }

  return (
    <div className={styles.widgetContainer}>
      <div id={EPG_WIDGET_CONTAINER_ID} ref={containerRef} className={styles.epgContainer} />
      {!isInitialized && (
        <div className={styles.loadingContainer}>
          <p className={styles.loadingMessage}>Initializing payment widget...</p>
        </div>
      )}
    </div>
  );
});

GMAdyenWidget.displayName = 'GMAdyenWidget';

GMAdyenWidget.propTypes = {
  paymentMode: PropTypes.string.isRequired,
  currency: PropTypes.string,
  transactionMetadata: PropTypes.object,
  onSuccess: PropTypes.func.isRequired,
  onError: PropTypes.func.isRequired,
  onStatusChange: PropTypes.func,
  epgConfig: PropTypes.object,
  gatewayConfig: PropTypes.object,
};

GMAdyenWidget.defaultProps = {
  currency: 'USD',
  transactionMetadata: EMPTY_OBJECT,
  onStatusChange: null,
  epgConfig: EMPTY_OBJECT,
  gatewayConfig: EMPTY_OBJECT,
};

export default GMAdyenWidget;
