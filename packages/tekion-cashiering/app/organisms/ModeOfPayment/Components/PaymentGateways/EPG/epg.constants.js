export const EPG_ENVIRONMENTS = {
  DEV: 'dev',
  PROD: 'prod',
};

export const EPG_SCRIPT_URLS = {
  [EPG_ENVIRONMENTS.DEV]:
    'https://wallet-ui-tst.musea2.azure.ext.gm.com/webapps/EPG_163080_WALLETUI/public/lib/v3/GMWalletUI.min.js',
  [EPG_ENVIRONMENTS.PROD]:
    'https://wallet-ui.musea2.azure.ext.gm.com/webapps/EPG_163080_WALLETUI/public/lib/v3/GMWalletUI.min.js',
};

export const EPG_WIDGET_CONTAINER_ID = 'epg-adyen-widget';

export const EPG_DEFAULT_CONFIG = {
  currency: 'USD',
  totalAmount: '0',
  epgEnvironment: EPG_ENVIRONMENTS.DEV,
  isCheckout: true,
  isAuthorized: false,
  isOneTimePayment: false,
  enableAutoCapture: false,
  billingAddressRequired: false,
  showPayButton: false,
  showErrorMessageBanners: true,
  useDebitOnly: true,
  disableThirdPartyPaymentMethods: false,
  enableWalletImportFeature: true,
  walletTypesBlackList: ['N/A'],
  adyenCheckoutButtonText: 'Confirm Payment',
  fetchCredentials: 'omit',
  enableAnalytics: true,
  headers: {},
};

export const EPG_ERROR_MESSAGES = {
  SCRIPT_LOAD_FAILED: 'Failed to load EPG payment widget',
  WIDGET_INIT_FAILED: 'Failed to initialize payment widget',
  PAYMENT_FAILED: 'Payment processing failed',
  INVALID_CONFIG: 'Invalid EPG configuration',
};

export const EPG_REQUIRED_CONFIG = [
  'containerId',
  'epgEnvironment',
  'appId',
  'platformName',
  'adyenConfigClientKey',
  'clientId',
  'walletRestService',
];
