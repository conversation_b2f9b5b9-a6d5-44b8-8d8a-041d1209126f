@import '~@tekion/tekion-styles-next/scss/component.scss';

.epgContainer {
  width: 100%;
  padding: 1rem;
}

.paymentModeContainer {
  margin-bottom: 2.4rem;
}

.widgetContainer {
  width: 100%;
  min-height: 40rem;
  position: relative;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 20rem;
  padding: 3.2rem;
}

.loadingMessage {
  margin-top: 1.6rem;
  text-align: center;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 20rem;
  padding: 3.2rem;
}

.errorMessage {
  text-align: center;
  margin-bottom: 1.6rem;
  font-weight: 500;
}

@media (max-width: 60rem) {
  .epgContainer {
    padding: 0.8rem;
  }

  .widgetContainer {
    min-height: 30rem;
  }

  :global {
    .adyen-checkout__button--pay {
      font-size: 1.4rem;
      padding: 1rem 1.6rem;
    }
  }
}
