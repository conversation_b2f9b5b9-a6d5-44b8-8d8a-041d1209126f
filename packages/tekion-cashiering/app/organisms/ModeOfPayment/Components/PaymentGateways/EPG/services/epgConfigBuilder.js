import _get from 'lodash/get';
import _isString from 'lodash/isString';
import _isNumber from 'lodash/isNumber';

import moneyUtils from '@tekion/tekion-base/utils/money';
import { getCdmsUrl, getDefaultHeader } from '@tekion/tekion-base/services/apiService/apiService.helper';

import { EPG_DEFAULT_CONFIG, EPG_REQUIRED_CONFIG, EPG_ENVIRONMENTS } from '../epg.constants';

export const buildEPGConfig = ({
  customConfig = {},
  gatewayConfig = {},
  transactionMetadata = {},
  currency = 'USD',
  containerId = 'epg-widget',
}) => {
  const orderId = _get(transactionMetadata, 'orderId');

  const walletRestService = constructWalletRestServiceUrl();

  const baseConfig = {
    ...EPG_DEFAULT_CONFIG,
    ...gatewayConfig,
    ...transactionMetadata,
    containerId,
    totalAmount: moneyUtils.withPrecision(_get(transactionMetadata, 'totalAmount')),
    currency: currency.toUpperCase(),
    orderId,
    walletRestService,
    headers: getDefaultHeader(),
  };

  if (_get(transactionMetadata, 'splits')) {
    baseConfig.splits = transactionMetadata.splits;
  }

  const finalConfig = {
    ...baseConfig,
    ...customConfig,
  };

  return finalConfig;
};

const constructWalletRestServiceUrl = () => {
  const baseUrl = getCdmsUrl();
  if (!baseUrl) {
    return 'cashier/u/epg';
  }

  const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`;
  return `${normalizedBaseUrl}cashier/u/epg`;
};

export const validateEPGConfig = config => {
  const errors = [];

  EPG_REQUIRED_CONFIG.forEach(field => {
    if (!_get(config, field)) {
      errors.push(`Missing required field: ${field}`);
    }
  });

  if (config.totalAmount && !_isString(config.totalAmount) && !_isNumber(config.totalAmount)) {
    errors.push('totalAmount must be a string or number');
  }

  if (config.currency && !_isString(config.currency)) {
    errors.push('currency must be a string');
  }

  if (config.epgEnvironment && !Object.values(EPG_ENVIRONMENTS).includes(config.epgEnvironment)) {
    errors.push(`Invalid epgEnvironment. Must be one of: ${Object.values(EPG_ENVIRONMENTS).join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
