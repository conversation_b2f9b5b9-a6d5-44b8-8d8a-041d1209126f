import { EPG_SCRIPT_URLS, EPG_ENVIRONMENTS, EPG_ERROR_MESSAGES } from '../epg.constants';

let scriptLoadPromise = null;
let isScriptLoaded = false;

export const getEPGScriptUrl = (environment = EPG_ENVIRONMENTS.DEV) =>
  EPG_SCRIPT_URLS[environment] || EPG_SCRIPT_URLS[EPG_ENVIRONMENTS.DEV];

export const isGMWalletScriptLoaded = () => isScriptLoaded && typeof window !== 'undefined' && window.GMWallet;

export const loadGMWalletScript = environment => {
  if (scriptLoadPromise) {
    return scriptLoadPromise;
  }

  if (isGMWalletScriptLoaded()) {
    return Promise.resolve();
  }

  scriptLoadPromise = new Promise((resolve, reject) => {
    try {
      if (typeof window === 'undefined' || typeof document === 'undefined') {
        reject(new Error('GM Wallet script can only be loaded in browser environment'));
        return;
      }

      const script = document.createElement('script');
      const scriptUrl = getEPGScriptUrl(environment);

      script.src = scriptUrl;
      script.async = true;
      script.type = 'text/javascript';

      script.onload = () => {
        if (window.GMWallet && typeof window.GMWallet === 'function') {
          isScriptLoaded = true;
          resolve();
        } else {
          const error = new Error('GMWallet not available after script load');
          reject(error);
        }
      };

      script.onerror = () => {
        const error = new Error(`${EPG_ERROR_MESSAGES.SCRIPT_LOAD_FAILED}: ${scriptUrl}`);
        reject(error);
      };

      const timeout = setTimeout(() => {
        const error = new Error('Script load timeout');
        reject(error);
      }, 10000);

      script.onload = () => {
        clearTimeout(timeout);
        if (window.GMWallet && typeof window.GMWallet === 'function') {
          isScriptLoaded = true;
          resolve();
        } else {
          const error = new Error('GMWallet not available after script load');
          reject(error);
        }
      };

      document.head.appendChild(script);
    } catch (error) {
      reject(error);
    }
  });

  scriptLoadPromise.catch(() => {
    scriptLoadPromise = null;
    isScriptLoaded = false;
  });

  return scriptLoadPromise;
};
