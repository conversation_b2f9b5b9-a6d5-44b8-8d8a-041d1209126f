import { getObjectWithOverriddenValues } from '@tekion/testing-utilities';
import { getConversationUnreadCount, getConversationSentiment } from '../communication.converationInfo';

const MOCK_CONVERSATION = {
  id: '123',
  unreadCount: 3,
  sentiment: 'positive',
};

const MOCK_CONVERSATION_ADDITIONAL_INFO = {
  unreadCount: 5,
  sentiment: 'negative',
  latestMessage: 'Hello there',
  customerName: '<PERSON>',
  siteId: 'site123',
};

describe('getConversationUnreadCount', () => {
  // Test cases for non-enterprise communication (when conversationAdditionalInfo is empty)
  it('should return conversation reader unread count when conversationAdditionalInfo is empty object', () => {
    const result = getConversationUnreadCount({}, MOCK_CONVERSATION);
    expect(result).toBe(3);
  });

  it('should return conversation reader unread count when conversationAdditionalInfo is null', () => {
    const mockConversation = getObjectWithOverriddenValues(MOCK_CONVERSATION, { unreadCount: 5 });
    const result = getConversationUnreadCount(null, mockConversation);
    expect(result).toBe(5);
  });

  it('should return conversation reader unread count when conversationAdditionalInfo is undefined', () => {
    const mockConversation = getObjectWithOverriddenValues(MOCK_CONVERSATION, { unreadCount: 2 });
    const result = getConversationUnreadCount(undefined, mockConversation);
    expect(result).toBe(2);
  });

  it('should return conversation reader unread count when conversationAdditionalInfo is empty array', () => {
    const mockConversation = getObjectWithOverriddenValues(MOCK_CONVERSATION, { unreadCount: 1 });
    const result = getConversationUnreadCount([], mockConversation);
    expect(result).toBe(1);
  });

  // Test cases for enterprise communication (when conversationAdditionalInfo has data)
  it('should return conversationAdditionalInfo unread count when enterprise data is available', () => {
    const result = getConversationUnreadCount(MOCK_CONVERSATION_ADDITIONAL_INFO, MOCK_CONVERSATION);
    expect(result).toBe(5);
  });

  it('should return conversationAdditionalInfo unread count when it has different value than conversation', () => {
    const enterpriseInfo = getObjectWithOverriddenValues(MOCK_CONVERSATION_ADDITIONAL_INFO, { unreadCount: 10 });
    const result = getConversationUnreadCount(enterpriseInfo, MOCK_CONVERSATION);
    expect(result).toBe(10);
  });

  it('should return default unread count when conversationAdditionalInfo has data but no unread count property', () => {
    const enterpriseInfoWithoutUnreadCount = getObjectWithOverriddenValues(MOCK_CONVERSATION_ADDITIONAL_INFO, {
      unreadCount: undefined,
    });
    const result = getConversationUnreadCount(enterpriseInfoWithoutUnreadCount, MOCK_CONVERSATION);
    expect(result).toBe(0); // DEFAULT_UNREAD_COUNT
  });

  it('should return default unread count when conversationAdditionalInfo unread count is zero', () => {
    const enterpriseInfoWithZeroCount = getObjectWithOverriddenValues(MOCK_CONVERSATION_ADDITIONAL_INFO, {
      unreadCount: 0,
    });
    const result = getConversationUnreadCount(enterpriseInfoWithZeroCount, MOCK_CONVERSATION);
    expect(result).toBe(0);
  });

  it('should return default unread count when conversationAdditionalInfo unread count is null', () => {
    const enterpriseInfoWithNullCount = getObjectWithOverriddenValues(MOCK_CONVERSATION_ADDITIONAL_INFO, {
      unreadCount: null,
    });
    const result = getConversationUnreadCount(enterpriseInfoWithNullCount, MOCK_CONVERSATION);
    expect(result).toBe(0); // DEFAULT_UNREAD_COUNT
  });
});

describe('getConversationSentiment', () => {
  // Test cases for non-enterprise communication (when conversationAdditionalInfo is empty)
  it('should return conversation reader sentiment when conversationAdditionalInfo is empty object', () => {
    const result = getConversationSentiment({}, MOCK_CONVERSATION);
    expect(result).toBe('positive');
  });

  it('should return conversation reader sentiment when conversationAdditionalInfo is null', () => {
    const mockConversation = getObjectWithOverriddenValues(MOCK_CONVERSATION, { sentiment: 'negative' });
    const result = getConversationSentiment(null, mockConversation);
    expect(result).toBe('negative');
  });

  it('should return conversation reader sentiment when conversationAdditionalInfo is undefined', () => {
    const mockConversation = getObjectWithOverriddenValues(MOCK_CONVERSATION, { sentiment: 'neutral' });
    const result = getConversationSentiment(undefined, mockConversation);
    expect(result).toBe('neutral');
  });

  it('should return conversation reader sentiment when conversationAdditionalInfo is empty array', () => {
    const result = getConversationSentiment([], MOCK_CONVERSATION);
    expect(result).toBe('positive');
  });

  // Test cases for enterprise communication (sentiment is hidden for enterprise)
  it('should return undefined for enterprise when conversationAdditionalInfo has unread count data', () => {
    const result = getConversationSentiment(MOCK_CONVERSATION_ADDITIONAL_INFO, MOCK_CONVERSATION);
    expect(result).toBeUndefined(); // Enterprise communication hides sentiment
  });

  it('should return undefined for enterprise when conversationAdditionalInfo has sentiment data', () => {
    const enterpriseInfoWithSentiment = getObjectWithOverriddenValues(MOCK_CONVERSATION_ADDITIONAL_INFO, {
      sentiment: 'positive',
    });
    const result = getConversationSentiment(enterpriseInfoWithSentiment, MOCK_CONVERSATION);
    expect(result).toBeUndefined(); // Enterprise communication hides sentiment
  });

  it('should return undefined for enterprise when conversationAdditionalInfo has any data', () => {
    const enterpriseInfoWithOtherData = { someOtherData: 'value', customField: 'test' };
    const result = getConversationSentiment(enterpriseInfoWithOtherData, MOCK_CONVERSATION);
    expect(result).toBeUndefined(); // Enterprise communication hides sentiment
  });
});
