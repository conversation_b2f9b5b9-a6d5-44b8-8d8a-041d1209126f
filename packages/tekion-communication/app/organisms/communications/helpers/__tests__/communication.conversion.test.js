import { formatPhoneNumber } from '../communication.conversion';

// Mock the lookup reader
jest.mock('tbase/readers/lookup', () => ({
  __esModule: true,
  default: {
    id: jest.fn(lookup => lookup.id),
    name: jest.fn(lookup => lookup.name),
  },
}));

// Mock the bulk resolvers
jest.mock('tbase/bulkResolvers', () => ({
  __esModule: true,
  default: {
    resolve: jest.fn(() => Promise.resolve({})),
  },
}));

// Mock the CRM helpers
jest.mock('twidgets/appServices/crm/helpers/common', () => ({
  getCustomerFromConversation: jest.fn(() => ({})),
}));

describe('formatPhoneNumber', () => {
  it('should return undefined when phone number is empty string', () => {
    const mockFormatter = jest.fn();
    expect(formatPhoneNumber(mockFormatter, '')).toBeUndefined();
  });

  it('should return undefined when phone number is null', () => {
    const mockFormatter = jest.fn();
    expect(formatPhoneNumber(mockFormatter, null)).toBeUndefined();
  });

  it('should return undefined when phone number is undefined', () => {
    const mockFormatter = jest.fn();
    expect(formatPhoneNumber(mockFormatter, undefined)).toBeUndefined();
  });

  it('should be a function', () => {
    expect(typeof formatPhoneNumber).toBe('function');
  });
});
