import WidgetEventEmitter, {
  WIDGET_TYPE,
  WIDGET_EVENTS,
} from '@tekion/tekion-components/src/emitters/WidgetEventEmitter';
import { sendActionToWidgetPanel } from '../communications.chatWidget';

jest.mock('@tekion/tekion-components/src/emitters/WidgetEventEmitter');

describe('sendActionToWidgetPanel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should emit widget event with string action', () => {
    const action = 'OPEN_CHAT';

    sendActionToWidgetPanel(action);

    expect(WidgetEventEmitter.emit).toHaveBeenCalledTimes(1);
    expect(WidgetEventEmitter.emit).toHaveBeenCalledWith(WIDGET_EVENTS, {
      type: WIDGET_TYPE.CHAT_WIDGET,
      action: 'OPEN_CHAT',
    });
  });

  it('should emit widget event with object action', () => {
    const action = { type: 'OPEN_CHAT', payload: { chatId: '123' } };

    sendActionToWidgetPanel(action);

    expect(WidgetEventEmitter.emit).toHaveBeenCalledTimes(1);
    expect(WidgetEventEmitter.emit).toHaveBeenCalledWith(WIDGET_EVENTS, {
      type: WIDGET_TYPE.CHAT_WIDGET,
      action: { type: 'OPEN_CHAT', payload: { chatId: '123' } },
    });
  });

  it('should emit widget event with null action', () => {
    const action = null;

    sendActionToWidgetPanel(action);

    expect(WidgetEventEmitter.emit).toHaveBeenCalledTimes(1);
    expect(WidgetEventEmitter.emit).toHaveBeenCalledWith(WIDGET_EVENTS, {
      type: WIDGET_TYPE.CHAT_WIDGET,
      action: null,
    });
  });

  it('should emit widget event with undefined action', () => {
    const action = undefined;

    sendActionToWidgetPanel(action);

    expect(WidgetEventEmitter.emit).toHaveBeenCalledTimes(1);
    expect(WidgetEventEmitter.emit).toHaveBeenCalledWith(WIDGET_EVENTS, {
      type: WIDGET_TYPE.CHAT_WIDGET,
      action: undefined,
    });
  });

  it('should emit widget event with number action', () => {
    const action = 123;

    sendActionToWidgetPanel(action);

    expect(WidgetEventEmitter.emit).toHaveBeenCalledTimes(1);
    expect(WidgetEventEmitter.emit).toHaveBeenCalledWith(WIDGET_EVENTS, {
      type: WIDGET_TYPE.CHAT_WIDGET,
      action: 123,
    });
  });

  it('should emit widget event with boolean action', () => {
    const action = true;

    sendActionToWidgetPanel(action);

    expect(WidgetEventEmitter.emit).toHaveBeenCalledTimes(1);
    expect(WidgetEventEmitter.emit).toHaveBeenCalledWith(WIDGET_EVENTS, {
      type: WIDGET_TYPE.CHAT_WIDGET,
      action: true,
    });
  });

  it('should emit widget event with array action', () => {
    const action = ['OPEN_CHAT', 'CLOSE_CHAT'];

    sendActionToWidgetPanel(action);

    expect(WidgetEventEmitter.emit).toHaveBeenCalledTimes(1);
    expect(WidgetEventEmitter.emit).toHaveBeenCalledWith(WIDGET_EVENTS, {
      type: WIDGET_TYPE.CHAT_WIDGET,
      action: ['OPEN_CHAT', 'CLOSE_CHAT'],
    });
  });
});
