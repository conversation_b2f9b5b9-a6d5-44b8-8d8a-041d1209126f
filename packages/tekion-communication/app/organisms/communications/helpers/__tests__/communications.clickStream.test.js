import { trackPageView, trackClick } from '../communications.clickStream';

// Mock the Clickstream module
jest.mock('@tekion/tekion-ui-eventing/src/clickstream', () => ({
  __esModule: true,
  default: {
    trackPageView: jest.fn(),
    trackClick: jest.fn(),
  },
}));

// Mock the helper modules
jest.mock('@tekion/tekion-widgets/src/helpers/clickStream', () => ({
  getBaseParams: jest.fn(() => ({
    dealerId: 'dealer123',
    dealerName: 'Test Dealer',
    tenantName: 'test-tenant',
    userRole: 'admin',
  })),
}));

jest.mock('@tekion/tekion-base/readers/Env', () => ({
  userInfo: jest.fn(() => ({ id: 'user123', name: 'Test User' })),
}));

jest.mock('@tekion/tekion-base/readers/User', () => ({
  id: jest.fn(user => user.id),
}));

// Mock the constants
jest.mock('@tekion/tekion-widgets/src/constants/clickStream', () => ({
  CLICK_STREAM_BASE_PAYLOAD_KEYS: {
    DEALER_ID: 'dealerId',
    DEALER_NAME: 'dealerName',
    TENANT_NAME: 'tenantName',
    USER_ROLE: 'userRole',
    GROUP: 'group',
    USER_ID: 'userId',
  },
}));

describe('trackPageView', () => {
  let mockClickstream;

  beforeEach(() => {
    jest.clearAllMocks();
    mockClickstream = require('@tekion/tekion-ui-eventing/src/clickstream').default;
  });

  it('should not call Clickstream.trackPageView when dealer property is false', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(false);
    const params = { page: 'communication-home' };

    trackPageView({ params, getDealerPropertyValue: mockGetDealerPropertyValue });

    expect(mockClickstream.trackPageView).not.toHaveBeenCalled();
    expect(mockGetDealerPropertyValue).toHaveBeenCalledWith('COMMUNICATION_ANALYTICS_METRICS');
  });

  it('should not call Clickstream.trackPageView when dealer property is undefined', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(undefined);
    const params = { page: 'communication-home' };

    trackPageView({ params, getDealerPropertyValue: mockGetDealerPropertyValue });

    expect(mockClickstream.trackPageView).not.toHaveBeenCalled();
    expect(mockGetDealerPropertyValue).toHaveBeenCalledWith('COMMUNICATION_ANALYTICS_METRICS');
  });

  it('should not call Clickstream.trackPageView when dealer property is null', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(null);
    const params = { page: 'communication-home' };

    trackPageView({ params, getDealerPropertyValue: mockGetDealerPropertyValue });

    expect(mockClickstream.trackPageView).not.toHaveBeenCalled();
    expect(mockGetDealerPropertyValue).toHaveBeenCalledWith('COMMUNICATION_ANALYTICS_METRICS');
  });

  it('should call Clickstream.trackPageView when dealer property is true', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(true);
    const params = { page: 'communication-home' };

    trackPageView({ params, getDealerPropertyValue: mockGetDealerPropertyValue });

    expect(mockClickstream.trackPageView).toHaveBeenCalledTimes(1);
    expect(mockGetDealerPropertyValue).toHaveBeenCalledWith('COMMUNICATION_ANALYTICS_METRICS');
  });
});

describe('trackClick', () => {
  let mockClickstream;

  beforeEach(() => {
    jest.clearAllMocks();
    mockClickstream = require('@tekion/tekion-ui-eventing/src/clickstream').default;
  });

  it('should not call Clickstream.trackClick when dealer property is false', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(false);
    const params = { element: 'send-button' };

    trackClick({ params, getDealerPropertyValue: mockGetDealerPropertyValue });

    expect(mockClickstream.trackClick).not.toHaveBeenCalled();
    expect(mockGetDealerPropertyValue).toHaveBeenCalledWith('COMMUNICATION_ANALYTICS_METRICS');
  });

  it('should not call Clickstream.trackClick when dealer property is undefined', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(undefined);
    const params = { element: 'send-button' };

    trackClick({ params, getDealerPropertyValue: mockGetDealerPropertyValue });

    expect(mockClickstream.trackClick).not.toHaveBeenCalled();
    expect(mockGetDealerPropertyValue).toHaveBeenCalledWith('COMMUNICATION_ANALYTICS_METRICS');
  });

  it('should not call Clickstream.trackClick when dealer property is null', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(null);
    const params = { element: 'send-button' };

    trackClick({ params, getDealerPropertyValue: mockGetDealerPropertyValue });

    expect(mockClickstream.trackClick).not.toHaveBeenCalled();
    expect(mockGetDealerPropertyValue).toHaveBeenCalledWith('COMMUNICATION_ANALYTICS_METRICS');
  });

  it('should call Clickstream.trackClick when dealer property is true', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(true);
    const params = { element: 'send-button' };

    trackClick({ params, getDealerPropertyValue: mockGetDealerPropertyValue });

    expect(mockClickstream.trackClick).toHaveBeenCalledTimes(1);
    expect(mockGetDealerPropertyValue).toHaveBeenCalledWith('COMMUNICATION_ANALYTICS_METRICS');
  });
});
