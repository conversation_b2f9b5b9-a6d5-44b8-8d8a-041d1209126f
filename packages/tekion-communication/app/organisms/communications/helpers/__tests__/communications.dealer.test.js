import {
  centralCommunicationOptInPreferenceEnabled,
  getDealerNameFromDealerInfo,
  getUserRole,
  getUserDetailsWithRoleAndDealershipName,
} from '../communications.dealer';

// Mock the base constants
jest.mock('tbase/app.constants', () => ({
  EMPTY_ARRAY: [],
  EMPTY_OBJECT: {},
}));

// Mock the role helper
jest.mock('@tekion/tekion-base/helpers/role', () => ({
  hasPermission: jest.fn(() => true),
}));

describe('centralCommunicationOptInPreferenceEnabled', () => {
  it('should return true when dealer property is enabled', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(true);
    const result = centralCommunicationOptInPreferenceEnabled(mockGetDealerPropertyValue);
    expect(result).toBe(true);
  });

  it('should return false when dealer property is disabled', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(false);
    const result = centralCommunicationOptInPreferenceEnabled(mockGetDealerPropertyValue);
    expect(result).toBe(false);
  });

  it('should return undefined when dealer property is undefined', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(undefined);
    const result = centralCommunicationOptInPreferenceEnabled(mockGetDealerPropertyValue);
    expect(result).toBeUndefined();
  });

  it('should return null when dealer property is null', () => {
    const mockGetDealerPropertyValue = jest.fn().mockReturnValue(null);
    const result = centralCommunicationOptInPreferenceEnabled(mockGetDealerPropertyValue);
    expect(result).toBeNull();
  });
});

describe('getDealerNameFromDealerInfo', () => {
  it('should return dealer display name when dealer exists in map', () => {
    const dealerInfoMap = {
      dealer1: { displayName: 'ABC Motors' },
      dealer2: { displayName: 'XYZ Auto' },
    };
    const result = getDealerNameFromDealerInfo(dealerInfoMap, 'dealer1');
    expect(result).toBe('ABC Motors');
  });

  it('should return empty string when dealer does not exist in map', () => {
    const dealerInfoMap = {
      dealer1: { displayName: 'ABC Motors' },
    };
    const result = getDealerNameFromDealerInfo(dealerInfoMap, 'dealer2');
    expect(result).toBe('');
  });

  it('should return empty string when dealerInfoMap is empty', () => {
    const dealerInfoMap = {};
    const result = getDealerNameFromDealerInfo(dealerInfoMap, 'dealer1');
    expect(result).toBe('');
  });

  it('should return empty string when dealerInfoMap is null', () => {
    const result = getDealerNameFromDealerInfo(null, 'dealer1');
    expect(result).toBe('');
  });

  it('should return empty string when dealerInfoMap is undefined', () => {
    const result = getDealerNameFromDealerInfo(undefined, 'dealer1');
    expect(result).toBe('');
  });

  it('should return empty string when dealerId is null', () => {
    const dealerInfoMap = {
      dealer1: { displayName: 'ABC Motors' },
    };
    const result = getDealerNameFromDealerInfo(dealerInfoMap, null);
    expect(result).toBe('');
  });

  it('should return empty string when dealerId is undefined', () => {
    const dealerInfoMap = {
      dealer1: { displayName: 'ABC Motors' },
    };
    const result = getDealerNameFromDealerInfo(dealerInfoMap, undefined);
    expect(result).toBe('');
  });

  it('should return empty string when dealer exists but has no displayName', () => {
    const dealerInfoMap = {
      dealer1: { name: 'ABC Motors' },
    };
    const result = getDealerNameFromDealerInfo(dealerInfoMap, 'dealer1');
    expect(result).toBe('');
  });
});

describe('getUserRole', () => {
  it('should be a function', () => {
    expect(typeof getUserRole).toBe('function');
  });
});

describe('getUserDetailsWithRoleAndDealershipName', () => {
  it('should be a function', () => {
    expect(typeof getUserDetailsWithRoleAndDealershipName).toBe('function');
  });
});
