import _get from 'lodash/get';
import _filter from 'lodash/filter';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

const isExternalCommunicationEnabledDealer =
  (dealerConfigurations = EMPTY_OBJECT) =>
  (dealer = EMPTY_OBJECT) => {
    const { workspaceId } = dealer;
    const isExternalCommunicationEnabled = _get(
      dealerConfigurations,
      `${workspaceId}.meta.externalCommunicationEnabled`
    );
    return isExternalCommunicationEnabled;
  };

export const getExternalCommunicationEnabledDealers = (dealers, displaySetting = EMPTY_OBJECT) => {
  const { dealerConfigurations } = displaySetting;
  const externalCommunicationEnabledDealers = _filter(
    dealers,
    isExternalCommunicationEnabledDealer(dealerConfigurations)
  );
  return externalCommunicationEnabledDealers;
};
