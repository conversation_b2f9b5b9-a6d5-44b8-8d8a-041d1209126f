import { showRealtimeCallProgress } from '@tekion/tekion-business/src/helpers/communications/callProvider';

import { MESSAGE_STATUS } from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.messages';

export const shouldShowCallDuration = ({ minimized, isCallCommunicationOn, currentCallStatus, providerType }) =>
  minimized &&
  isCallCommunicationOn &&
  currentCallStatus === MESSAGE_STATUS.IN_PROGRESS &&
  showRealtimeCallProgress(providerType);
