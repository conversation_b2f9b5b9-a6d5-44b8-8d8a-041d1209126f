import React from 'react';
import PropTypes from 'prop-types';

import CommunicationComplianceBanner from '@tekion/tekion-widgets/src/appServices/communication/molecules/communicationComplianceBanner/CommunicationComplianceBanner';
import useCommunicationComplianceBanner from '@tekion/tekion-widgets/src/appServices/communication/hooks/useCommunicationComplianceBanner/useCommunicationComplianceBanner';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import styles from './banner.module.scss';

const Banner = props => {
  const { additional } = props;
  const { dealerId, siteId } = additional;
  const { isVisible, reminders, closeBanner } = useCommunicationComplianceBanner(dealerId, siteId);

  return (
    <CommunicationComplianceBanner
      containerClassname={styles.complianceBannerClassName}
      isVisible={isVisible}
      reminders={reminders}
      closeBanner={closeBanner}
    />
  );
};

Banner.propTypes = {
  additional: PropTypes.shape({
    dealerId: PropTypes.string,
    siteId: PropTypes.string,
  }),
};

Banner.defaultProps = {
  additional: EMPTY_OBJECT,
};

export default React.memo(Banner);
