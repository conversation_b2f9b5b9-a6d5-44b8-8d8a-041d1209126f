import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import loginService from '@tekion/tekion-base/services/loginService';
import { tget } from '@tekion/tekion-base/utils/general';

export const fetchIdentityProviderData = async () => {
  try {
    const response = await loginService.identityProviderDealer();
    const identityProviderData = tget(response, 'data', EMPTY_ARRAY);
    return identityProviderData;
  } catch {
    return EMPTY_OBJECT;
  }
};
