jest.mock('@tekion/tekion-base/readers/User');
jest.mock('../../../helpers/user.helpers', () => ({
  getWarningLabelForDeactivatingUser: jest.fn(() => 'custom warning'),
}));

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { sendDataForExportAction, getExportDetailsByIdAction } from 'actions/exportActions';
import { ARC_USER_TYPES } from '@tekion/tekion-base/constants/users';
import userReader from '@tekion/tekion-base/readers/User';
import { handleDownloadExcel, __test__ } from '../userList.actionHandlers';
import { USER_ONBOARDING_FILE_NAME } from '../userList.constants';
import { POP_UP_TYPES } from '../../popUpComponent';
import { MODAL_TYPES } from '../../rowActionModal/rowActionModal.constants';
import userListStyles from '../userList.module.scss';
import { getWarningLabelForDeactivatingUser } from '../../../helpers/user.helpers';

const { handleStatusUpdate } = __test__;

jest.mock('../userList.general.js', () => ({
  getColumnDefinitions: jest.fn(),
  createUserTypeFilter: jest.fn(),
  getFiltersWithKey: jest.fn(),
  createPayload: jest.fn(),
}));

jest.mock('../../../../../actions/exportActions', () => ({
  sendDataForExportAction: jest.fn(),
  getExportDetailsByIdAction: jest.fn(),
}));

jest.mock('@tekion/tekion-components/src/organisms/NotificationWrapper', () => {
  const actualToaster = jest.requireActual('@tekion/tekion-components/src/organisms/NotificationWrapper');
  return {
    ...actualToaster,
    toaster: jest.fn(),
  };
});

describe('handleDownloadExcel', () => {
  let getStateMock;
  let dispatchMock;

  beforeEach(() => {
    getStateMock = () => ({
      searchQuery: 'John Doe',
      selectedFilters: [],
      sortDetails: { name: 'asc' },
      preFilter: [],
      filters: [],
      columns: ['name', 'email'],
      userType: 'admin',
    });

    dispatchMock = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should generate correct payload and call sendDataForExportAction', async () => {
    sendDataForExportAction.mockResolvedValue('1234');
    getExportDetailsByIdAction.mockResolvedValue();

    await handleDownloadExcel(dispatchMock, { getState: getStateMock });

    expect(sendDataForExportAction).toHaveBeenCalled();

    expect(getExportDetailsByIdAction).toHaveBeenCalledWith('1234', USER_ONBOARDING_FILE_NAME);
  });

  it('should not call getExportDetailsByIdAction if no exportId is returned', async () => {
    sendDataForExportAction.mockResolvedValue(null);

    await handleDownloadExcel(dispatchMock, { getState: getStateMock });

    expect(sendDataForExportAction).toHaveBeenCalled();
    expect(getExportDetailsByIdAction).not.toHaveBeenCalled();
  });

  it('should display a toaster notification when generating Excel sheet', async () => {
    sendDataForExportAction.mockResolvedValue('1234');

    await handleDownloadExcel(dispatchMock, { getState: getStateMock });

    expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.INFO, __('Please Wait! Generating Excel Sheet'));
  });
});

describe('handleStatusUpdate', () => {
  const setState = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should set popUpValues with customWarning for non-internal user', () => {
    userReader.id.mockReturnValue('user1');
    userReader.type.mockReturnValue(2);
    userReader.accessibleDealershipsCount.mockReturnValue(2);
    userReader.employeeDealerMaps.mockReturnValue([{}, {}]);

    const listItem = {};
    handleStatusUpdate({ payload: listItem }, { setState });

    expect(setState).toHaveBeenCalledWith({
      popUpValues: {
        type: POP_UP_TYPES.MODAL,
        subType: MODAL_TYPES.INACTIVE,
        values: {
          userId: 'user1',
          customWarning: getWarningLabelForDeactivatingUser(2, 2),
          messageLabelStyle: userListStyles.messageLabelStyle,
        },
        userType: 2,
      },
    });
    expect(getWarningLabelForDeactivatingUser).toHaveBeenCalledWith(2, 2);
  });

  it('should set popUpValues with undefined customWarning for internal user', () => {
    userReader.id.mockReturnValue('user2');
    userReader.type.mockReturnValue(ARC_USER_TYPES.INTERNAL_USER);
    userReader.accessibleDealershipsCount.mockReturnValue(1);
    userReader.employeeDealerMaps.mockReturnValue([{}]);

    const listItem = {};
    handleStatusUpdate({ payload: listItem }, { setState });

    expect(setState).toHaveBeenCalledWith({
      popUpValues: {
        type: POP_UP_TYPES.MODAL,
        subType: MODAL_TYPES.INACTIVE,
        values: {
          userId: 'user2',
          customWarning: undefined,
          messageLabelStyle: userListStyles.messageLabelStyle,
        },
        userType: ARC_USER_TYPES.INTERNAL_USER,
      },
    });
    expect(getWarningLabelForDeactivatingUser).not.toHaveBeenCalled();
  });

  it('should use correct values from userReader', () => {
    userReader.id.mockReturnValue('user3');
    userReader.type.mockReturnValue(2);
    userReader.accessibleDealershipsCount.mockReturnValue(5);
    userReader.employeeDealerMaps.mockReturnValue([{}, {}, {}, {}]);

    const listItem = {};
    handleStatusUpdate({ payload: listItem }, { setState });

    expect(setState).toHaveBeenCalledWith(
      expect.objectContaining({
        popUpValues: expect.objectContaining({
          values: expect.objectContaining({
            userId: 'user3',
          }),
        }),
      })
    );
    expect(getWarningLabelForDeactivatingUser).toHaveBeenCalledWith(5, 4);
  });
});
