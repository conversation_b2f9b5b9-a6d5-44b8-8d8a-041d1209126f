import { getTeamsTableValues } from '../TeamsTable.helpers';

describe('getTeamsTableValues', () => {
  const mockTeams = [
    {
      teamId: 'team-1',
      name: 'Service Team Alpha',
      type: 'Dynamic',
      createdByUserName: '<PERSON>',
      modifiedTime: 1640995200000,
      memberCount: 5,
      department: 'service',
    },
    {
      teamId: 'team-2',
      name: 'Sales Team Beta',
      type: 'Static',
      createdByUserName: '<PERSON>',
      modifiedTime: 1641081600000,
      memberCount: 8,
      department: 'sales',
    },
  ];

  it('should return formatted teams with empty search', () => {
    const result = getTeamsTableValues(mockTeams, '');

    expect(result).toHaveLength(2);
    expect(result[0]).toEqual({
      id: 'team-1',
      teamType: 'Dynamic',
      teamName: 'Service Team Alpha',
      teamCreatedBy: '<PERSON>',
      lastModification: '05:30 AM | Jan 01, 2022',
      membersCount: 5,
      teamsDepartment: 'service',
    });
  });

  it('should filter teams by name case-insensitively', () => {
    const result = getTeamsTableValues(mockTeams, 'SERVICE');

    expect(result).toHaveLength(1);
    expect(result[0].teamName).toBe('Service Team Alpha');
  });

  it('should return empty array when no match', () => {
    const result = getTeamsTableValues(mockTeams, 'nonexistent');

    expect(result).toEqual([]);
  });
});
