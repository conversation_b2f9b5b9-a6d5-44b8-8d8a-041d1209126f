import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { getDealerPrimaryAddress, getDealerStateCode, isStateCA } from '../payroll.helpers';

describe('payroll.helpers', () => {
  describe('getDealerPrimaryAddress', () => {
    it('should return first dealer address', () => {
      const dealerInfo = {
        dealerAddress: [
          { state: 'CA', city: 'Los Angeles' },
          { state: 'NY', city: 'New York' },
        ],
      };
      const result = getDealerPrimaryAddress(dealerInfo);
      expect(result).toEqual({ state: 'CA', city: 'Los Angeles' });
    });

    it('should handle empty dealer address', () => {
      const dealerInfo = { dealerAddress: EMPTY_ARRAY };
      const result = getDealerPrimaryAddress(dealerInfo);
      expect(result).toBeUndefined();
    });
  });

  describe('getDealerStateCode', () => {
    it('should return state code from dealer primary address', () => {
      const dealerInfo = {
        dealerAddress: [{ state: 'CA', city: 'Los Angeles' }],
      };
      const result = getDealerStateCode(dealerInfo);
      expect(result).toBe('CA');
    });

    it('should handle dealer with no address', () => {
      const dealerInfo = { dealerAddress: EMPTY_ARRAY };
      const result = getDealerStateCode(dealerInfo);
      expect(result).toBeUndefined();
    });
  });

  describe('isStateCA', () => {
    it('should return true when dealerState is CA', () => {
      const result = isStateCA('CA', 'NY');
      expect(result).toBe(true);
    });

    it('should return true when taxationAddressState is CA', () => {
      const result = isStateCA('NY', 'CA');
      expect(result).toBe(true);
    });

    it('should return true when both states are CA', () => {
      const result = isStateCA('CA', 'CA');
      expect(result).toBe(true);
    });

    it('should return false when neither state is CA', () => {
      const result = isStateCA('NY', 'TX');
      expect(result).toBe(false);
    });

    it('should return false when both parameters are null', () => {
      const result = isStateCA(null, null);
      expect(result).toBe(false);
    });
  });
});
