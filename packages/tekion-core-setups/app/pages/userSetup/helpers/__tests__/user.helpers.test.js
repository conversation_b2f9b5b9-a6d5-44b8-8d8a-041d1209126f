import { EMPTY_OBJECT } from 'tbase/app.constants';
import { getWarningLabelForDeactivatingUser } from '../user.helpers';

jest.mock('react-i18next', () => ({
  __: (str, vars = EMPTY_OBJECT) =>
    str
      .replace('{{totalAccessibleDealers}}', vars.totalAccessibleDealers)
      .replace('{{totalEmployees}}', vars.totalEmployees),
}));

describe('getWarningLabelForDeactivatingUser', () => {
  it('should return warning label with correct dealer and employee counts', () => {
    const result = getWarningLabelForDeactivatingUser(3, 10);
    expect(result).toBe(
      'Deactivating will deactivate you across all 3 Dealerships and 10 employees related to this user.'
    );
  });

  it('should handle zero values', () => {
    const result = getWarningLabelForDeactivatingUser(0, 0);
    expect(result).toBe(
      'Deactivating will deactivate you across all 0 Dealerships and 0 employees related to this user.'
    );
  });

  it('should handle string numbers', () => {
    const result = getWarningLabelForDeactivatingUser('5', '20');
    expect(result).toBe(
      'Deactivating will deactivate you across all 5 Dealerships and 20 employees related to this user.'
    );
  });
});
