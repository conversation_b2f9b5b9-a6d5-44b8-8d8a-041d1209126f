import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
// Services
import { fetchWorkspacesByType } from '@tekion/tekion-base/services/workspaceService';
import { isCorporateUser } from '@tekion/tekion-base/helpers/user.helper';

// Utils
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import { WORKSPACE_TYPES } from '@tekion/tekion-business/src/constants/workspace/workspaceTypes';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { getLoginData } from '@tekion/tekion-components/src/pages/authentication/reducers/authentication.selectors';

const withAccessibleDealers = WrappedComponent => {
  const WithAccessibleDealers = props => {
    const [accessibleDealersForEnterprise, setAccessibleDealersForEnterprise] = useState(EMPTY_ARRAY);
    const [isLoadingAccessibleDealers, setIsLoadingAccessibleDealers] = useState(false);

    const fetchAccessibleDealers = useCallback(async () => {
      // Only fetch if user is corporate user (has cross-dealer access)
      if (!isCorporateUser()) {
        return;
      }

      try {
        setIsLoadingAccessibleDealers(true);
        // Use workspace access API for corporate users
        const response = await fetchWorkspacesByType(WORKSPACE_TYPES.DEALER, true);
        const dealers = getDataFromResponse(response);

        setAccessibleDealersForEnterprise(dealers);
      } catch (error) {
        const errorMessage = getErrorMessage(error, __('Failed to fetch accessible dealers'));
        toaster(TOASTER_TYPE.ERROR, errorMessage);
        setAccessibleDealersForEnterprise(EMPTY_ARRAY);
      } finally {
        setIsLoadingAccessibleDealers(false);
      }
    }, []);

    useEffect(() => {
      fetchAccessibleDealers();
    }, [fetchAccessibleDealers]);

    return (
      <WrappedComponent
        {...props}
        accessibleDealersForEnterprise={accessibleDealersForEnterprise}
        isLoadingAccessibleDealers={isLoadingAccessibleDealers}
        refreshAccessibleDealers={fetchAccessibleDealers}
      />
    );
  };

  WithAccessibleDealers.propTypes = {
    loginData: PropTypes.object,
  };

  WithAccessibleDealers.defaultProps = {
    loginData: EMPTY_OBJECT,
  };

  const mapStateToProps = state => ({
    loginData: getLoginData(state),
  });

  return connect(mapStateToProps)(WithAccessibleDealers);
};

export default withAccessibleDealers;
