import _noop from 'lodash/noop';

import { genericActionHandlerAdapter } from '@tekion/tekion-base/utils/general';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

export const WAREHOUSE_ACTION_TYPE = {
  CENTRAL_WAREHOUSE_CHANGE: 'CENTRAL_WAREHOUSE_CHANGE',
};

export const WAREHOUSE_ACTION_HANDLERS = {
  [WAREHOUSE_ACTION_TYPE.CENTRAL_WAREHOUSE_CHANGE]: genericActionHandlerAdapter(({ setState, params }) => {
    const { warehouseId, callbackFn = _noop } = params || EMPTY_OBJECT;
    setState({ selectedWarehouse: warehouseId }, callbackFn);
  }),
};
