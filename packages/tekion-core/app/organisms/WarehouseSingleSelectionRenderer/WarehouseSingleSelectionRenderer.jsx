import React, { useState, useCallback, useMemo } from 'react';
import cx from 'classnames';

import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import _find from 'lodash/find';

import PropTypes from 'prop-types';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import Button from '@tekion/tekion-components/src/atoms/Button';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Dropdown, { DROPDOWN_TRIGGER, DROPDOWN_PLACEMENT } from '@tekion/tekion-components/src/molecules/DropDown';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import withActions from '@tekion/tekion-components/src/connectors/withActions';

import colors from '@tekion/tekion-styles-next/scss/exports.scss';

import coreEnv from 'utils/coreEnv';

import OverlayContent from './components/OverlayContent';
import { getFilteredOptions } from './warehouseSingleSelectionRenderer.helpers';
import styles from './warehouseSingleSelectionRenderer.module.scss';
import { ACTION_HANDLERS } from './warehouseSingleSelectionRenderer.actionHandlers';
import { ACTION_TYPES } from './warehouseSingleSelectionRenderer.constants';

const WarehouseSelectionRenderer = ({
  selectedWarehouse,
  options,
  disabled,
  loading,
  shouldShowDivider,
  wrapperClassName,
  onAction,
  onChange,
}) => {
  const [warehouseState, setWarehouseState] = useState({
    searchValue: '',
    expanded: false,
  });

  const handleSearchChange = useCallback(
    e => setWarehouseState(prev => ({ ...prev, searchValue: _get(e, 'target.value') })),
    []
  );

  const handleVisibleChange = useCallback(expanded => setWarehouseState(prev => ({ ...prev, expanded })), []);

  const value = _isNil(selectedWarehouse) ? coreEnv.userPreferredWarehouseId : selectedWarehouse;

  const handleOnChange = useCallback(
    values => {
      if (values === selectedWarehouse) return;
      coreEnv.userPreferredWarehouseId = values;
      onChange(values);
      onAction({
        type: ACTION_TYPES.HANDLE_WAREHOUSE_CHANGE,
        payload: {
          values,
        },
      });
    },
    [onAction, onChange, selectedWarehouse]
  );

  const warehouseOptions = _isEmpty(options) ? coreEnv.warehouseOptions : options;
  const searchValue = _get(warehouseState, 'searchValue');
  const expanded = _get(warehouseState, 'expanded');

  const filteredOptions = useMemo(
    () => getFilteredOptions(warehouseOptions, searchValue),
    [warehouseOptions, searchValue]
  );

  const handleSelect = useCallback(
    selectedValue => {
      setWarehouseState(prev => ({ ...prev, expanded: false }));
      handleOnChange(selectedValue);
    },
    [handleOnChange]
  );

  const selectedWarehouseData = _find(warehouseOptions, warehouse => _get(warehouse, 'value') === value);
  const selectedWarehouseLabel = _get(selectedWarehouseData, 'label');

  return (
    <div className={cx(wrapperClassName, styles.wrapperClass)}>
      <PropertyControlledComponent controllerProperty={shouldShowDivider}>
        <div className={styles.divider} />
      </PropertyControlledComponent>
      <Dropdown
        overlay={overlayProps => (
          <OverlayContent
            {...overlayProps}
            handleSelect={handleSelect}
            searchValue={searchValue}
            handleSearchChange={handleSearchChange}
            filteredOptions={filteredOptions}
            disabled={disabled}
            value={value}
          />
        )}
        trigger={DROPDOWN_TRIGGER.CLICK}
        disabled={disabled}
        placement={DROPDOWN_PLACEMENT.BOTTOM_LEFT}
        visible={expanded}
        onVisibleChange={handleVisibleChange}
        className={styles.container}>
        <Button loading={loading} className={styles.button}>
          <FontIcon size={SIZES.L} className={styles.warehouseIcon}>
            icon-site
          </FontIcon>
          {selectedWarehouseLabel}
          <FontIcon size={SIZES.S} color={colors.azure}>
            icon-chevron-down
          </FontIcon>
        </Button>
      </Dropdown>
    </div>
  );
};

WarehouseSelectionRenderer.propTypes = {
  options: PropTypes.array,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  shouldShowDivider: PropTypes.bool,
  wrapperClassName: PropTypes.string,
  onAction: PropTypes.func,
  onChange: PropTypes.func,
};

WarehouseSelectionRenderer.defaultProps = {
  options: EMPTY_ARRAY,
  disabled: false,
  loading: false,
  shouldShowDivider: true,
  wrapperClassName: undefined,
  onAction: _noop,
  onChange: _noop,
};

export default withActions(undefined, ACTION_HANDLERS)(WarehouseSelectionRenderer);
