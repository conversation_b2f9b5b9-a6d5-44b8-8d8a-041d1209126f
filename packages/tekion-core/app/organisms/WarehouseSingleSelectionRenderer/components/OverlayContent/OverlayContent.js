import React, { memo, useCallback } from 'react';

import _map from 'lodash/map';

import PropTypes from 'prop-types';

import { EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Input from '@tekion/tekion-components/src/atoms/Input';
import Menu from '@tekion/tekion-components/src/molecules/Menu';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';

import styles from '../../warehouseSingleSelectionRenderer.module.scss';

const OverlayContent = ({
  handleSelect,
  searchValue,
  handleSearchChange,
  filteredOptions,
  disabled,
  value,
  ...rest
}) => {
  const handleClick = useCallback(
    e => {
      handleSelect(e.key);
    },
    [handleSelect]
  );

  return (
    <Menu {...rest} onClick={handleClick} className={styles.menu}>
      <Menu.Item disabled>
        <Input
          placeholder={__('Search Warehouse')}
          value={searchValue}
          onChange={handleSearchChange}
          prefix={<FontIcon size={SIZES.S}>icon-search</FontIcon>}
        />
      </Menu.Item>
      {_map(filteredOptions, option => (
        <Menu.Item
          key={option?.value}
          disabled={disabled}
          className="d-flex justify-content-between align-items-center">
          {option?.label}
          <PropertyControlledComponent controllerProperty={option?.value === value}>
            <FontIcon size={SIZES.S}>icon-tick1</FontIcon>
          </PropertyControlledComponent>
        </Menu.Item>
      ))}
    </Menu>
  );
};

OverlayContent.propTypes = {
  handleSelect: PropTypes.func.isRequired,
  searchValue: PropTypes.string,
  handleSearchChange: PropTypes.func.isRequired,
  filteredOptions: PropTypes.array,
  disabled: PropTypes.bool,
  value: PropTypes.string,
};

OverlayContent.defaultProps = {
  searchValue: EMPTY_STRING,
  filteredOptions: EMPTY_ARRAY,
  disabled: false,
  value: EMPTY_STRING,
};

export default memo(OverlayContent);
