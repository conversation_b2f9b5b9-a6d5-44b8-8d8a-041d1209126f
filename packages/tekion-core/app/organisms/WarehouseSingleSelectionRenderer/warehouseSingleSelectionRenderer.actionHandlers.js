import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { updatePreferences } from '@tekion/tekion-business/src/actions/parts/warehouseManagement.actions';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { WAREHOUSE_PREFERENCE_ASSET_TYPE } from '@tekion/tekion-widgets/src/appServices/parts/Warehouse/warehouse.constants';

import { ACTION_TYPES } from './warehouseSingleSelectionRenderer.constants';

const handleWarehouseChange = ({ params }) => {
  const { values: newSelectedWarehouse } = params || EMPTY_OBJECT;
  const payload = {
    assetType: WAREHOUSE_PREFERENCE_ASSET_TYPE,
    userPreferences: [
      {
        preferenceName: 'preferedWarehouseId',
        value: newSelectedWarehouse,
      },
    ],
  };

  try {
    updatePreferences(payload);
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Failed to update warehouse preference'));
  }
};

export const ACTION_HANDLERS = {
  [ACTION_TYPES.HANDLE_WAREHOUSE_CHANGE]: handleWarehouseChange,
};
