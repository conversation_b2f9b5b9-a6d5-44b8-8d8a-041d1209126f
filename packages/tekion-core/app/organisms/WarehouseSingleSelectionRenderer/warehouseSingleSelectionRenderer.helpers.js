import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _toLower from 'lodash/toLower';
import _get from 'lodash/get';

import { EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

export const getFilteredOptions = (options = EMPTY_ARRAY, searchValue = EMPTY_STRING) =>
  _filter(options, option => _includes(_toLower(_get(option, 'label', EMPTY_STRING)), _toLower(searchValue)));
