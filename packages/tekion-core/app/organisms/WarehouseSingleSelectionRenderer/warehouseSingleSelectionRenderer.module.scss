@import "tstyles/component.scss";

.menu {
  max-height: 25rem;
  overflow-y: auto;
}

.button {
  @include flex($justify-content: space-between, $align-items: center);
  @include full-width;
  text-align: left;
  border: none;
  background-color: $lighterGray;
  color: $black;
}

.button:focus {
  color: $black;
}

.container {
  width: 25rem;
  margin: 0 0.8rem;
}

.warehouseIcon {
  background-color: $lightskyblue;
  padding: 0.3rem;
}

.divider {
  border-left: 0.2rem solid $platinum;
  margin-left: 0.8rem;
}

.wrapperClass {
  display: flex;
}
