import React, { useCallback } from 'react';

import PropTypes from 'prop-types';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import useToggle from '@tekion/tekion-base/customHooks/useToggle';

import withRouter from '@tekion/tekion-components/src/hoc/withRouter';
import HeaderSidebar from '@tekion/tekion-components/src/molecules/HeaderSidebar';

import { getCoreRoutesWithRoot } from 'helpers/base.helpers';
import { APP } from './customerHeaderSideBar.constants';

const CustomerHeaderSideBar = ({ navigate, selectedModule, visible, routes, title, menuConfig }) => {
  const [isSidebarVisible, setIsSidebarVisible] = useToggle(visible);

  const handleMenuClick = useCallback(
    ({ key }) => {
      const moduleRoute = routes[key];
      navigate(getCoreRoutesWithRoot(moduleRoute));
    },
    [navigate, routes]
  );

  const menuProps = {
    onClick: handleMenuClick,
    selectedKeys: [selectedModule],
    defaultOpenMenuKeys: [APP.ADDRESS_UPDATES],
  };

  return (
    <HeaderSidebar
      title={title}
      showSearch={false}
      menuConfig={menuConfig}
      menuProps={menuProps}
      toggleSidebarVisibility={setIsSidebarVisible}
      isSidebarVisible={isSidebarVisible}
    />
  );
};

CustomerHeaderSideBar.propTypes = {
  navigate: PropTypes.object,
  selectedModule: PropTypes.string,
  visible: PropTypes.bool,
  routes: PropTypes.object,
  title: PropTypes.string,
  menuConfig: PropTypes.array,
};

CustomerHeaderSideBar.defaultProps = {
  navigate: EMPTY_OBJECT,
  selectedModule: EMPTY_STRING,
  visible: false,
  routes: EMPTY_ARRAY,
  title: EMPTY_STRING,
  menuConfig: EMPTY_ARRAY,
};

export default withRouter(CustomerHeaderSideBar);
