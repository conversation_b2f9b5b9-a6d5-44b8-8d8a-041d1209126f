import React from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';

import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';
import { EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { Heading } from '@tekion/tekion-components/src/atoms';
import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';

import { canViewAddressExclusionPermission } from 'permissions/customerManagement.permissions';
import CustomerHeaderSidebar from './CustomerHeaderSideBar';
import { menuConfig } from './customerHeaderSideBar.config';
import { ROUTES } from './customerHeaderSideBar.constants';

const CustomerHeading = ({ selectedModule, title, getDealerPropertyValue }) => {
  const isNADEnabled = !!getDealerPropertyValue(DEALER_PROPERTIES.CMS_NAD_ENABLED);
  return (
    <div className="d-flex align-items-center">
      {isNADEnabled && canViewAddressExclusionPermission() && (
        <CustomerHeaderSidebar selectedModule={selectedModule} routes={ROUTES} menuConfig={menuConfig} />
      )}
      <Heading size={1}> {title} </Heading>
    </div>
  );
};

CustomerHeading.propTypes = {
  selectedModule: PropTypes.string,
  title: PropTypes.string,
  getDealerPropertyValue: PropTypes.func,
};

CustomerHeading.defaultProps = {
  selectedModule: EMPTY_STRING,
  title: EMPTY_STRING,
  getDealerPropertyValue: _noop,
};

export default withPropertyConsumer(CustomerHeading);
