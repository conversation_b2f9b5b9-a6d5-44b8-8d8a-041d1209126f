import { MENU_CHILD_TYPE } from '@tekion/tekion-components/src/molecules/HeaderSidebar';

import { APP } from './customerHeaderSideBar.constants';

export const menuConfig = [
  {
    type: MENU_CHILD_TYPE.MENU_ITEM,
    key: APP.CUSTOMER_LIST,
    title: __('Customer Management'),
  },
  {
    type: MENU_CHILD_TYPE.SUB_MENU,
    key: APP.ADDRESS_UPDATES,
    titleText: __('Address Updates'),
    menuItems: [
      {
        type: MENU_CHILD_TYPE.MENU_ITEM,
        key: APP.CUSTOMER_ADDRESS_EXCLUSION,
        title: __('Manage Address Exclusions'),
      },
    ],
  },
];
