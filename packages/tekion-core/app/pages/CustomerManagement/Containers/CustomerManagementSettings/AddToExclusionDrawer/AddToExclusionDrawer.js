import React, { useCallback, useMemo, useEffect } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

import _noop from 'lodash/noop';
import _size from 'lodash/size';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import FormPage from '@tekion/tekion-components/src/pages/formPage';
import FooterComponent from '@tekion/tekion-components/src/pages/formPage/molecules/FooterComponent';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import withActionHandlers from '@tekion/tekion-components/src/connectors/withActionHandlers';
import FORM_ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import withSize from '@tekion/tekion-components/src/hoc/withSize';
import Page from '@tekion/tekion-components/src/molecules/pageComponent/PageComponent';

import { ADD_TO_EXCLUSION_DRAWER } from '../NADExclusion/nadExclusion.constants';
import { CONTEXT_ID } from './addToExclusionDrawer.constants';
import { ACTION_HANDLERS } from './addToExclusionDrawer.actionHandlers';
import { getFormFields, getFormSections } from './addToExclusionDrawer.config';

import styles from './addToExclusionDrawer.module.scss';

const AddToExclusionDrawer = props => {
  const { onAction, handleAddToExclusion, values, loading = false, contentHeight } = props;

  useEffect(() => {
    onAction({
      type: FORM_ACTION_TYPES.ON_FORM_INIT,
    });
  }, [onAction]);

  const fields = useMemo(() => getFormFields(onAction), [onAction]);

  const sections = useMemo(() => getFormSections(), EMPTY_ARRAY);

  const renderForm = useCallback(
    () => (
      <FormPage
        headerComponent={null}
        footerComponent={null}
        values={values}
        fields={fields}
        sections={sections}
        contextId={CONTEXT_ID}
        onAction={onAction}
        onClose={handleAddToExclusion}
      />
    ),
    [onAction, fields, handleAddToExclusion, sections, values]
  );

  const handleSaveToExclusion = useCallback(() => {
    onAction({ type: FORM_ACTION_TYPES.ON_FORM_SUBMIT });
  }, [onAction]);

  const renderHeading = useCallback(
    () => (
      <div className={cx('d-flex justify-content-between', styles.headerPanel)}>
        <Heading size={2}>{ADD_TO_EXCLUSION_DRAWER.HEADER}</Heading>
      </div>
    ),
    EMPTY_ARRAY
  );

  return (
    <Page className="full-height full-width">
      <Page.Header className={styles.heading}>{renderHeading()}</Page.Header>
      <Page.Body style={{ height: contentHeight }}>{renderForm()}</Page.Body>
      <Page.Footer>
        <FooterComponent
          primaryButtonLabel={__('Add')}
          secondaryButtonLabel={__('Cancel')}
          isPrimaryDisabled={_size(values?.customerSearch) < 2}
          onClose={handleAddToExclusion}
          onSubmit={handleSaveToExclusion}
          onCancel={handleAddToExclusion}
          primaryActionLoading={loading}
        />
      </Page.Footer>
    </Page>
  );
};

AddToExclusionDrawer.propTypes = {
  values: PropTypes.object,
  onAction: PropTypes.func,
  handleAddToExclusion: PropTypes.func,
  loading: PropTypes.bool,
};

AddToExclusionDrawer.defaultProps = {
  values: EMPTY_OBJECT,
  onAction: _noop,
  handleAddToExclusion: _noop,
  loading: false,
};

export default compose(
  withSize({ hasPageFooter: 0, hasPageHeader: 1 }),
  withActionHandlers(ACTION_HANDLERS)
)(AddToExclusionDrawer);
