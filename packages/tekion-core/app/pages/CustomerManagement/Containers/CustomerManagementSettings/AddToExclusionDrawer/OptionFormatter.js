import React, { Fragment } from 'react';

import _get from 'lodash/get';
import _head from 'lodash/head';
import _find from 'lodash/find';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import CustomerReader from '@tekion/tekion-base/readers/Customer';
import AddressReader from '@tekion/tekion-base/readers/Address';

import { Avatar } from '@tekion/tekion-components/src/atoms';
import Content from '@tekion/tekion-components/src/atoms/Content';

import {
  getPhoneNoForList,
  getPhoneNumberWithExtension,
} from '@tekion/tekion-widgets/src/organisms/MergeCustomersList/helpers/mergeCustomersList.general';

import { getCustomerName } from '../NADExclusion/nadExclusion.helpers';
import { CUSTOMER_SEARCH_MENU_OPTION_STYLES } from '../NADExclusion/nadExclusion.constants';

const getLineAndCity = address => {
  const line1 = AddressReader.line1(address) || '';
  const city = AddressReader.city(address) || '';
  return {
    line1,
    city,
  };
};

export const getAddress = (line, city) => `${line} , ${city}`;

export const getVehicleDisplayName = vehicle => {
  const { vin, make, model, year } = vehicle;
  return `${vin} | ${year} ${make} ${model}`;
};

export const optionLabelFormatterForCustomerSearch = defaultPhoneCode => props => {
  const customerData = _get(props, 'optionData', EMPTY_OBJECT);

  // Customer Id and Name
  const customerName = getCustomerName(customerData);
  const displayId = CustomerReader.displayId(customerData);
  const name = customerName ? `${displayId} - ${customerName}` : displayId;

  // Customer contact
  const phones = CustomerReader.phones(customerData);
  const phoneNo = getPhoneNoForList(phones);

  // Customer Address
  const addresses = CustomerReader.addresses(customerData);
  let defaultAddress = _find(addresses, address => AddressReader.default(address));
  if (!defaultAddress) {
    defaultAddress = _head(addresses);
  }
  const { line1, city } = getLineAndCity(defaultAddress);
  const addressText = getAddress(line1, city);

  // Customer Vehicle
  const headVehicle = _head(CustomerReader.vehicleInfo(customerData));
  const vehicleYearMakeModel = headVehicle ? getVehicleDisplayName(headVehicle) : '';

  return (
    <Fragment>
      <Content className="inline-block mr-4" style={CUSTOMER_SEARCH_MENU_OPTION_STYLES.customer}>
        <div className="d-flex align-items-center">
          <Avatar name={customerName} displayName={false} colorizeOnName containerClassName="m-r-4" />
          {name}
        </div>
      </Content>
      <Content className="inline-block mr-4" style={CUSTOMER_SEARCH_MENU_OPTION_STYLES.contact}>
        {getPhoneNumberWithExtension(phoneNo, defaultPhoneCode, true)}
      </Content>
      <Content className="inline-block mr-4" style={CUSTOMER_SEARCH_MENU_OPTION_STYLES.address}>
        {addressText}
      </Content>
      <Content className="inline-block mr-4" style={CUSTOMER_SEARCH_MENU_OPTION_STYLES.vehicle}>
        {vehicleYearMakeModel}
      </Content>
    </Fragment>
  );
};
