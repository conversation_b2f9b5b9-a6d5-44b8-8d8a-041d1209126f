import React from 'react';
import PropTypes from 'prop-types';

import _head from 'lodash/head';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers/constants/resourceType';
import { AdvanceAsyncSelect } from '@tekion/tekion-components/src/molecules/advancedSelect';
import withSelectInput from '@tekion/tekion-components/src/organisms/FormBuilder/hoc/withSelectInput';
import MenuList from '@tekion/tekion-widgets/src/organisms/MergeCustomersList/components/MenuList';
import {
  SingleValue,
  DropdownIndicator,
} from '@tekion/tekion-widgets/src/organisms/MergeCustomersList/components/MenuList/SingleValue';

import { getFiltersWithExcludedCustomerId } from 'pages/CustomerManagement/customerManagement.helper';
import {
  CUSTOMER_SEARCH_HEADER_SECTION,
  CUSTOMER_SEARCH_OPTION_STYLES,
  MENU_STYLES,
} from '../NADExclusion/nadExclusion.constants';
import { optionLabelFormatterForCustomerSearch } from './OptionFormatter';
import styles from './addToExclusionDrawer.module.scss';

const SelectInput = withSelectInput(AdvanceAsyncSelect);

const getFiltersForCustomerSearch = () => [
  ...getFiltersWithExcludedCustomerId(),
  { field: 'stopNADVerification', operator: OPERATORS.NIN, values: [true] },
];

const SelectInputRenderer = onAction => props => {
  const { data } = props;
  const id = _head(data);
  return (
    <SelectInput
      {...props}
      placeholder={__('Type Customer Name')}
      className={styles.searchCustomer}
      resourceType={RESOURCE_TYPE.CUSTOMER}
      filters={getFiltersForCustomerSearch()}
      shouldFetchOnValueChange
      onAction={onAction}
      id={id}
      formatOptionLabel={optionLabelFormatterForCustomerSearch()}
      styles={{
        menu: providedStyles => ({ ...providedStyles, ...MENU_STYLES }),
        option: providedStyles => ({
          ...providedStyles,
          ...CUSTOMER_SEARCH_OPTION_STYLES,
        }),
      }}
      components={{
        DropdownIndicator,
        SingleValue,
        MenuList: MenuList(CUSTOMER_SEARCH_HEADER_SECTION),
      }}
    />
  );
};

SelectInputRenderer.propTypes = {
  data: PropTypes.array,
};

SelectInputRenderer.defaultProps = {
  data: EMPTY_ARRAY,
};

export default SelectInputRenderer;
