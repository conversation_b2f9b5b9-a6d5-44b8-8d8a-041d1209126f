// Mock dependencies before imports
jest.mock('@tekion/tekion-base/utils/updateElementAtIndex');
jest.mock('@tekion/tekion-components/src/molecules/tableInputField/utils/tableRowHelpers');
jest.mock('../../NADExclusion/nadExclusion.actions');

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { ES_REFETCH_DELAY } from '@tekion/tekion-base/constants/general';
import FORM_ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import TABLE_ACTION_TYPES from '@tekion/tekion-components/src/molecules/tableInputField/constants/TableInputField.actionTypes';
import { REMOVE_ACTION } from '@tekion/tekion-components/src/molecules/tableInputField/constants/general';

import updateElementAtIndex from '@tekion/tekion-base/utils/updateElementAtIndex';
import { removeAtIndex } from '@tekion/tekion-components/src/molecules/tableInputField/utils/tableRowHelpers';
import { excludeCustomersAction } from '../../NADExclusion/nadExclusion.actions';
import { ACTION_HANDLERS } from '../addToExclusionDrawer.actionHandlers';

describe('addToExclusionDrawer.actionHandlers', () => {
  let mockGetState;
  let mockSetState;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    mockGetState = jest.fn();
    mockSetState = jest.fn();

    // Default mock implementations
    updateElementAtIndex.mockImplementation((array, element, index) => {
      const newArray = [...array];
      newArray[index] = element;
      return newArray;
    });

    removeAtIndex.mockImplementation(index => (item, idx) => idx !== index);
    excludeCustomersAction.mockResolvedValue(true);
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('handleFormInit', () => {
    const handleFormInit = ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FORM_INIT];

    it('should initialize form with empty customer search row', () => {
      handleFormInit(null, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({
        values: { customerSearch: [{ isLastRow: true }] },
      });
    });

    it('should handle undefined action parameter', () => {
      handleFormInit(undefined, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({
        values: { customerSearch: [{ isLastRow: true }] },
      });
    });
  });

  describe('handleFormSubmit', () => {
    const handleFormSubmit = ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FORM_SUBMIT];
    it('should handle empty customer search values', async () => {
      mockGetState.mockReturnValue({
        values: { customerSearch: [{ isLastRow: true }] },
        handleAddToExclusion: jest.fn(),
        handleTableItems: jest.fn(),
      });

      await handleFormSubmit(null, { getState: mockGetState, setState: mockSetState });

      expect(excludeCustomersAction).toHaveBeenCalledWith([]);
    });

    it('should handle undefined values in customer search', async () => {
      mockGetState.mockReturnValue({
        values: {
          customerSearch: [{ value: [undefined] }, { value: ['customer2'] }, { isLastRow: true }],
        },
        handleAddToExclusion: jest.fn(),
        handleTableItems: jest.fn(),
      });

      await handleFormSubmit(null, { getState: mockGetState, setState: mockSetState });

      expect(excludeCustomersAction).toHaveBeenCalledWith(['customer2']);
    });

    it('should handle missing state values', async () => {
      mockGetState.mockReturnValue({});

      await handleFormSubmit(null, { getState: mockGetState, setState: mockSetState });

      expect(excludeCustomersAction).toHaveBeenCalledWith([]);
    });

    it('should handle API rejection', async () => {
      mockGetState.mockReturnValue({
        values: { customerSearch: [{ value: ['customer1'] }] },
        handleAddToExclusion: jest.fn(),
        handleTableItems: jest.fn(),
      });

      excludeCustomersAction.mockRejectedValue(new Error('API Error'));

      await expect(handleFormSubmit(null, { getState: mockGetState, setState: mockSetState })).rejects.toThrow(
        'API Error'
      );

      expect(mockSetState).toHaveBeenCalledWith({ loading: true });
    });
  });

  describe('handleFieldChange', () => {
    const handleFieldChange = ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FIELD_CHANGE];

    it('should add new customer when field changes with new value', () => {
      const action = {
        payload: {
          value: ['customer1'],
          id: undefined,
        },
      };

      mockGetState.mockReturnValue({
        values: {
          customerSearch: [{ isLastRow: true }],
        },
      });

      handleFieldChange(action, { setState: mockSetState, getState: mockGetState });

      expect(mockSetState).toHaveBeenCalledWith({
        values: {
          customerSearch: [{ value: ['customer1'] }, { isLastRow: true }],
        },
      });
    });

    it('should update existing customer when field changes with existing id', () => {
      const action = {
        payload: {
          value: ['customer1-updated'],
          id: 'customer1',
        },
      };

      mockGetState.mockReturnValue({
        values: {
          customerSearch: [{ value: ['customer1'] }, { value: ['customer2'] }, { isLastRow: true }],
        },
      });

      updateElementAtIndex.mockReturnValue([{ value: ['customer1-updated'] }, { value: ['customer2'] }]);

      handleFieldChange(action, { setState: mockSetState, getState: mockGetState });

      expect(updateElementAtIndex).toHaveBeenCalledWith(
        [{ value: ['customer1'] }, { value: ['customer2'] }],
        { value: ['customer1-updated'] },
        0
      );
    });

    it('should not add duplicate customer', () => {
      const action = {
        payload: {
          value: ['customer1'],
          id: undefined,
        },
      };

      mockGetState.mockReturnValue({
        values: {
          customerSearch: [{ value: ['customer1'] }, { isLastRow: true }],
        },
      });

      handleFieldChange(action, { setState: mockSetState, getState: mockGetState });

      expect(mockSetState).toHaveBeenCalledWith({
        values: {
          customerSearch: [{ value: ['customer1'] }, { isLastRow: true }],
        },
      });
    });

    it('should handle empty action payload', () => {
      mockGetState.mockReturnValue({
        values: { customerSearch: [{ isLastRow: true }] },
      });

      handleFieldChange(EMPTY_OBJECT, { setState: mockSetState, getState: mockGetState });

      expect(mockSetState).toHaveBeenCalledWith({
        values: {
          customerSearch: [{ isLastRow: true }],
        },
      });
    });

    it('should handle missing customerSearch in state', () => {
      const action = {
        payload: {
          value: ['customer1'],
          id: undefined,
        },
      };

      mockGetState.mockReturnValue({
        values: {},
      });

      handleFieldChange(action, { setState: mockSetState, getState: mockGetState });

      expect(mockSetState).toHaveBeenCalledWith({
        values: {
          customerSearch: [{ value: ['customer1'] }, { isLastRow: true }],
        },
      });
    });

    it('should handle undefined action', () => {
      mockGetState.mockReturnValue({
        values: { customerSearch: [{ isLastRow: true }] },
      });

      handleFieldChange(undefined, { setState: mockSetState, getState: mockGetState });

      expect(mockSetState).toHaveBeenCalledWith({
        values: {
          customerSearch: [{ isLastRow: true }],
        },
      });
    });
  });

  describe('handleTableActionClick', () => {
    const handleTableActionClick = ACTION_HANDLERS[TABLE_ACTION_TYPES.TABLE_ACTION_CLICK];

    it('should remove row when REMOVE_ACTION is triggered', () => {
      const action = {
        payload: {
          actionType: REMOVE_ACTION.id,
          nestingPath: [1], // Remove second row
        },
      };

      mockGetState.mockReturnValue({
        values: {
          customerSearch: [
            { value: ['customer1'] },
            { value: ['customer2'] },
            { value: ['customer3'] },
            { isLastRow: true },
          ],
        },
      });

      // Mock the filter function to simulate row removal
      const mockFilteredResult = [{ value: ['customer1'] }, { value: ['customer3'] }, { isLastRow: true }];

      // Since we're using _filter with removeAtIndex, we need to mock the behavior
      removeAtIndex.mockReturnValue((item, index) => index !== 1);

      handleTableActionClick(action, { getState: mockGetState, setState: mockSetState });

      expect(removeAtIndex).toHaveBeenCalledWith(1);
      expect(mockSetState).toHaveBeenCalledWith({
        values: {
          customerSearch: expect.any(Array),
        },
      });
    });

    it('should handle non-REMOVE_ACTION types gracefully', () => {
      const action = {
        payload: {
          actionType: 'OTHER_ACTION',
          nestingPath: [0],
        },
      };

      mockGetState.mockReturnValue({
        values: {
          customerSearch: [{ value: ['customer1'] }, { isLastRow: true }],
        },
      });

      handleTableActionClick(action, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).not.toHaveBeenCalled();
    });

    it('should handle empty payload', () => {
      const action = { payload: {} };

      mockGetState.mockReturnValue({
        values: {
          customerSearch: [{ value: ['customer1'] }, { isLastRow: true }],
        },
      });

      handleTableActionClick(action, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).not.toHaveBeenCalled();
    });

    it('should handle missing nestingPath', () => {
      const action = {
        payload: {
          actionType: REMOVE_ACTION.id,
          nestingPath: undefined,
        },
      };

      mockGetState.mockReturnValue({
        values: {
          customerSearch: [{ value: ['customer1'] }, { isLastRow: true }],
        },
      });

      handleTableActionClick(action, { getState: mockGetState, setState: mockSetState });

      expect(removeAtIndex).toHaveBeenCalledWith(undefined);
    });

    it('should handle empty customerSearch', () => {
      const action = {
        payload: {
          actionType: REMOVE_ACTION.id,
          nestingPath: [0],
        },
      };

      mockGetState.mockReturnValue({
        values: { customerSearch: [] },
      });

      handleTableActionClick(action, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({
        values: { customerSearch: [] },
      });
    });

    it('should handle undefined action', () => {
      mockGetState.mockReturnValue({
        values: {
          customerSearch: [{ value: ['customer1'] }, { isLastRow: true }],
        },
      });

      handleTableActionClick(undefined, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).not.toHaveBeenCalled();
    });
  });

  describe('ACTION_HANDLERS object', () => {
    it('should contain all expected action handlers', () => {
      const expectedHandlers = [
        FORM_ACTION_TYPES.ON_FORM_INIT,
        FORM_ACTION_TYPES.ON_FIELD_CHANGE,
        TABLE_ACTION_TYPES.TABLE_ACTION_CLICK,
        FORM_ACTION_TYPES.ON_FORM_SUBMIT,
      ];

      expectedHandlers.forEach(actionType => {
        expect(ACTION_HANDLERS).toHaveProperty(actionType);
        expect(typeof ACTION_HANDLERS[actionType]).toBe('function');
      });
    });

    it('should have the correct number of handlers', () => {
      expect(Object.keys(ACTION_HANDLERS)).toHaveLength(4);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete workflow: init -> add customers -> submit', async () => {
      const mockHandleAddToExclusion = jest.fn();
      const mockHandleTableItems = jest.fn();

      // Step 1: Initialize form
      ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FORM_INIT](null, { setState: mockSetState });

      // Step 2: Add first customer
      mockGetState.mockReturnValue({
        values: { customerSearch: [{ isLastRow: true }] },
      });

      const addCustomer1Action = {
        payload: { value: ['customer1'], id: undefined },
      };

      ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FIELD_CHANGE](addCustomer1Action, {
        setState: mockSetState,
        getState: mockGetState,
      });

      // Step 3: Add second customer
      mockGetState.mockReturnValue({
        values: {
          customerSearch: [{ value: ['customer1'] }, { isLastRow: true }],
        },
      });

      const addCustomer2Action = {
        payload: { value: ['customer2'], id: undefined },
      };

      ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FIELD_CHANGE](addCustomer2Action, {
        setState: mockSetState,
        getState: mockGetState,
      });

      // Step 4: Submit form
      mockGetState.mockReturnValue({
        values: {
          customerSearch: [{ value: ['customer1'] }, { value: ['customer2'] }, { isLastRow: true }],
        },
        handleAddToExclusion: mockHandleAddToExclusion,
        handleTableItems: mockHandleTableItems,
      });

      mockSetState.mockImplementation((state, callback) => {
        if (callback) callback();
      });

      await ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FORM_SUBMIT](null, {
        getState: mockGetState,
        setState: mockSetState,
      });

      // Verify the complete workflow
      expect(mockSetState).toHaveBeenCalledWith({
        values: { customerSearch: [{ isLastRow: true }] },
      });
      expect(excludeCustomersAction).toHaveBeenCalledWith(['customer1', 'customer2']);
      expect(mockHandleAddToExclusion).toHaveBeenCalled();
    });

    it('should handle workflow with customer removal', () => {
      // Step 1: Setup initial state with customers
      mockGetState.mockReturnValue({
        values: {
          customerSearch: [
            { value: ['customer1'] },
            { value: ['customer2'] },
            { value: ['customer3'] },
            { isLastRow: true },
          ],
        },
      });

      // Step 2: Remove middle customer
      const removeAction = {
        payload: {
          actionType: REMOVE_ACTION.id,
          nestingPath: [1],
        },
      };

      removeAtIndex.mockReturnValue((item, index) => index !== 1);

      ACTION_HANDLERS[TABLE_ACTION_TYPES.TABLE_ACTION_CLICK](removeAction, {
        getState: mockGetState,
        setState: mockSetState,
      });

      expect(removeAtIndex).toHaveBeenCalledWith(1);
      expect(mockSetState).toHaveBeenCalledWith({
        values: { customerSearch: expect.any(Array) },
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle setState callback errors', async () => {
      mockGetState.mockReturnValue({
        values: { customerSearch: [{ value: ['customer1'] }] },
        handleAddToExclusion: jest.fn(),
        handleTableItems: jest.fn(),
      });

      mockSetState.mockImplementation((state, callback) => {
        if (callback) {
          throw new Error('setState callback error');
        }
      });

      await expect(
        ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FORM_SUBMIT](null, {
          getState: mockGetState,
          setState: mockSetState,
        })
      ).rejects.toThrow('setState callback error');
    });

    it('should handle missing getState/setState functions', () => {
      expect(() => {
        ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FORM_INIT](null, {});
      }).toThrow();

      expect(() => {
        ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FIELD_CHANGE](null, {});
      }).toThrow();
    });

    it('should handle complex customer search values', () => {
      const action = {
        payload: {
          value: [{ id: 'customer1', name: 'John Doe', complex: { nested: 'data' } }],
          id: undefined,
        },
      };

      mockGetState.mockReturnValue({
        values: { customerSearch: [{ isLastRow: true }] },
      });

      expect(() => {
        ACTION_HANDLERS[FORM_ACTION_TYPES.ON_FIELD_CHANGE](action, {
          setState: mockSetState,
          getState: mockGetState,
        });
      }).not.toThrow();
    });
  });
});
