import _filter from 'lodash/filter';
import _head from 'lodash/head';
import _map from 'lodash/map';
import _compact from 'lodash/compact';
import _noop from 'lodash/noop';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { ES_REFETCH_DELAY } from '@tekion/tekion-base/constants/general';

import FORM_ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import TABLE_ACTION_TYPES from '@tekion/tekion-components/src/molecules/tableInputField/constants/TableInputField.actionTypes';
import { REMOVE_ACTION } from '@tekion/tekion-components/src/molecules/tableInputField/constants/general';
import { removeAtIndex } from '@tekion/tekion-components/src/molecules/tableInputField/utils/tableRowHelpers';

import { excludeCustomersAction } from '../NADExclusion/nadExclusion.actions';
import { getModifiedCustomerList } from '../NADExclusion/nadExclusion.helpers';

const handleFormInit = (_, { setState }) => {
  setState({
    values: { customerSearch: [{ isLastRow: true }] },
  });
};

const handleListFetchWithDelay = handleTableItems => setTimeout(() => handleTableItems(), ES_REFETCH_DELAY);

const handleFormSubmit = async (_, { getState, setState }) => {
  setState({ loading: true });
  const { values = EMPTY_OBJECT, handleAddToExclusion = _noop, handleTableItems = _noop } = getState();
  const { customerSearch = EMPTY_OBJECT } = values;
  const idsToInclude = _compact(_map(customerSearch, item => _head(item?.value)));
  const response = await excludeCustomersAction(idsToInclude);
  if (response) {
    handleAddToExclusion();
    handleListFetchWithDelay(handleTableItems);
  }
  setState({ loading: false });
};

const handleFieldChange = (action = EMPTY_OBJECT, { setState, getState }) => {
  const { values = EMPTY_OBJECT } = getState();
  const { customerSearch = EMPTY_OBJECT } = values || EMPTY_OBJECT;
  const updatedValues = getModifiedCustomerList(action, customerSearch);
  setState({
    values: { customerSearch: updatedValues },
  });
};

const handleTableActionClick = (action = EMPTY_OBJECT, { getState, setState }) => {
  const { payload = EMPTY_OBJECT } = action;
  const { values = EMPTY_OBJECT } = getState();
  const { customerSearch = EMPTY_OBJECT } = values;

  const { actionType, nestingPath } = payload;
  if (actionType === REMOVE_ACTION.id) {
    const indexOfRowToDelete = _head(nestingPath);
    const updatedValues = _filter(customerSearch, removeAtIndex(indexOfRowToDelete));
    setState({
      values: { customerSearch: updatedValues },
    });
  }
};

export const ACTION_HANDLERS = {
  [FORM_ACTION_TYPES.ON_FORM_INIT]: handleFormInit,
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: handleFieldChange,
  [TABLE_ACTION_TYPES.TABLE_ACTION_CLICK]: handleTableActionClick,
  [FORM_ACTION_TYPES.ON_FORM_SUBMIT]: handleFormSubmit,
};
