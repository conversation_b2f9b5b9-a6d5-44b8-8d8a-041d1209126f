import { defaultMemoize } from 'reselect';

import { tget } from '@tekion/tekion-base/utils/general';
import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import { ColumnConfig, CellConfig } from '@tekion/tekion-components/src/molecules/tableInputField';
import makeCellRenderer from '@tekion/tekion-components/src/molecules/CellRenderers/makeCellRenderer';

import { CUSTOMER_SEARCH_COLUMN, VALUE } from './addToExclusionDrawer.constants';
import SelectInputRenderer from './SelectInputRenderer';

const getAccessor = values => {
  const value = tget(values, VALUE, EMPTY_STRING);
  return value;
};

const getComponentFieldColumnProps = (props = EMPTY_OBJECT) => props;

const SelectInputCellRenderer = onAction => makeCellRenderer(SelectInputRenderer(onAction));

const getComponentColumn = onAction =>
  new ColumnConfig(CUSTOMER_SEARCH_COLUMN)
    .addCellConfig(new CellConfig().setComponent(SelectInputCellRenderer(onAction)))
    .setTitle(__('Select Customer'))
    .setMapCellPropsToComponentProps(getComponentFieldColumnProps)
    .setAccessor(getAccessor);

export const getCustomerSearchColumn = defaultMemoize(onAction => [getComponentColumn(onAction)]);
