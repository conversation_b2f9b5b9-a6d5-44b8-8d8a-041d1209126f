import { defaultMemoize } from 'reselect';

import _castArray from 'lodash/castArray';
import _stubTrue from 'lodash/stubTrue';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import { TableFieldWithActions } from '@tekion/tekion-components/src/molecules/tableInputField';
import { REMOVE_ACTION } from '@tekion/tekion-components/src/molecules/tableInputField/constants/general';
import FieldLabel from '@tekion/tekion-components/src/organisms/FormBuilder/components/fieldLabel';

import { ADD_TO_EXCLUSION_DRAWER } from '../NADExclusion/nadExclusion.constants';
import { getCustomerSearchColumn } from './addToExclusionDrawer.columns';
import { CUSTOMER_SEARCH, DISCLAIMER } from './addToExclusionDrawer.constants';

import styles from './addToExclusionDrawer.module.scss';

const getActionsForRow = ({ original = EMPTY_OBJECT }) => {
  const { isLastRow = false } = original;
  if (isLastRow) return EMPTY_ARRAY;
  return _castArray(REMOVE_ACTION);
};

export const getFormFields = defaultMemoize(onAction => ({
  [DISCLAIMER]: {
    id: DISCLAIMER,
    renderer: FieldLabel,
    renderOptions: {
      label: ADD_TO_EXCLUSION_DRAWER.DESCRIPTION,
      fieldLabelClassName: styles.disclaimer,
    },
  },
  [CUSTOMER_SEARCH]: {
    id: CUSTOMER_SEARCH,
    renderer: TableFieldWithActions,
    renderOptions: {
      columns: getCustomerSearchColumn(onAction),
      getActionsForRow,
      shouldAddNewRow: _stubTrue,
      className: 'full-width',
      containerClassName: 'full-width',
    },
  },
}));

export const getFormSections = defaultMemoize(() => [
  {
    className: styles.sectionWidth,
    rows: [
      { columns: [DISCLAIMER] },
      {
        columns: [CUSTOMER_SEARCH],
      },
    ],
  },
]);
