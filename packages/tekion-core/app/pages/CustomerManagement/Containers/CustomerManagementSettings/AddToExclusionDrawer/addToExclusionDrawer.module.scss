@use "tstyles/component.scss";

.contentSection {
  padding: 2.4rem;
}

.sectionWidth {
  max-width: 100% !important;
}

.searchCustomer {
  width: 100%;
}

.heading {
  padding: 0 !important;
}

.disclaimer {
  white-space: normal;
  margin-bottom: 2.4rem;
  margin-top: 2.4rem;
}

.headerPanel {
  width: 100%;
  height: 6.4rem;
  background-color: component.$glitter;
  padding-left: 2.4rem;
  padding-top: 1.6rem;
  font-family: component.$font-bold;
  font-size: component.$font-size-mlarge;
}
