import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import customerReader from '@tekion/tekion-base/readers/Customer';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import FieldLabel from '@tekion/tekion-components/src/organisms/FormBuilder/components/fieldLabel';

import { produceCustomerData } from 'components/UniversalGuestProfile/utils/utils.common';
import UniversalGuestProfileAvatar from 'components/UniversalGuestProfile/components/UniversalGuestProfileAvatar';
import { MODULE_TYPES, PROFILE_TYPES } from 'components/UniversalGuestProfile/UniversalGuestProfile.constants';
import { getCustomerName } from '../nadExclusion.helpers';
import styles from './cellRenderer.module.scss';

const CustomerNameRenderer = props => {
  const { original } = props;
  const customerId = customerReader.displayId(original);
  const customerName = getCustomerName(original);
  const profileDetails = produceCustomerData(original);

  return (
    <div className={cx('d-flex', styles.customerName)}>
      <UniversalGuestProfileAvatar
        showPopover
        profileId={original?.id}
        profileType={PROFILE_TYPES.CUSTOMER}
        profileDetails={profileDetails}
        module={MODULE_TYPES.CORE}
      />
      <div className="d-flex-column">
        <FieldLabel label={customerName} />
        <FieldLabel labelClassName={styles.displayId} label={customerId} />
      </div>
    </div>
  );
};

CustomerNameRenderer.propTypes = {
  original: PropTypes.object,
};

CustomerNameRenderer.defaultProps = {
  original: EMPTY_OBJECT,
};

export default React.memo(CustomerNameRenderer);
