import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import customerReader from '@tekion/tekion-base/readers/Customer';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { CUSTOMER_TYPE_DISPLAY } from '@tekion/tekion-base/constants/customer.constants';

import Icon from '@tekion/tekion-components/src/atoms/FontIcon';
import FieldLabel from '@tekion/tekion-components/src/organisms/FormBuilder/components/fieldLabel';

import styles from './cellRenderer.module.scss';

const CustomerTypeRenderer = props => {
  const { original } = props;
  const customerType = customerReader.customerType(original);
  const chargeCustomer = customerReader.chargeCustomer(original);
  const customerTypeLabel = useMemo(() => CUSTOMER_TYPE_DISPLAY[customerType], [customerType]);

  return (
    <div className={cx('d-flex', 'align-items-center', styles.customerName)}>
      <FieldLabel label={customerTypeLabel} />
      {!!chargeCustomer && <Icon className={styles.chargeCustomerIcon}>icon-charge</Icon>}
    </div>
  );
};

CustomerTypeRenderer.propTypes = {
  original: PropTypes.object,
};

CustomerTypeRenderer.defaultProps = {
  original: EMPTY_OBJECT,
};

export default React.memo(CustomerTypeRenderer);
