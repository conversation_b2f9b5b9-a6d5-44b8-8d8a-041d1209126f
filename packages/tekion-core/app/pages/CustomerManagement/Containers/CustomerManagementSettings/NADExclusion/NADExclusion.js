import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';
import _noop from 'lodash/noop';
import _size from 'lodash/size';

import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import { EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import TableManager from '@tekion/tekion-components/src/organisms/TableManager';
import Drawer from '@tekion/tekion-components/src/molecules/drawer';
import withActionHandlers from '@tekion/tekion-components/src/connectors/withActionHandlers';
import withCustomSortTable from '@tekion/tekion-components/src/organisms/withCustomSortTable';
import TABLE_ACTION_TYPES from '@tekion/tekion-components/src/organisms/TableManager/constants/actionTypes';

import { canExcludeCustomersFromRefresh } from 'permissions/customerManagement.permissions';
import CustomerHeading from '../../CustomerHeaderSideBar/CustomerHeading';
import { APP } from '../../CustomerHeaderSideBar/customerHeaderSideBar.constants';
import AddToExclusionDrawer from '../AddToExclusionDrawer/AddToExclusionDrawer';
import RemoveExclusionModal from './RemoveExclusionModal';
import { getSubHeaderProps, getTableProps } from './nadExclusion.helpers';
import { ACTION_TYPES, ADD_TO_EXCLUSION_DRAWER, TABLE_MANAGER_PROPS_FOR_NAD_EXCLUSION } from './nadExclusion.constants';
import { COLUMN_CONFIG_FOR_NDC_EXCLUSION_SYNC } from './nadExclusion.column';
import { ACTION_HANDLERS } from './nadExclusion.actionHandlers';

import styles from './nadExclusion.module.scss';

const SortableTableManager = withCustomSortTable(TableManager);

const NADExclusion = props => {
  const {
    tableData,
    onAction,
    totalCount,
    showExclusionDrawer,
    searchText,
    currentPage,
    pageSize,
    loading,
    selectedRequests,
    showExcludeModal,
    isExcludeLoading,
  } = props;

  const handleSearchValueChange = useCallback(
    value => {
      onAction({ type: ACTION_TYPES.UPDATE_SEARCH_TEXT, payload: { value } });
    },
    [onAction]
  );

  const handleSearch = useCallback(
    value => {
      onAction({ type: ACTION_TYPES.HANDLE_SEARCH, payload: { value } });
    },
    [onAction]
  );

  const handleCloseExcludeModal = useCallback(() => {
    onAction({ type: ACTION_TYPES.CLOSE_EXCLUDE_MODAL });
  }, [onAction]);

  const handleConfirmExclude = useCallback(() => {
    onAction({ type: ACTION_TYPES.EXCLUDE_FROM_NAD_SYNC });
  }, [onAction]);

  const onRowActionClick = useCallback(
    (actionType, listItem) => {
      onAction({ type: ACTION_TYPES.REMOVE_FROM_EXCLUSION, payload: { actionType, listItem } });
    },
    [onAction]
  );

  const handleSelection = useCallback(
    selection => {
      onAction({
        type: ACTION_TYPES.CUSTOMER_SELECT,
        payload: selection,
      });
    },
    [onAction]
  );

  const handleAddToExclusion = useCallback(() => {
    onAction({ type: ACTION_TYPES.ADD_TO_EXCLUSION });
  }, [onAction]);

  const handleTableItems = useCallback(() => {
    onAction({ type: TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH });
  }, [onAction]);

  const handleAction = useCallback(() => {
    onAction({
      type: ACTION_TYPES.BULK_ACTION,
    });
  }, [onAction]);

  const tableProps = useMemo(
    () =>
      getTableProps({
        totalCount,
        currentPage,
        pageSize,
        tableData,
        selectedRequests,
        loading,
        onRowActionClick,
        handleSelection,
      }),
    [totalCount, currentPage, pageSize, loading, tableData, selectedRequests, onRowActionClick, handleSelection]
  );

  const subHeaderProps = useMemo(
    () =>
      getSubHeaderProps({
        totalCount,
        isSearchActive: true,
        handleSearch,
        searchText,
        handleSearchValueChange,
        handleAction,
        isBulkActionsDisabled: _size(selectedRequests) === 0 || !canExcludeCustomersFromRefresh(),
        handleAddToExclusion,
      }),
    [
      totalCount,
      searchText,
      handleAddToExclusion,
      handleSearch,
      handleAction,
      handleSearchValueChange,
      selectedRequests,
    ]
  );

  return (
    <>
      <div className={styles.heading}>
        <CustomerHeading selectedModule={APP.CUSTOMER_ADDRESS_EXCLUSION} title={__('Manage Address Exclusions')} />
      </div>
      <SortableTableManager
        tableProps={tableProps}
        columns={COLUMN_CONFIG_FOR_NDC_EXCLUSION_SYNC}
        tableManagerProps={TABLE_MANAGER_PROPS_FOR_NAD_EXCLUSION}
        data={tableData}
        onAction={onAction}
        subHeaderProps={subHeaderProps}
        containerClassName={styles.containerClassName}
      />
      {showExclusionDrawer && (
        <Drawer
          closable
          onClose={handleAddToExclusion}
          visible={showExclusionDrawer}
          className="full-height overflow-hidden"
          width={ADD_TO_EXCLUSION_DRAWER.WIDTH}
          bodyStyle={ADD_TO_EXCLUSION_DRAWER.DRAWER_BODY_STYLES}
          destroyOnClose
          closeIcon
          maskClosable={false}>
          <AddToExclusionDrawer
            onAction={onAction}
            handleAddToExclusion={handleAddToExclusion}
            handleTableItems={handleTableItems}
          />
        </Drawer>
      )}
      {showExcludeModal && (
        <RemoveExclusionModal
          onCancel={handleCloseExcludeModal}
          onConfirm={handleConfirmExclude}
          loading={isExcludeLoading}
        />
      )}
    </>
  );
};

NADExclusion.propTypes = {
  tableData: PropTypes.array,
  onAction: PropTypes.func,
  totalCount: PropTypes.number,
  showExclusionDrawer: PropTypes.bool,
  searchText: PropTypes.string,
  currentPage: PropTypes.number,
  pageSize: PropTypes.number,
  loading: PropTypes.bool,
  selectedRequests: PropTypes.array,
  showExcludeModal: PropTypes.bool,
  isExcludeLoading: PropTypes.bool,
};

NADExclusion.defaultProps = {
  tableData: EMPTY_ARRAY,
  onAction: _noop,
  totalCount: 0,
  showExclusionDrawer: false,
  searchText: EMPTY_STRING,
  currentPage: 0,
  pageSize: DEFAULT_PAGE_SIZE,
  loading: false,
  selectedRequests: EMPTY_ARRAY,
  showExcludeModal: false,
  isExcludeLoading: false,
};

export default compose(React.memo, withActionHandlers(ACTION_HANDLERS))(NADExclusion);
