import React from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import cx from 'classnames';

import Modal from '@tekion/tekion-components/src/molecules/Modal';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';

import styles from './removeExclusionModal.module.scss';

const RemoveExclusionModal = ({ loading, onConfirm, onCancel }) => (
  <Modal
    visible
    title={__('Remove from Exclusions')}
    submitBtnText={__('Remove')}
    secondaryBtnText={__('Close')}
    onSubmit={onConfirm}
    onCancel={onCancel}
    loading={loading}
    width={Modal.SIZES.SM}
    destroyOnClose
    maskClosable={false}>
    <div className={styles.modalContent}>
      <div className={cx('d-flex align-items-center', styles.contentContainer)}>
        <div className="d-flex justify-content-center align-items-center flex-shrink-0">
          <FontIcon className={styles.warningIcon}>icon-information-filled</FontIcon>
        </div>
        <div className={cx('d-flex flex-column flex-1', styles.textContainer)}>
          <p className={styles.message}>
            {__(
              'This customer will get removed from the exclusion list and thus will get included in the address data refresh services.'
            )}
          </p>
          <p className={styles.message}>{__('Are you sure you want to proceed?')}</p>
        </div>
      </div>
    </div>
  </Modal>
);

RemoveExclusionModal.propTypes = {
  loading: PropTypes.bool,
  onConfirm: PropTypes.func,
  onCancel: PropTypes.func,
};

RemoveExclusionModal.defaultProps = {
  loading: false,
  onConfirm: _noop,
  onCancel: _noop,
};

export default React.memo(RemoveExclusionModal);
