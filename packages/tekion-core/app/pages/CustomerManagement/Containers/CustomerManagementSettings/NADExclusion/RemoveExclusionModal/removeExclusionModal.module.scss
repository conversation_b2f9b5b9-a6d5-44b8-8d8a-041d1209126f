@use "@tekion/tekion-styles-next/scss/component.scss" as component;

.modalContent {
  min-height: 12rem;
}

.contentContainer {
  gap: 1.6rem;
}

.textContainer {
  gap: 0.8rem;
}

.warningIcon {
  font-size: 3.2rem;
  color: component.$goldenBrown;
}

.message {
  font-size: component.$font-size-normal;
  line-height: 1.6;
  color: component.$atomic;
  margin: 0;
  font-family: component.$font-regular;
}
