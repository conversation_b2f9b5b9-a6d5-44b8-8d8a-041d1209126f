import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { ES_REFETCH_DELAY } from '@tekion/tekion-base/constants/general';
import TABLE_ACTION_TYPES from '@tekion/tekion-components/src/organisms/TableManager/constants/actionTypes';

// Mock dependencies
jest.mock('@tekion/tekion-base/utils/getDataFromResponse');
jest.mock('@tekion/tekion-components/src/organisms/NotificationWrapper');
jest.mock('pages/CustomerManagement/customerManagement.api');
jest.mock('actions/exportActions');
jest.mock('../nadExclusion.actions');
jest.mock('../nadExclusion.helpers');

import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import CustomerManagementApi from 'pages/CustomerManagement/customerManagement.api';
import { sendDataForExportAction, getExportDetailsByIdAction } from 'actions/exportActions';
import { includeCustomersAction } from '../nadExclusion.actions';
import { getRequestDTO, getCustomerName } from '../nadExclusion.helpers';
import { ACTION_HANDLERS } from '../nadExclusion.actionHandlers';
import { ACTION_TYPES } from '../nadExclusion.constants';

describe('nadExclusion.actionHandlers', () => {
  let mockGetState;
  let mockSetState;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    mockGetState = jest.fn();
    mockSetState = jest.fn();

    // Default mock implementations
    getDataFromResponse.mockReturnValue({ hits: [], count: 0 });
    CustomerManagementApi.fetchCustomerList.mockResolvedValue({});
    getRequestDTO.mockReturnValue({});
    getCustomerName.mockReturnValue('Test Customer');
    includeCustomersAction.mockResolvedValue(true);
    sendDataForExportAction.mockResolvedValue('export-id-123');
    getExportDetailsByIdAction.mockResolvedValue();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('handleListFetch', () => {
    const handleListFetch = ACTION_HANDLERS[TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH];

    it('should fetch customer list and update state with sorted data', async () => {
      const mockData = {
        hits: [
          { id: '1', firstName: 'John', lastName: 'Doe' },
          { id: '2', firstName: 'Jane', lastName: 'Smith' },
        ],
        count: 2,
      };

      mockGetState.mockReturnValue({
        currentPage: 0,
        pageSize: 20,
        searchText: 'test',
      });

      getDataFromResponse.mockReturnValue(mockData);
      getRequestDTO.mockReturnValue({ searchQuery: 'test' });

      await handleListFetch(null, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ loading: true });
      expect(getRequestDTO).toHaveBeenCalledWith(0, 20, 'test');
      expect(CustomerManagementApi.fetchCustomerList).toHaveBeenCalledWith({ searchQuery: 'test' });
      expect(mockSetState).toHaveBeenCalledWith({
        tableData: mockData.hits,
        totalCount: 2,
        loading: false,
      });
    });

    it('should use default values when state is empty', async () => {
      mockGetState.mockReturnValue({});
      getDataFromResponse.mockReturnValue({ hits: [], count: 0 });

      await handleListFetch(null, { getState: mockGetState, setState: mockSetState });

      expect(getRequestDTO).toHaveBeenCalledWith(0, DEFAULT_PAGE_SIZE, undefined);
    });

    it('should handle empty response data', async () => {
      mockGetState.mockReturnValue({ currentPage: 0, pageSize: 20 });
      getDataFromResponse.mockReturnValue(null);

      await handleListFetch(null, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({
        tableData: EMPTY_ARRAY,
        totalCount: undefined,
        loading: false,
      });
    });

    it('should handle API errors gracefully', async () => {
      mockGetState.mockReturnValue({ currentPage: 0, pageSize: 20 });
      CustomerManagementApi.fetchCustomerList.mockRejectedValue(new Error('API Error'));

      await expect(handleListFetch(null, { getState: mockGetState, setState: mockSetState })).rejects.toThrow(
        'API Error'
      );

      expect(mockSetState).toHaveBeenCalledWith({ loading: true });
    });
  });

  describe('handleAddToExclusion', () => {
    const handleAddToExclusion = ACTION_HANDLERS[ACTION_TYPES.ADD_TO_EXCLUSION];

    it('should toggle showExclusionDrawer state', () => {
      mockGetState.mockReturnValue({ showExclusionDrawer: false });

      handleAddToExclusion(null, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ showExclusionDrawer: true });
    });

    it('should toggle showExclusionDrawer from true to false', () => {
      mockGetState.mockReturnValue({ showExclusionDrawer: true });

      handleAddToExclusion(null, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ showExclusionDrawer: false });
    });
  });

  describe('handleUpdateSearchField', () => {
    const handleUpdateSearchField = ACTION_HANDLERS[ACTION_TYPES.UPDATE_SEARCH_FIELD];

    it('should update search field with provided value', () => {
      const action = { payload: { value: 'fullName' } };

      handleUpdateSearchField(action, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ searchField: 'fullName' });
    });

    it('should handle empty action object', () => {
      handleUpdateSearchField(EMPTY_OBJECT, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ searchField: EMPTY_STRING });
    });

    it('should handle undefined action', () => {
      handleUpdateSearchField(undefined, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ searchField: EMPTY_STRING });
    });
  });

  describe('handleUpdateSearchText', () => {
    const handleUpdateSearchText = ACTION_HANDLERS[ACTION_TYPES.UPDATE_SEARCH_TEXT];

    it('should update search text with provided value', () => {
      const action = { payload: { value: 'John Doe' } };

      handleUpdateSearchText(action, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ searchText: 'John Doe' });
    });

    it('should handle empty action object', () => {
      handleUpdateSearchText(EMPTY_OBJECT, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ searchText: EMPTY_STRING });
    });
  });

  describe('handleTableItemsPageUpdate', () => {
    const handleTableItemsPageUpdate = ACTION_HANDLERS[TABLE_ACTION_TYPES.TABLE_ITEMS_PAGE_UPDATE];

    it('should update pagination and trigger list fetch', async () => {
      const action = { payload: { value: { page: 3, resultsPerPage: 25 } } };
      mockGetState.mockReturnValue({ currentPage: 0, pageSize: 20, searchText: '' });
      getDataFromResponse.mockReturnValue({ hits: [], count: 0 });

      // Mock setState to call the callback immediately
      mockSetState.mockImplementation((state, callback) => {
        if (callback) callback();
      });

      await handleTableItemsPageUpdate(action, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ currentPage: 2, pageSize: 25 }, expect.any(Function));
    });
  });

  describe('handleTableItemSelect', () => {
    const handleTableItemSelect = ACTION_HANDLERS[TABLE_ACTION_TYPES.TABLE_ITEM_SELECT];

    it('should update selected requests', () => {
      const params = { payload: { value: ['id1', 'id2', 'id3'] } };

      handleTableItemSelect(params, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ selectedRequests: ['id1', 'id2', 'id3'] });
    });

    it('should handle empty selection', () => {
      const params = { payload: { value: [] } };

      handleTableItemSelect(params, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ selectedRequests: [] });
    });
  });

  describe('handleRemoveFromExclusion', () => {
    const handleRemoveFromExclusion = ACTION_HANDLERS[ACTION_TYPES.REMOVE_FROM_EXCLUSION];

    it('should set up exclusion modal with single customer ID', () => {
      const action = { payload: { listItem: { id: 'customer-123' } } };

      handleRemoveFromExclusion(action, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({
        idsToExclude: ['customer-123'],
        showExcludeModal: true,
      });
    });

    it('should handle missing listItem', () => {
      const action = { payload: {} };

      handleRemoveFromExclusion(action, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({
        idsToExclude: [undefined],
        showExcludeModal: true,
      });
    });
  });

  describe('handleBulkAction', () => {
    const handleBulkAction = ACTION_HANDLERS[ACTION_TYPES.BULK_ACTION];

    it('should set up exclusion modal with selected requests', () => {
      mockGetState.mockReturnValue({ selectedRequests: ['id1', 'id2', 'id3'] });

      handleBulkAction(null, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({
        idsToExclude: ['id1', 'id2', 'id3'],
        showExcludeModal: true,
      });
    });
  });

  describe('handleCloseExcludeModal', () => {
    const handleCloseExcludeModal = ACTION_HANDLERS[ACTION_TYPES.CLOSE_EXCLUDE_MODAL];

    it('should close the exclude modal', () => {
      handleCloseExcludeModal(null, { setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ showExcludeModal: false });
    });
  });

  describe('handleSearch', () => {
    const handleSearch = ACTION_HANDLERS[ACTION_TYPES.HANDLE_SEARCH];

    it('should update search text and trigger list fetch', async () => {
      const action = { payload: { value: 'John Doe' } };
      mockGetState.mockReturnValue({ currentPage: 0, pageSize: 20, searchText: '' });
      getDataFromResponse.mockReturnValue({ hits: [], count: 0 });

      // Mock setState to call the callback immediately
      mockSetState.mockImplementation((state, callback) => {
        if (callback) callback();
      });

      await handleSearch(action, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ searchText: 'John Doe' }, expect.any(Function));
    });

    it('should handle empty search value', async () => {
      const action = { payload: { value: '' } };
      mockGetState.mockReturnValue({ currentPage: 0, pageSize: 20, searchText: 'previous' });
      getDataFromResponse.mockReturnValue({ hits: [], count: 0 });
      mockSetState.mockImplementation((state, callback) => {
        if (callback) callback();
      });

      await handleSearch(action, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ searchText: '' }, expect.any(Function));
    });
  });

  describe('handleExcludeFromNad', () => {
    const handleExcludeFromNad = ACTION_HANDLERS[ACTION_TYPES.EXCLUDE_FROM_NAD_SYNC];

    it('should successfully exclude customers and update state', async () => {
      mockGetState.mockReturnValue({
        idsToExclude: ['id1', 'id2'],
        selectedRequests: ['id1', 'id2', 'id3'],
      });

      includeCustomersAction.mockResolvedValue(true);

      // Mock setState to call the callback immediately for the final setState
      mockSetState.mockImplementation((state, callback) => {
        if (callback) callback();
      });

      await handleExcludeFromNad(null, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ isExcludeLoading: true });
      expect(includeCustomersAction).toHaveBeenCalledWith(['id1', 'id2']);
      expect(mockSetState).toHaveBeenCalledWith({ selectedRequests: ['id3'] });
      expect(mockSetState).toHaveBeenCalledWith(
        {
          isExcludeLoading: false,
          showExcludeModal: false,
          idsToExclude: EMPTY_ARRAY,
        },
        expect.any(Function)
      );
    });

    it('should handle API failure gracefully', async () => {
      mockGetState.mockReturnValue({
        idsToExclude: ['id1'],
        selectedRequests: ['id1', 'id2'],
      });

      includeCustomersAction.mockResolvedValue(false);
      mockSetState.mockImplementation((state, callback) => {
        if (callback) callback();
      });

      await handleExcludeFromNad(null, { getState: mockGetState, setState: mockSetState });

      expect(mockSetState).toHaveBeenCalledWith({ isExcludeLoading: true });
      expect(mockSetState).toHaveBeenCalledWith(
        {
          isExcludeLoading: false,
          showExcludeModal: false,
          idsToExclude: EMPTY_ARRAY,
        },
        expect.any(Function)
      );
    });

    it('should handle empty idsToExclude', async () => {
      mockGetState.mockReturnValue({
        idsToExclude: [],
        selectedRequests: ['id1', 'id2'],
      });

      mockSetState.mockImplementation((state, callback) => {
        if (callback) callback();
      });

      await handleExcludeFromNad(null, { getState: mockGetState, setState: mockSetState });

      expect(includeCustomersAction).toHaveBeenCalledWith([]);
    });
  });

  describe('handleExcelDownload', () => {
    const handleExcelDownload = ACTION_HANDLERS[ACTION_TYPES.DOWNLOAD_EXCEL];

    beforeEach(() => {
      // Mock MAX_EXCEL_DOWNLOADABLE_CUSTOMER_COUNT
      jest.doMock('constants/constants', () => ({
        MAX_EXCEL_DOWNLOADABLE_CUSTOMER_COUNT: 10000,
      }));
    });

    it('should successfully generate excel report for valid data', async () => {
      mockGetState.mockReturnValue({
        searchText: 'John',
        sortDetails: { firstName: 'ASC', lastName: 'DESC' },
        totalCount: 100,
      });

      await handleExcelDownload(null, { getState: mockGetState });

      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.INFO, __('Please Wait! Generating Excel Sheet'));
      expect(sendDataForExportAction).toHaveBeenCalledWith(
        expect.objectContaining({
          downloadType: expect.any(String),
          exportTemplate: expect.objectContaining({
            name: expect.any(String),
            entityName: expect.any(String),
            exportMetadataId: expect.any(String),
            filters: expect.any(Array),
            sort: expect.any(Array),
            columnDefinitions: expect.any(Array),
          }),
        })
      );
      expect(getExportDetailsByIdAction).toHaveBeenCalledWith('export-id-123', expect.any(String));
    });

    it('should show warning for empty list', async () => {
      mockGetState.mockReturnValue({
        searchText: '',
        sortDetails: {},
        totalCount: 0,
      });

      await handleExcelDownload(null, { getState: mockGetState });

      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.WARN, __('Cannot generate excel report for empty list.'));
      expect(sendDataForExportAction).not.toHaveBeenCalled();
    });

    it('should handle export failure gracefully', async () => {
      mockGetState.mockReturnValue({
        searchText: '',
        sortDetails: {},
        totalCount: 100,
      });

      sendDataForExportAction.mockResolvedValue(null);

      await handleExcelDownload(null, { getState: mockGetState });

      expect(sendDataForExportAction).toHaveBeenCalled();
      expect(getExportDetailsByIdAction).not.toHaveBeenCalled();
    });

    it('should include search text in filters when provided', async () => {
      mockGetState.mockReturnValue({
        searchText: 'John Doe',
        sortDetails: {},
        totalCount: 50,
      });

      await handleExcelDownload(null, { getState: mockGetState });

      const exportCall = sendDataForExportAction.mock.calls[0][0];
      expect(exportCall.exportTemplate.filters).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'searchText',
            operator: expect.any(String),
            values: ['John Doe'],
          }),
        ])
      );
    });

    it('should handle empty sort details', async () => {
      mockGetState.mockReturnValue({
        searchText: '',
        sortDetails: {},
        totalCount: 50,
      });

      await handleExcelDownload(null, { getState: mockGetState });

      const exportCall = sendDataForExportAction.mock.calls[0][0];
      expect(exportCall.exportTemplate.sort).toEqual([]);
    });

    it('should handle negative total count as empty list', async () => {
      mockGetState.mockReturnValue({
        searchText: '',
        sortDetails: {},
        totalCount: -1,
      });

      await handleExcelDownload(null, { getState: mockGetState });

      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.WARN, __('Cannot generate excel report for empty list.'));
    });
  });

  describe('ACTION_HANDLERS object', () => {
    it('should contain all expected action handlers', () => {
      const expectedHandlers = [
        TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH,
        ACTION_TYPES.ADD_TO_EXCLUSION,
        ACTION_TYPES.UPDATE_SEARCH_FIELD,
        ACTION_TYPES.UPDATE_SEARCH_TEXT,
        TABLE_ACTION_TYPES.TABLE_ITEM_SELECT,
        ACTION_TYPES.REMOVE_FROM_EXCLUSION,
        TABLE_ACTION_TYPES.TABLE_ITEMS_PAGE_UPDATE,
        ACTION_TYPES.HANDLE_SEARCH,
        ACTION_TYPES.DOWNLOAD_EXCEL,
        ACTION_TYPES.CLOSE_EXCLUDE_MODAL,
        ACTION_TYPES.EXCLUDE_FROM_NAD_SYNC,
        ACTION_TYPES.BULK_ACTION,
      ];

      expectedHandlers.forEach(actionType => {
        expect(ACTION_HANDLERS).toHaveProperty(actionType);
        expect(typeof ACTION_HANDLERS[actionType]).toBe('function');
      });
    });

    it('should have the correct number of handlers', () => {
      expect(Object.keys(ACTION_HANDLERS)).toHaveLength(12);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete workflow: search -> select -> exclude', async () => {
      // Step 1: Search for customers
      const searchAction = { payload: { value: 'John' } };
      mockGetState.mockReturnValue({ searchText: '', currentPage: 0, pageSize: 20 });
      getDataFromResponse.mockReturnValue({
        hits: [{ id: 'customer1', firstName: 'John', lastName: 'Doe' }],
        count: 1,
      });

      mockSetState.mockImplementation((state, callback) => {
        if (callback) callback();
      });

      await ACTION_HANDLERS[ACTION_TYPES.HANDLE_SEARCH](searchAction, {
        getState: mockGetState,
        setState: mockSetState,
      });

      // Step 2: Select customers
      const selectAction = { payload: { value: ['customer1'] } };
      ACTION_HANDLERS[TABLE_ACTION_TYPES.TABLE_ITEM_SELECT](selectAction, { setState: mockSetState });

      // Step 3: Bulk action to exclude
      mockGetState.mockReturnValue({ selectedRequests: ['customer1'] });
      ACTION_HANDLERS[ACTION_TYPES.BULK_ACTION](null, {
        getState: mockGetState,
        setState: mockSetState,
      });

      // Step 4: Confirm exclusion
      mockGetState.mockReturnValue({
        idsToExclude: ['customer1'],
        selectedRequests: ['customer1'],
      });
      includeCustomersAction.mockResolvedValue(true);

      await ACTION_HANDLERS[ACTION_TYPES.EXCLUDE_FROM_NAD_SYNC](null, {
        getState: mockGetState,
        setState: mockSetState,
      });

      // Verify the complete workflow
      expect(mockSetState).toHaveBeenCalledWith({ searchText: 'John' }, expect.any(Function));
      expect(mockSetState).toHaveBeenCalledWith({ selectedRequests: ['customer1'] });
      expect(mockSetState).toHaveBeenCalledWith({
        idsToExclude: ['customer1'],
        showExcludeModal: true,
      });
      expect(includeCustomersAction).toHaveBeenCalledWith(['customer1']);
    });

    it('should handle error scenarios gracefully', async () => {
      // Test API error in list fetch
      CustomerManagementApi.fetchCustomerList.mockRejectedValue(new Error('Network Error'));
      mockGetState.mockReturnValue({ currentPage: 0, pageSize: 20, searchText: '' });

      await expect(
        ACTION_HANDLERS[TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH](null, {
          getState: mockGetState,
          setState: mockSetState,
        })
      ).rejects.toThrow('Network Error');

      // Test error in exclusion action
      includeCustomersAction.mockRejectedValue(new Error('Exclusion Error'));
      mockGetState.mockReturnValue({
        idsToExclude: ['customer1'],
        selectedRequests: ['customer1'],
      });

      await expect(
        ACTION_HANDLERS[ACTION_TYPES.EXCLUDE_FROM_NAD_SYNC](null, {
          getState: mockGetState,
          setState: mockSetState,
        })
      ).rejects.toThrow('Exclusion Error');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle malformed action payloads', () => {
      const malformedActions = [
        null,
        undefined,
        { payload: null },
        { payload: undefined },
        { payload: { value: null } },
        { payload: { value: undefined } },
      ];

      malformedActions.forEach(action => {
        expect(() => {
          ACTION_HANDLERS[ACTION_TYPES.UPDATE_SEARCH_TEXT](action, { setState: mockSetState });
        }).not.toThrow();

        expect(() => {
          ACTION_HANDLERS[ACTION_TYPES.UPDATE_SEARCH_FIELD](action, { setState: mockSetState });
        }).not.toThrow();
      });
    });

    it('should handle missing getState/setState functions', () => {
      expect(() => {
        ACTION_HANDLERS[ACTION_TYPES.ADD_TO_EXCLUSION](null, {});
      }).toThrow();

      expect(() => {
        ACTION_HANDLERS[ACTION_TYPES.UPDATE_SEARCH_TEXT](null, {});
      }).toThrow();
    });
  });
});
