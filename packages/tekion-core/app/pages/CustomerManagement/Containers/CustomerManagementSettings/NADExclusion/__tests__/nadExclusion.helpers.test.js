// Mock CustomerReader before any imports
jest.mock('@tekion/tekion-base/readers/Customer', () => ({
  companyName: jest.fn(),
  customerType: jest.fn(),
  firstName: jest.fn(),
  lastName: jest.fn(),
  middleName: jest.fn(),
  name: jest.fn(),
  getHomePhoneDetails: jest.fn(),
  getWorkPhoneDetails: jest.fn(),
  getMobilePhoneDetails: jest.fn(),
}));

import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import { CUSTOMER_TYPE } from '@tekion/tekion-base/constants/customer.constants';
import { SORT } from '@tekion/tekion-base/builders/request/Request.constants';
import CustomerReader from '@tekion/tekion-base/readers/Customer';

import { getCustomerName, getRequestDTO, getExcelPayload, getModifiedCustomerList } from '../nadExclusion.helpers';
import { FIELDS_TO_INCLUDE, NAD_FILTERS } from '../nadExclusion.constants';

describe('nadExclusion.helpers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCustomerName', () => {
    const mockCustomer = {
      companyName: 'Test Company',
      customerType: CUSTOMER_TYPE.BUSINESS,
      firstName: 'John',
      lastName: 'Doe',
      middleName: 'Michael',
      name: 'John Doe',
    };

    beforeEach(() => {
      CustomerReader.companyName.mockReturnValue(mockCustomer.companyName);
      CustomerReader.customerType.mockReturnValue(mockCustomer.customerType);
      CustomerReader.firstName.mockReturnValue(mockCustomer.firstName);
      CustomerReader.lastName.mockReturnValue(mockCustomer.lastName);
      CustomerReader.middleName.mockReturnValue(mockCustomer.middleName);
      CustomerReader.name.mockReturnValue(mockCustomer.name);
    });

    it('should return company name for business customers', () => {
      CustomerReader.customerType.mockReturnValue(CUSTOMER_TYPE.BUSINESS);
      CustomerReader.companyName.mockReturnValue('Acme Corporation');

      const result = getCustomerName(mockCustomer);

      expect(result).toBe('Acme Corporation');
      expect(CustomerReader.companyName).toHaveBeenCalledWith(mockCustomer);
      expect(CustomerReader.customerType).toHaveBeenCalledWith(mockCustomer);
    });

    it('should return name field when both firstName and lastName are empty', () => {
      CustomerReader.customerType.mockReturnValue(CUSTOMER_TYPE.PERSONAL);
      CustomerReader.firstName.mockReturnValue('');
      CustomerReader.lastName.mockReturnValue('');
      CustomerReader.name.mockReturnValue('John Smith');

      const result = getCustomerName(mockCustomer);

      expect(result).toBe('John Smith');
    });

    it('should handle empty company name for business customers', () => {
      CustomerReader.customerType.mockReturnValue(CUSTOMER_TYPE.BUSINESS);
      CustomerReader.companyName.mockReturnValue('');

      const result = getCustomerName(mockCustomer);

      expect(result).toBe('');
    });

    it('should handle null values gracefully', () => {
      CustomerReader.customerType.mockReturnValue(CUSTOMER_TYPE.PERSONAL);
      CustomerReader.firstName.mockReturnValue(null);
      CustomerReader.lastName.mockReturnValue(null);
      CustomerReader.middleName.mockReturnValue(null);
      CustomerReader.name.mockReturnValue(null);

      const result = getCustomerName(mockCustomer);

      expect(result).toBe('');
    });

    it('should handle only first name', () => {
      CustomerReader.customerType.mockReturnValue(CUSTOMER_TYPE.PERSONAL);
      CustomerReader.firstName.mockReturnValue('John');
      CustomerReader.lastName.mockReturnValue('');
      CustomerReader.middleName.mockReturnValue('');

      const result = getCustomerName(mockCustomer);

      expect(result).toBe('John');
    });

    it('should handle only last name', () => {
      CustomerReader.customerType.mockReturnValue(CUSTOMER_TYPE.PERSONAL);
      CustomerReader.firstName.mockReturnValue('');
      CustomerReader.lastName.mockReturnValue('Doe');
      CustomerReader.middleName.mockReturnValue('');

      const result = getCustomerName(mockCustomer);

      expect(result).toBe('Doe');
    });
  });

  describe('getRequestDTO', () => {
    it('should return correct DTO with all required fields', () => {
      const currentPage = 0;
      const pageSize = 20;
      const searchText = 'test search';

      const result = getRequestDTO(currentPage, pageSize, searchText);

      expect(result).toEqual({
        searchQuery: searchText,
        sort: [
          {
            field: 'modifiedTime',
            order: SORT.DESC,
          },
        ],
        includeFields: FIELDS_TO_INCLUDE,
        pageInfo: {
          start: 0,
          rows: 20,
        },
        advancedSearchDTO: {},
        searchText,
        filters: NAD_FILTERS,
      });
    });

    it('should calculate correct start position for pagination', () => {
      const currentPage = 2;
      const pageSize = 25;

      const result = getRequestDTO(currentPage, pageSize);

      expect(result.pageInfo.start).toBe(50); // 2 * 25
      expect(result.pageInfo.rows).toBe(25);
    });

    it('should use DEFAULT_PAGE_SIZE when pageSize is undefined', () => {
      const result = getRequestDTO(0, undefined);

      expect(result.pageInfo.rows).toBe(DEFAULT_PAGE_SIZE);
    });

    it('should handle zero page size by using DEFAULT_PAGE_SIZE', () => {
      const result = getRequestDTO(0, 0);

      expect(result.pageInfo.rows).toBe(DEFAULT_PAGE_SIZE);
    });

    it('should handle large page numbers correctly', () => {
      const currentPage = 100;
      const pageSize = 50;

      const result = getRequestDTO(currentPage, pageSize);

      expect(result.pageInfo.start).toBe(5000); // 100 * 50
    });

    it('should include all required fields in includeFields', () => {
      const result = getRequestDTO(0, 20);

      expect(result.includeFields).toBe(FIELDS_TO_INCLUDE);
      expect(Array.isArray(result.includeFields)).toBe(true);
    });

    it('should include NAD filters', () => {
      const result = getRequestDTO(0, 20);

      expect(result.filters).toBe(NAD_FILTERS);
      expect(Array.isArray(result.filters)).toBe(true);
    });

    it('should always sort by modifiedTime in DESC order', () => {
      const result = getRequestDTO(0, 20);

      expect(result.sort).toEqual([
        {
          field: 'modifiedTime',
          order: SORT.DESC,
        },
      ]);
    });

    it('should include empty advancedSearchDTO', () => {
      const result = getRequestDTO(0, 20);

      expect(result.advancedSearchDTO).toEqual({});
    });

    it('should handle empty string search text', () => {
      const result = getRequestDTO(0, 20, '');

      expect(result.searchQuery).toBe('');
      expect(result.searchText).toBe('');
    });

    it('should handle whitespace search text', () => {
      const searchText = '   test   ';
      const result = getRequestDTO(0, 20, searchText);

      expect(result.searchQuery).toBe(searchText);
      expect(result.searchText).toBe(searchText);
    });

    it('should handle special characters in search text', () => {
      const searchText = 'test@#$%^&*()';
      const result = getRequestDTO(0, 20, searchText);

      expect(result.searchQuery).toBe(searchText);
      expect(result.searchText).toBe(searchText);
    });

    it('should handle unicode characters in search text', () => {
      const searchText = 'tëst 测试 тест';
      const result = getRequestDTO(0, 20, searchText);

      expect(result.searchQuery).toBe(searchText);
      expect(result.searchText).toBe(searchText);
    });
  });

  describe('getExcelPayload', () => {
    it('should return correct payload structure with all required fields', () => {
      const sortDetails = { firstName: 'ASC', lastName: 'DESC' };
      const searchText = 'John Doe';

      const result = getExcelPayload(sortDetails, searchText);

      expect(result).toEqual({
        exportTemplate: {
          name: expect.any(String), // NAD_REPORT_FILE_NAME
          entityName: expect.any(String), // RESOURCE_TYPE.CUSTOMER
          exportMetadataId: expect.any(String), // CUSTOMER_REPORT_METADATA_ID
          filters: expect.arrayContaining([
            expect.objectContaining({
              field: 'searchText',
              operator: expect.any(String),
              values: [searchText],
            }),
          ]),
          sort: [
            { field: 'firstName', order: 'ASC' },
            { field: 'lastName', order: 'DESC' },
          ],
          columnDefinitions: expect.any(Array),
        },
        downloadType: expect.any(String), // DOWNLOAD_FORMATS.EXCEL_FORMAT
      });
    });

    it('should handle empty sort details', () => {
      const result = getExcelPayload({}, 'test');

      expect(result.exportTemplate.sort).toEqual([]);
    });

    it('should handle undefined sort details', () => {
      const result = getExcelPayload(undefined, 'test');

      expect(result.exportTemplate.sort).toEqual([]);
    });

    it('should include search filter when searchText is provided', () => {
      const searchText = 'John Smith';
      const result = getExcelPayload({}, searchText);

      const searchFilter = result.exportTemplate.filters.find(filter => filter.field === 'searchText');

      expect(searchFilter).toEqual({
        field: 'searchText',
        operator: expect.any(String),
        values: [searchText],
      });
    });

    it('should not include search filter when searchText is empty', () => {
      const result = getExcelPayload({}, '');

      const searchFilter = result.exportTemplate.filters.find(filter => filter.field === 'searchText');

      expect(searchFilter).toBeUndefined();
    });

    it('should not include search filter when searchText is not provided', () => {
      const result = getExcelPayload({});

      const searchFilter = result.exportTemplate.filters.find(filter => filter.field === 'searchText');

      expect(searchFilter).toBeUndefined();
    });

    it('should generate correct column definitions structure', () => {
      const result = getExcelPayload({}, 'test');

      expect(Array.isArray(result.exportTemplate.columnDefinitions)).toBe(true);

      if (result.exportTemplate.columnDefinitions.length > 0) {
        const firstColumn = result.exportTemplate.columnDefinitions[0];
        expect(firstColumn).toEqual({
          columnType: expect.any(String),
          fieldName: expect.any(String),
          defaultValue: '',
          displayName: expect.any(String),
          order: 1,
        });
      }
    });

    it('should handle complex sort details with multiple fields', () => {
      const sortDetails = {
        firstName: 'ASC',
        lastName: 'DESC',
        email: 'ASC',
        createdTime: 'DESC',
      };

      const result = getExcelPayload(sortDetails, 'test');

      expect(result.exportTemplate.sort).toEqual([
        { field: 'firstName', order: 'ASC' },
        { field: 'lastName', order: 'DESC' },
        { field: 'email', order: 'ASC' },
        { field: 'createdTime', order: 'DESC' },
      ]);
    });

    it('should handle special characters in search text', () => {
      const searchText = 'test@#$%^&*()';
      const result = getExcelPayload({}, searchText);

      const searchFilter = result.exportTemplate.filters.find(filter => filter.field === 'searchText');

      expect(searchFilter.values).toEqual([searchText]);
    });

    it('should handle unicode characters in search text', () => {
      const searchText = 'tëst 测试 тест';
      const result = getExcelPayload({}, searchText);

      const searchFilter = result.exportTemplate.filters.find(filter => filter.field === 'searchText');

      expect(searchFilter.values).toEqual([searchText]);
    });

    it('should always include base NAD_FILTERS', () => {
      const result = getExcelPayload({}, 'test');

      expect(Array.isArray(result.exportTemplate.filters)).toBe(true);
      expect(result.exportTemplate.filters.length).toBeGreaterThan(0);
    });

    it('should maintain column order in columnDefinitions', () => {
      const result = getExcelPayload({}, 'test');

      result.exportTemplate.columnDefinitions.forEach((column, index) => {
        expect(column.order).toBe(index + 1);
      });
    });
  });

  describe('getModifiedCustomerList', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should add new customer when not already present', () => {
      const action = {
        payload: {
          value: ['customer1'],
          id: undefined,
        },
      };
      const customerSearch = [{ value: ['existing1'] }, { isLastRow: true }];

      const result = getModifiedCustomerList(action, customerSearch);

      expect(result).toEqual([{ value: ['existing1'] }, { value: ['customer1'] }, { isLastRow: true }]);
    });

    it('should update existing customer when id is provided', () => {
      const action = {
        payload: {
          value: ['updated_customer'],
          id: 'existing1',
        },
      };
      const customerSearch = [{ value: ['existing1'] }, { value: ['existing2'] }, { isLastRow: true }];

      const result = getModifiedCustomerList(action, customerSearch);

      expect(result).toEqual([{ value: ['updated_customer'] }, { value: ['existing2'] }, { isLastRow: true }]);
    });

    it('should not add duplicate customer', () => {
      const action = {
        payload: {
          value: ['existing1'],
          id: undefined,
        },
      };
      const customerSearch = [{ value: ['existing1'] }, { value: ['existing2'] }, { isLastRow: true }];

      const result = getModifiedCustomerList(action, customerSearch);

      expect(result).toEqual([{ value: ['existing1'] }, { value: ['existing2'] }, { isLastRow: true }]);
    });

    it('should handle empty action object', () => {
      const customerSearch = [{ value: ['existing1'] }, { isLastRow: true }];

      const result = getModifiedCustomerList({}, customerSearch);

      expect(result).toEqual([{ value: ['existing1'] }, { isLastRow: true }]);
    });

    it('should handle undefined action', () => {
      const customerSearch = [{ value: ['existing1'] }, { isLastRow: true }];

      const result = getModifiedCustomerList(undefined, customerSearch);

      expect(result).toEqual([{ value: ['existing1'] }, { isLastRow: true }]);
    });

    it('should handle empty customerSearch array', () => {
      const action = {
        payload: {
          value: ['customer1'],
          id: undefined,
        },
      };

      const result = getModifiedCustomerList(action, []);

      expect(result).toEqual([{ value: ['customer1'] }, { isLastRow: true }]);
    });

    it('should handle undefined customerSearch', () => {
      const action = {
        payload: {
          value: ['customer1'],
          id: undefined,
        },
      };

      const result = getModifiedCustomerList(action, undefined);

      expect(result).toEqual([{ value: ['customer1'] }, { isLastRow: true }]);
    });

    it('should filter out isLastRow entries before processing', () => {
      const action = {
        payload: {
          value: ['customer1'],
          id: undefined,
        },
      };
      const customerSearch = [
        { value: ['existing1'] },
        { isLastRow: true },
        { value: ['existing2'] },
        { isLastRow: true },
      ];

      const result = getModifiedCustomerList(action, customerSearch);

      expect(result).toEqual([
        { value: ['existing1'] },
        { value: ['existing2'] },
        { value: ['customer1'] },
        { isLastRow: true },
      ]);
    });

    it('should always add isLastRow at the end', () => {
      const action = {
        payload: {
          value: ['customer1'],
          id: undefined,
        },
      };
      const customerSearch = [];

      const result = getModifiedCustomerList(action, customerSearch);

      expect(result[result.length - 1]).toEqual({ isLastRow: true });
    });

    it('should handle complex customer values', () => {
      const action = {
        payload: {
          value: [{ id: 'customer1', name: 'John Doe' }],
          id: undefined,
        },
      };
      const customerSearch = [{ value: [{ id: 'existing1', name: 'Jane Smith' }] }, { isLastRow: true }];

      const result = getModifiedCustomerList(action, customerSearch);

      expect(result).toEqual([
        { value: [{ id: 'existing1', name: 'Jane Smith' }] },
        { value: [{ id: 'customer1', name: 'John Doe' }] },
        { isLastRow: true },
      ]);
    });

    it('should update customer at correct index when id matches', () => {
      const action = {
        payload: {
          value: ['updated_customer'],
          id: 'existing2',
        },
      };
      const customerSearch = [
        { value: ['existing1'] },
        { value: ['existing2'] },
        { value: ['existing3'] },
        { isLastRow: true },
      ];

      const result = getModifiedCustomerList(action, customerSearch);

      expect(result).toEqual([
        { value: ['existing1'] },
        { value: ['updated_customer'] },
        { value: ['existing3'] },
        { isLastRow: true },
      ]);
    });

    it('should handle case when id is not found in customerSearch', () => {
      const action = {
        payload: {
          value: ['new_customer'],
          id: 'non_existent_id',
        },
      };
      const customerSearch = [{ value: ['existing1'] }, { value: ['existing2'] }, { isLastRow: true }];

      const result = getModifiedCustomerList(action, customerSearch);

      // When id is not found, _findIndex returns -1, updateElementAtIndex should handle this
      expect(result).toEqual([{ value: ['existing1'] }, { value: ['existing2'] }, { isLastRow: true }]);
    });
  });
});
