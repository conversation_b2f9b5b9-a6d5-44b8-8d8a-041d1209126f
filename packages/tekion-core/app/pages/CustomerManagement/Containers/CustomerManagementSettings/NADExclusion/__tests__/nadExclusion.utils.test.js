// Mock dependencies
jest.mock('@tekion/tekion-components/src/molecules/Menu', () => {
  const MockMenuItem = jest.fn();
  const MockMenu = jest.fn();

  // Attach Item as a property to MockMenu
  MockMenu.Item = MockMenuItem;

  return MockMenu;
});

import React from 'react';

jest.mock('../nadExclusion.constants', () => ({
  BULK_ACTION_OPTIONS: [
    {
      label: 'Remove from Exclusions',
      value: 'INCLUDE_IN_SYNC',
    },
    {
      label: 'Test Action',
      value: 'TEST_ACTION',
    },
  ],
}));

import Menu from '@tekion/tekion-components/src/molecules/Menu';
import { getBulkActionsMenu } from '../nadExclusion.utils';
import { BULK_ACTION_OPTIONS } from '../nadExclusion.constants';

describe('nadExclusion.utils', () => {
  describe('getBulkActionsMenu', () => {
    let mockHandleAction;

    beforeEach(() => {
      mockHandleAction = jest.fn();
      jest.clearAllMocks();
    });

    it('should return a React element', () => {
      const menu = getBulkActionsMenu(mockHandleAction);

      expect(React.isValidElement(menu)).toBe(true);
      expect(menu.type).toBe(Menu);
    });

    it('should pass handleAction to Menu onClick prop', () => {
      const menu = getBulkActionsMenu(mockHandleAction);

      expect(menu.props.onClick).toBe(mockHandleAction);
    });

    it('should render Menu with correct number of children', () => {
      const menu = getBulkActionsMenu(mockHandleAction);

      expect(menu.props.children).toHaveLength(BULK_ACTION_OPTIONS.length);
      expect(menu.props.children).toHaveLength(2); // Based on our mock
    });

    it('should create Menu.Item for each bulk action option', () => {
      const menu = getBulkActionsMenu(mockHandleAction);
      const { children } = menu.props;

      expect(Array.isArray(children)).toBe(true);
      expect(children).toHaveLength(2);

      children.forEach((child, index) => {
        expect(React.isValidElement(child)).toBe(true);
        expect(child.type).toBe(Menu.Item);
        expect(child.key).toBe(BULK_ACTION_OPTIONS[index].value);
        expect(child.props.children).toBe(BULK_ACTION_OPTIONS[index].label);
      });
    });

    it('should render Menu.Item with correct labels', () => {
      const menu = getBulkActionsMenu(mockHandleAction);
      const { children } = menu.props;

      expect(children[0].props.children).toBe('Remove from Exclusions');
      expect(children[1].props.children).toBe('Test Action');
    });

    it('should set correct key prop for each Menu.Item', () => {
      const menu = getBulkActionsMenu(mockHandleAction);
      const { children } = menu.props;

      expect(children[0].key).toBe('INCLUDE_IN_SYNC');
      expect(children[1].key).toBe('TEST_ACTION');
    });

    it('should handle undefined handleAction gracefully', () => {
      const menu = getBulkActionsMenu(undefined);

      expect(React.isValidElement(menu)).toBe(true);
      expect(menu.props.onClick).toBeUndefined();
    });

    it('should handle null handleAction gracefully', () => {
      const menu = getBulkActionsMenu(null);

      expect(React.isValidElement(menu)).toBe(true);
      expect(menu.props.onClick).toBeNull();
    });

    it('should maintain function signature and return type', () => {
      expect(typeof getBulkActionsMenu).toBe('function');

      const menu = getBulkActionsMenu(mockHandleAction);
      expect(React.isValidElement(menu)).toBe(true);
      expect(menu.type).toBe(Menu);
    });

    it('should work with the current BULK_ACTION_OPTIONS structure', () => {
      const menu = getBulkActionsMenu(mockHandleAction);

      expect(React.isValidElement(menu)).toBe(true);
      expect(menu.type).toBe(Menu);
      expect(menu.props.onClick).toBe(mockHandleAction);
      expect(Array.isArray(menu.props.children)).toBe(true);
      expect(menu.props.children.length).toBeGreaterThan(0);
    });

    it('should handle different types of handleAction functions', () => {
      const asyncHandleAction = async () => Promise.resolve('async result');
      const arrowFunction = () => 'arrow function result';
      const namedFunction = function namedHandler() {
        return 'named function';
      };

      // Test with async function
      expect(() => {
        const menu = getBulkActionsMenu(asyncHandleAction);
        expect(React.isValidElement(menu)).toBe(true);
      }).not.toThrow();

      // Test with arrow function
      expect(() => {
        const menu = getBulkActionsMenu(arrowFunction);
        expect(React.isValidElement(menu)).toBe(true);
      }).not.toThrow();

      // Test with named function
      expect(() => {
        const menu = getBulkActionsMenu(namedFunction);
        expect(React.isValidElement(menu)).toBe(true);
      }).not.toThrow();
    });

    it('should maintain referential stability when called multiple times with same handler', () => {
      const menu1 = getBulkActionsMenu(mockHandleAction);
      const menu2 = getBulkActionsMenu(mockHandleAction);

      // The function creates new elements each time, so they won't be referentially equal
      // But they should have the same structure
      expect(menu1.type).toBe(menu2.type);
      expect(menu1.props.onClick).toBe(menu2.props.onClick);
    });

    it('should handle function calls with consistent behavior', () => {
      const menu1 = getBulkActionsMenu(mockHandleAction);
      const menu2 = getBulkActionsMenu(mockHandleAction);

      // Both should be valid React elements
      expect(React.isValidElement(menu1)).toBe(true);
      expect(React.isValidElement(menu2)).toBe(true);

      // Both should have the same number of children
      expect(menu1.props.children.length).toBe(menu2.props.children.length);
    });
  });
});
