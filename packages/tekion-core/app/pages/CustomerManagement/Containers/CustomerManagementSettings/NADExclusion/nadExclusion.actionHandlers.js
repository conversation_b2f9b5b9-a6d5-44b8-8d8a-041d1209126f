import _castArray from 'lodash/castArray';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _take from 'lodash/take';
import _get from 'lodash/get';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import { tget } from '@tekion/tekion-base/utils/general';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import { ES_REFETCH_DELAY } from '@tekion/tekion-base/constants/general';

import TABLE_ACTION_TYPES from '@tekion/tekion-components/src/organisms/TableManager/constants/actionTypes';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import CustomerManagementApi from 'pages/CustomerManagement/customerManagement.api';

import { sendDataForExportAction, getExportDetailsByIdAction } from 'actions/exportActions';
import { MAX_EXCEL_DOWNLOADABLE_CUSTOMER_COUNT } from 'constants/constants';

import { ACTION_TYPES, MAXIMUM_ADDRESS_TO_INCLUDE_AT_ONCE, NAD_REPORT_FILE_NAME } from './nadExclusion.constants';
import { includeCustomersAction } from './nadExclusion.actions';
import { getRequestDTO, getExcelPayload } from './nadExclusion.helpers';

const handleListFetch = async (_, { getState, setState }) => {
  setState({ loading: true });
  const { currentPage = 0, pageSize = DEFAULT_PAGE_SIZE, searchText } = getState();
  const payloadSearch = getRequestDTO(currentPage, pageSize, searchText);
  const response = await CustomerManagementApi.fetchCustomerList(payloadSearch);
  const data = getDataFromResponse(response);
  const tableData = data?.hits || EMPTY_ARRAY;
  setState({
    tableData,
    totalCount: data?.count,
    loading: false,
  });
};

const handleListFetchWithDelay = (_, { setState, getState }) =>
  setTimeout(() => handleListFetch(_, { setState, getState }), ES_REFETCH_DELAY);

const handleAddToExclusion = (_, { getState, setState }) => {
  const { showExclusionDrawer } = getState();
  setState({ showExclusionDrawer: !showExclusionDrawer });
};

const handleUpdateSearchField = (action = EMPTY_OBJECT, { setState }) => {
  const value = tget(action, 'payload.value', EMPTY_STRING);
  // Need to add list fetch here, once BE adds the support
  setState({ searchField: value });
};

const handleUpdateSearchText = (action = EMPTY_OBJECT, { setState }) => {
  const value = tget(action, 'payload.value', EMPTY_STRING);
  setState({ searchText: value });
};

const handleTableItemsPageUpdate = (action = EMPTY_OBJECT, { getState, setState }) => {
  const { page, resultsPerPage } = tget(action, 'payload.value', EMPTY_OBJECT);
  setState({ currentPage: page - 1, pageSize: resultsPerPage }, () => {
    handleListFetch(null, { getState, setState });
  });
};

const handleTableItemSelect = (params, { setState }) => {
  const selectedRequests = tget(params, 'payload.value', EMPTY_ARRAY);
  setState({ selectedRequests });
};

const handleRemoveFromExclusion = (action = EMPTY_OBJECT, { setState }) => {
  const value = tget(action, 'payload.listItem', EMPTY_OBJECT);
  const { id } = value;
  setState({ idsToExclude: _castArray(id), showExcludeModal: true });
};

const handleBulkAction = (_, { getState, setState }) => {
  const { selectedRequests } = getState();
  setState({ idsToExclude: selectedRequests, showExcludeModal: true });
};

const handleCloseExcludeModal = (_, { setState }) => {
  setState({ showExcludeModal: false });
};

const handleSearch = (action = EMPTY_OBJECT, { getState, setState }) => {
  const value = tget(action, 'payload.value', EMPTY_STRING);
  setState({ searchText: value }, () => handleListFetch(action, { getState, setState }));
};

const handleExcludeFromNad = async (_, { getState, setState }) => {
  setState({ isExcludeLoading: true });
  const { idsToExclude, selectedRequests } = getState();
  const response = await includeCustomersAction(idsToExclude);
  if (response) {
    const updatedRequests = _filter(selectedRequests, item => !_includes(idsToExclude, item));
    setState({ selectedRequests: updatedRequests });
  }
  setState({ isExcludeLoading: false, showExcludeModal: false, idsToExclude: EMPTY_ARRAY }, () =>
    handleListFetchWithDelay(_, { getState, setState })
  );
};

const handleExcelDownload = async (_, { getState }) => {
  const { searchText, sortDetails = EMPTY_OBJECT, totalCount } = getState();
  if (totalCount > MAX_EXCEL_DOWNLOADABLE_CUSTOMER_COUNT) {
    toaster(
      TOASTER_TYPE.WARN,
      __('Cannot generate excel report for more than {{maxExcelDownloadableCustCount}} Customers', {
        maxExcelDownloadableCustCount: MAX_EXCEL_DOWNLOADABLE_CUSTOMER_COUNT,
      })
    );
  } else if (totalCount <= 0) {
    toaster(TOASTER_TYPE.WARN, __('Cannot generate excel report for empty list.'));
  } else {
    toaster(TOASTER_TYPE.INFO, __('Please Wait! Generating Excel Sheet'));
    const payload = getExcelPayload(sortDetails, searchText);
    const exportId = await sendDataForExportAction(payload);
    if (!exportId) return;
    await getExportDetailsByIdAction(exportId, NAD_REPORT_FILE_NAME);
  }
};

const handleCustomerSelect = (action = EMPTY_OBJECT, { setState }) => {
  const selectedRequests = _get(action, 'payload', EMPTY_OBJECT);
  setState({ selectedRequests: _take(selectedRequests, MAXIMUM_ADDRESS_TO_INCLUDE_AT_ONCE) });
};

export const ACTION_HANDLERS = {
  [TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH]: handleListFetch,
  [ACTION_TYPES.ADD_TO_EXCLUSION]: handleAddToExclusion,
  [ACTION_TYPES.UPDATE_SEARCH_FIELD]: handleUpdateSearchField,
  [ACTION_TYPES.UPDATE_SEARCH_TEXT]: handleUpdateSearchText,
  [TABLE_ACTION_TYPES.TABLE_ITEM_SELECT]: handleTableItemSelect,
  [ACTION_TYPES.REMOVE_FROM_EXCLUSION]: handleRemoveFromExclusion,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_PAGE_UPDATE]: handleTableItemsPageUpdate,
  [ACTION_TYPES.HANDLE_SEARCH]: handleSearch,
  [ACTION_TYPES.DOWNLOAD_EXCEL]: handleExcelDownload,
  [ACTION_TYPES.CLOSE_EXCLUDE_MODAL]: handleCloseExcludeModal,
  [ACTION_TYPES.EXCLUDE_FROM_NAD_SYNC]: handleExcludeFromNad,
  [ACTION_TYPES.BULK_ACTION]: handleBulkAction,
  [ACTION_TYPES.CUSTOMER_SELECT]: handleCustomerSelect,
};
