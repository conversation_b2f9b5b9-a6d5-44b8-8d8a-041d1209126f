import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { excludeCustomersFromNADSync, includeCustomersInNADSync } from 'services/nadSyncService';

export const excludeCustomersAction = async payload => {
  try {
    await excludeCustomersFromNADSync(payload);
    toaster(
      TOASTER_TYPE.SUCCESS,
      __(
        "Selected customers have been added to exclusion list, and thus these records won't get processed in address data refresh services"
      ),
      undefined,
      __('Added to Exclusion List')
    );
    return true;
  } catch (e) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(e));
    return false;
  }
};

export const includeCustomersAction = async payload => {
  try {
    await includeCustomersInNADSync(payload);
    toaster(
      TOASTER_TYPE.SUCCESS,

      __(
        'Selected customers have been removed from exclusion list, and thus these records will now get processed in address data refresh services'
      ),
      undefined,
      __('Removed from Exclusion List')
    );
    return true;
  } catch (e) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(e));
    return false;
  }
};
