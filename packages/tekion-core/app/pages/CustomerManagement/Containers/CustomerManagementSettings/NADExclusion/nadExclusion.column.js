import makeCellRenderer from '@tekion/tekion-components/src/molecules/CellRenderers/makeCellRenderer';

import AddressRenderer from '@tekion/tekion-widgets/src/organisms/MergeCustomersList/cellRenderers/AddressRenderer';

import CustomerTypeCellRenderer from './CellRenderers/CustomerTypeCellRenderer';
import CustomerNameCellRenderer from './CellRenderers/CustomerNameCellRenderer';
import { COLUMN_IDS } from './nadExclusion.constants';

const CustomerTypeRenderer = makeCellRenderer(CustomerTypeCellRenderer);
const CustomerNameRenderer = makeCellRenderer(CustomerNameCellRenderer);

export const COLUMN_CONFIG_FOR_NDC_EXCLUSION_SYNC = [
  {
    key: COLUMN_IDS.CUSTOMER_NAME,
    id: COLUMN_IDS.CUSTOMER_NAME,
    accessor: COLUMN_IDS.CUSTOMER_NAME,
    width: 200,
    Header: __('Full Name'),
    Cell: CustomerNameRenderer,
  },
  {
    key: COLUMN_IDS.CUSTOMER_TYPE,
    id: COLUMN_IDS.CUSTOMER_TYPE,
    width: 200,
    accessor: COLUMN_IDS.CUSTOMER_TYPE,
    Header: __('Type of Customer'),
    Cell: CustomerTypeRenderer,
  },
  {
    key: COLUMN_IDS.BILLING_ADDRESS,
    id: COLUMN_IDS.BILLING_ADDRESS,
    accessor: COLUMN_IDS.BILLING_ADDRESS,
    Header: __('Billing Address'),
    Cell: AddressRenderer,
  },
  {
    key: COLUMN_IDS.SHIPPING_ADDRESS,
    id: COLUMN_IDS.SHIPPING_ADDRESS,
    accessor: COLUMN_IDS.SHIPPING_ADDRESS,
    Header: __('Shipping Address'),
    Cell: AddressRenderer,
  },
];
