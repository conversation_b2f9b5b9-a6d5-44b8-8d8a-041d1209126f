import React from 'react';
import _join from 'lodash/join';

import { NO_DATA, SINGLE_SPACE } from '@tekion/tekion-base/app.constants';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';

import IconWithLabel from '@tekion/tekion-components/src/molecules/IconWithLabel';
import COLORS from '@tekion/tekion-styles-next/scss/exports.scss';
import RowActionComponent from 'pages/CustomerManagement/Components/RowActionComponent';

import variables from 'tstyles/exports.scss';

export const COLUMN_IDS = {
  CUSTOMER_NAME: 'customerName',
  CUSTOMER_TYPE: 'customerType',
  BILLING_ADDRESS: 'billingAddress',
  SHIPPING_ADDRESS: 'shippingAddress',
};

export const TABLE_MANAGER_PROPS_FOR_NAD_EXCLUSION = {
  isSearchActive: true,
  showSearch: false,
  showFilter: false,
  showHeader: false,
  showSubHeader: true,
};

export const TABLE_MANAGER_PROPS_FOR_NAD_EXCLUSION_DRAWER = {
  showSearch: false,
  showFilter: false,
  showHeader: false,
  showSubHeader: false,
};

export const SUB_HEADER_FIELDS = {
  ADD_TO_EXCLUSION_CTA: __('Add to exclusion'),
};

export const BULK_ACTIONS = {
  INCLUDE_IN_SYNC: 'INCLUDE_IN_SYNC',
};

export const BULK_ACTION_OPTIONS = [
  {
    label: __('Remove from Exclusions'),
    value: BULK_ACTIONS.INCLUDE_IN_SYNC,
  },
];

export const ADD_TO_EXCLUSION_DRAWER = {
  HEADER: SUB_HEADER_FIELDS.ADD_TO_EXCLUSION_CTA,
  DESCRIPTION: __(
    'Choose customers to exclude from synchronisation. This exclusion only affects their USPS-compliant addresses. To resume syncing, remove a customer from the exclusion list.'
  ),
  WIDTH: 1016,
  DRAWER_BODY_STYLES: {
    padding: '0',
    height: '100%',
  },
};

export const CUSTOMER_ID_COLUMN_WIDTH = '24';
export const CONTACT_COLUMN_WIDTH = '12';
export const ADDRESS_COLUMN_WIDTH = '24';
export const VEHICLE_COLUMN_WIDTH = '32';

export const CUSTOMER_SEARCH_MENU_OPTION_STYLES = {
  customer: {
    width: `${CUSTOMER_ID_COLUMN_WIDTH}rem`,
  },
  contact: {
    width: `${CONTACT_COLUMN_WIDTH}rem`,
  },
  address: {
    width: `${ADDRESS_COLUMN_WIDTH}rem`,
  },
  vehicle: {
    width: `${VEHICLE_COLUMN_WIDTH}rem`,
  },
};

export const CUSTOMER_HEADER_LABELS = {
  NAME_ID: _join([__('ID'), NO_DATA, __('Customer'), __('Name')], SINGLE_SPACE),
  CONTACT: __('Contact'),
  ADDRESS: __('Address'),
  VEHICLE: __('Vehicle'),
};

export const CUSTOMER_SEARCH_HEADER_SECTION = [
  {
    label: (
      <IconWithLabel
        label={CUSTOMER_HEADER_LABELS.NAME_ID}
        icon="icon-customer-profile"
        iconColor={COLORS.black}
        bodyClassName="d-flex align-items-center"
      />
    ),
    width: CUSTOMER_ID_COLUMN_WIDTH,
  },
  {
    label: (
      <IconWithLabel
        label={CUSTOMER_HEADER_LABELS.CONTACT}
        icon="icon-call"
        iconColor={COLORS.black}
        bodyClassName="d-flex align-items-center"
      />
    ),
    width: CONTACT_COLUMN_WIDTH,
  },
  {
    label: (
      <IconWithLabel
        label={CUSTOMER_HEADER_LABELS.ADDRESS}
        icon="icon-address"
        iconColor={COLORS.black}
        bodyClassName="d-flex align-items-center"
      />
    ),
    width: ADDRESS_COLUMN_WIDTH,
  },
  {
    label: (
      <IconWithLabel
        label={CUSTOMER_HEADER_LABELS.VEHICLE}
        icon="icon-vehicle2"
        iconColor={COLORS.black}
        bodyClassName="d-flex align-items-center"
      />
    ),
    width: VEHICLE_COLUMN_WIDTH,
  },
];

export const CUSTOMER_SEARCH_OPTION_STYLES = {
  borderBottom: `0.1rem solid ${variables.platinum}`,
  padding: '1.2rem 2rem',
  display: 'flex',
};

export const MENU_HEADER_STYLES = { padding: '1.2rem' };

export const MENU_STYLES = { width: '96rem' };

export const ACTION_TYPES = {
  ADD_TO_EXCLUSION: 'ADD_TO_EXCLUSION',
  UPDATE_SEARCH_TEXT: 'UPDATE_SEARCH_TEXT',
  UPDATE_SEARCH_FIELD: 'UPDATE_SEARCH_FIELD',
  REMOVE_FROM_EXCLUSION: 'REMOVE_FROM_EXCLUSION',
  DOWNLOAD_EXCEL: 'DOWNLOAD_EXCEL',
  HANDLE_SEARCH: 'HANDLE_SEARCH',
  CLOSE_EXCLUDE_MODAL: 'CLOSE_EXCLUDE_MODAL',
  EXCLUDE_FROM_NAD_SYNC: 'EXCLUDE_FROM_NAD_SYNC',
  BULK_ACTION: 'BULK_ACTION',
  CUSTOMER_SELECT: 'CUSTOMER_SELECT',
};

export const REMOVE_FROM_EXCLUSION = [
  {
    id: ACTION_TYPES.REMOVE_FROM_EXCLUSION,
    name: <RowActionComponent label={__('Remove from Exclusions')} />,
    isEnabled: true,
  },
];

export const FIELDS_TO_INCLUDE = [
  'id',
  'displayId',
  'firstName',
  'middleName',
  'lastName',
  'businessName',
  'companyName',
  'addresses',
  'searchDetail',
  'stopNADVerification',
  'accountingInfo',
  'customerType',
];

export const NAD_REPORT_FILE_NAME = __('NAD Exclusion Report');

export const NAD_COLUMNS = [
  {
    key: 'fullName',
    displayName: __('Customer'),
  },
  {
    key: 'customerTypeForDisplay',
    displayName: __('Customer Type'),
  },
  {
    key: 'chargeCustomer',
    displayName: __('Charge Customer'),
  },
  {
    key: 'address',
    displayName: __('Billing Address'),
  },
  {
    key: 'shippingAddress',
    displayName: __('Shipping Address'),
  },
];

export const NAD_FILTERS = [
  {
    field: 'merged',
    values: [true],
    operator: OPERATORS.NIN,
  },
  {
    field: 'stopNADVerification',
    values: [true],
    operator: OPERATORS.IN,
  },
];

export const MAXIMUM_ADDRESS_TO_INCLUDE_AT_ONCE = 100;
