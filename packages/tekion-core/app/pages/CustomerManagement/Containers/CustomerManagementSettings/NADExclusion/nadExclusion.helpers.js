import React from 'react';

import _isEmpty from 'lodash/isEmpty';
import _size from 'lodash/size';
import _map from 'lodash/map';
import _findIndex from 'lodash/findIndex';
import _filter from 'lodash/filter';
import _head from 'lodash/head';
import _find from 'lodash/find';
import _includes from 'lodash/includes';

import { tget } from '@tekion/tekion-base/utils/general';
import updateElementAtIndex from '@tekion/tekion-base/utils/updateElementAtIndex';
import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import CustomerReader from '@tekion/tekion-base/readers/Customer';
import { CUSTOMER_TYPE } from '@tekion/tekion-base/constants/customer.constants';
import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { SORT } from '@tekion/tekion-base/builders/request/Request.constants';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { DOWNLOAD_FORMATS } from '@tekion/tekion-components/src/molecules/downloadFileMenu';

import Content from '@tekion/tekion-components/src/atoms/Content';
import Button from '@tekion/tekion-components/src/atoms/Button';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon/Icon.constants';
import Dropdown from '@tekion/tekion-components/src/molecules/DropDown';
import ExpandableSearchField from '@tekion/tekion-components/src/organisms/ExpandableSearchField';
import { TABLE_TYPES } from '@tekion/tekion-components/src/organisms/TableManager';

import { canExcludeCustomersFromRefresh } from 'permissions/customerManagement.permissions';
import {
  DEFAULT_HEADER_HEIGHT,
  COLUMN_TYPES_FOR_REPORT,
  CUSTOMER_REPORT_METADATA_ID,
} from '../../../customerManagement.constants';

import {
  SUB_HEADER_FIELDS,
  REMOVE_FROM_EXCLUSION,
  ACTION_TYPES,
  FIELDS_TO_INCLUDE,
  NAD_FILTERS,
  NAD_COLUMNS,
  NAD_REPORT_FILE_NAME,
  MAXIMUM_ADDRESS_TO_INCLUDE_AT_ONCE,
} from './nadExclusion.constants';
import { getBulkActionsMenu } from './nadExclusion.utils';
import styles from './nadExclusion.module.scss';

export const getSubHeaderProps = ({
  totalCount,
  isSearchActive,
  handleSearch,
  searchText,
  handleSearchValueChange,
  handleAction,
  isBulkActionsDisabled,
  handleAddToExclusion,
}) => ({
  subHeaderLeftActions: [
    {
      renderer: Content,
      renderOptions: {
        children: __('{{totalCount}} Result(s)', { totalCount }),
      },
    },
  ],
  subHeaderRightActions: [
    {
      renderer: Button,
      action: ACTION_TYPES.DOWNLOAD_EXCEL,
      renderOptions: {
        view: Button.VIEW.ICON,
        icon: 'icon-download1',
        className: styles.exportIcon,
      },
    },
    {
      renderer: ExpandableSearchField,
      renderOptions: {
        isActive: false,
        isClearable: false,
        className: styles.expandableSearchByField,
        onSearch: handleSearch,
        value: searchText,
        onChange: handleSearchValueChange,
        inputFieldClassName: styles.searchInput,
      },
    },
    {
      renderer: Dropdown,
      renderOptions: {
        overlay: getBulkActionsMenu(handleAction),
        trigger: ['click'],
        disabled: isBulkActionsDisabled,
        children: (
          <Button view={Button.VIEW.SECONDARY}>
            {__('Actions')}
            <FontIcon className="p-l-4 inline" size={SIZES.S}>
              icon-chevron-down
            </FontIcon>
          </Button>
        ),
      },
    },
    {
      renderer: Button,
      renderOptions: {
        label: SUB_HEADER_FIELDS.ADD_TO_EXCLUSION_CTA,
        view: Button.VIEW.PRIMARY,
        onClick: handleAddToExclusion,
        disabled: !canExcludeCustomersFromRefresh(),
      },
    },
  ],
  className: styles.subHeader,
  rightHeaderClassName: styles.subHeaderRight,
});

export const getTableProps = ({
  totalCount,
  currentPage,
  pageSize,
  headerHeight = DEFAULT_HEADER_HEIGHT,
  tableData,
  loading,
  selectedRequests,
  onRowActionClick,
  handleSelection,
}) => {
  const tableSize = _size(tableData);
  const selectedIdsSize = _size(selectedRequests);
  const isMaximumLimitToMerge = selectedIdsSize === MAXIMUM_ADDRESS_TO_INCLUDE_AT_ONCE;
  return {
    totalNumberOfEntries: totalCount,
    currentPage: currentPage + 1,
    resultsPerPage: pageSize,
    tableHeaderHeight: headerHeight,
    loading,
    showPagination: true,
    pageSize,
    rowHeight: 50,
    minRows: tableSize,
    showActions: canExcludeCustomersFromRefresh(),
    onRowActionClick,
    selection: selectedRequests,
    getRowActions: () => REMOVE_FROM_EXCLUSION,
    type: TABLE_TYPES.WITH_CHECKBOX,
    onSelect: handleSelection,
    selectAll:
      tableSize &&
      (tableSize < MAXIMUM_ADDRESS_TO_INCLUDE_AT_ONCE ? tableSize === selectedIdsSize : isMaximumLimitToMerge),
    disabled: isMaximumLimitToMerge
      ? _map(
          _filter(tableData, item => !_includes(selectedRequests, item?.id)),
          'id'
        )
      : EMPTY_ARRAY,
  };
};

export const getRequestDTO = (currentPage, pageSize, searchText = EMPTY_STRING) => ({
  searchQuery: searchText,
  sort: [
    {
      field: 'modifiedTime',
      order: SORT.DESC,
    },
  ],
  includeFields: FIELDS_TO_INCLUDE,
  pageInfo: {
    start: currentPage * pageSize,
    rows: pageSize || DEFAULT_PAGE_SIZE,
  },
  advancedSearchDTO: {},
  searchText,
  filters: NAD_FILTERS,
});

export const getCustomerName = customer => {
  const companyName = CustomerReader.companyName(customer) || '';
  const customerType = CustomerReader.customerType(customer) || '';
  const firstName = CustomerReader.firstName(customer) || '';
  const lastName = CustomerReader.lastName(customer) || '';
  const middleNameValue = CustomerReader.middleName(customer);
  const middleName = _isEmpty(middleNameValue) ? '' : ` ${middleNameValue}`;
  const name = CustomerReader.name(customer) || '';

  if (customerType === CUSTOMER_TYPE.BUSINESS) return companyName;

  if (_isEmpty(firstName) && _isEmpty(lastName)) return name;
  return `${firstName} ${middleName} ${lastName}`.trim();
};

export const getExcelPayload = (sortDetails, searchText = EMPTY_STRING) => ({
  exportTemplate: {
    name: NAD_REPORT_FILE_NAME,
    entityName: RESOURCE_TYPE.CUSTOMER,
    exportMetadataId: CUSTOMER_REPORT_METADATA_ID,
    filters: searchText
      ? [...NAD_FILTERS, { field: 'searchText', operator: OPERATORS.IN, values: [searchText] }]
      : NAD_FILTERS,
    sort: _map(sortDetails, (order, field) => ({ field, order })),
    columnDefinitions: _map(NAD_COLUMNS, (column, index) => ({
      columnType: COLUMN_TYPES_FOR_REPORT.SIMPLE,
      fieldName: column?.key,
      defaultValue: EMPTY_STRING,
      displayName: column?.displayName,
      order: index + 1,
    })),
  },
  downloadType: DOWNLOAD_FORMATS.EXCEL_FORMAT,
});

export const getModifiedCustomerList = (action = EMPTY_OBJECT, customerSearch = EMPTY_OBJECT) => {
  const value = tget(action, 'payload.value', EMPTY_ARRAY);
  const id = tget(action, 'payload.id');
  let updatedValues = _filter(customerSearch, row => !row.isLastRow) || EMPTY_ARRAY;
  const alreadyAdded = !_isEmpty(_find(customerSearch, row => _head(row?.value) === _head(value)));
  if (!alreadyAdded) {
    if (_isEmpty(id)) {
      updatedValues = [...updatedValues, { value }];
    } else {
      const index = _findIndex(customerSearch, row => _head(row?.value) === id);
      updatedValues = updateElementAtIndex(updatedValues, { value }, index);
    }
  }
  updatedValues = [...updatedValues, { isLastRow: true }];
  return updatedValues;
};
