@use "tstyles/component.scss";

.expandableSearchByField {
  @include component.flex($justify-content: flex-end);
  @include component.full-width;
}

.subHeaderRight {
  @include component.flex($justify-content: flex-end, $align-items: center);
  flex: 1;
  gap: 1.6rem;
}

.containerClassName {
  height: calc(100% - 8rem);
}

.heading {
  padding: 2rem;
  border-bottom: component.$border;
}

.searchInput {
  max-width: 28rem !important;
}
