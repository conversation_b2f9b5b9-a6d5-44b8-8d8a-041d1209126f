import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/organisms/NotificationWrapper';
import { getSignedURLs } from '@tekion/tekion-business/src/services/mediaV3';
import { getFirstSignedURLFromMediaList } from '@tekion/tekion-business/src/helpers/mediaV3.helpers';

import AttachmentDrawerService from '../attachmentDrawer.service';
import { getCustomerContactId, getMediaUrlAction } from '../attachmentDrawer.actions';

// Mock dependencies
jest.mock('@tekion/tekion-components/organisms/NotificationWrapper', () => ({
  toaster: jest.fn(),
  TOASTER_TYPE: {
    ERROR: 'error',
    SUCCESS: 'success',
  },
}));

jest.mock('@tekion/tekion-business/src/services/mediaV3', () => ({
  getSignedURLs: jest.fn(),
}));

jest.mock('@tekion/tekion-business/src/helpers/mediaV3.helpers', () => ({
  getFirstSignedURLFromMediaList: jest.fn(),
}));

jest.mock('../attachmentDrawer.service', () => ({
  getContactId: jest.fn(),
  getCustomerDocs: jest.fn(),
  setCustomerDocs: jest.fn(),
  removeCustomerDocs: jest.fn(),
}));

describe('AttachmentDrawer Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCustomerContactId', () => {
    it('should return empty string and show error toast when API call fails', async () => {
      const mockError = { data: { statusMessage: 'Error message' } };
      AttachmentDrawerService.getContactId.mockRejectedValueOnce(mockError);

      const result = await getCustomerContactId('customerId123');

      expect(AttachmentDrawerService.getContactId).toHaveBeenCalledWith('customerId123');
      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.ERROR, 'Error message');
      expect(result).toBe('');
    });
  });

  describe('getMediaUrlAction', () => {
    it('should return signed URL when API call is successful', async () => {
      const mediaId = 'media123';
      const mockSignedURLs = [{ mediaId, url: 'https://example.com/file.pdf' }];
      const mockFirstURL = 'https://example.com/file.pdf';

      getSignedURLs.mockResolvedValueOnce(mockSignedURLs);
      getFirstSignedURLFromMediaList.mockReturnValueOnce(mockFirstURL);

      const result = await getMediaUrlAction(mediaId);

      expect(getSignedURLs).toHaveBeenCalledWith([mediaId]);
      expect(getFirstSignedURLFromMediaList).toHaveBeenCalledWith(mockSignedURLs);
      expect(result).toBe(mockFirstURL);
    });

    it('should return false and show error toast when API call fails', async () => {
      const mediaId = 'media123';
      const mockError = { message: 'Error message' };

      getSignedURLs.mockRejectedValueOnce(mockError);

      const result = await getMediaUrlAction(mediaId);

      expect(getSignedURLs).toHaveBeenCalledWith([mediaId]);
      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.ERROR, 'Error message');
      expect(result).toBe(false);
    });
  });
});
