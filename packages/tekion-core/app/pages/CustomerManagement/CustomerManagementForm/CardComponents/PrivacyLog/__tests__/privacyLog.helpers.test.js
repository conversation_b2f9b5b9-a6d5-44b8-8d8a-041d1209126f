import { toMoment } from '@tekion/tekion-base/utils/dateUtils';
import { getUserName } from '@tekion/tekion-base/formatters/user';
import userReader from '@tekion/tekion-base/readers/User';
import customerReader from '@tekion/tekion-base/readers/Customer';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

import { getCommunicationType } from 'organisms/CustomerBasicDetailsForm/Components/optInPreferenceSetup/optInPreferenceSetup.helpers';
import { OPT_IN_COMMUNICATION_MODES } from 'organisms/CustomerBasicDetailsForm/Components/optInPreferenceSetup/optInPreferenceSetup.constants';
import {
  COMMUNICATION_PREFERENCE,
  PAGE_SIZE,
  PREFERENCE_HEIGHT,
  MODE_HEIGHT,
  DESCRIPTION_HEIGHT,
} from '../privacyLog.constants';

import {
  getValuesForCommunicationModes,
  constructPayload,
  getAssetsWithNextPageToken,
  getDataForPanels,
  contructDataForDisplay,
  getHeight,
} from '../privacyLog.helpers';

// Mock dependencies
jest.mock('@tekion/tekion-base/readers/Customer');
jest.mock('@tekion/tekion-base/readers/User');
jest.mock('@tekion/tekion-base/formatters/user');
jest.mock('@tekion/tekion-base/utils/dateUtils');
jest.mock('organisms/CustomerBasicDetailsForm/Components/optInPreferenceSetup/optInPreferenceSetup.helpers');

describe('privacyLog.helpers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getValuesForCommunicationModes', () => {
    it('should return communication mode values excluding empty ones', () => {
      const mockInitialValues = { email: '<EMAIL>', phone: '1234567890' };

      customerReader.email.mockReturnValue('<EMAIL>');
      customerReader.alternateEmail.mockReturnValue('');
      customerReader.getMobilePhoneDetails.mockReturnValue({ number: '1234567890' });
      customerReader.getWorkPhoneDetails.mockReturnValue({ number: '' });
      customerReader.getHomePhoneDetails.mockReturnValue({ number: '0987654321' });
      customerReader.number.mockImplementation(phoneDetails => phoneDetails?.number || '');

      const result = getValuesForCommunicationModes(mockInitialValues);

      expect(result).toEqual({
        [OPT_IN_COMMUNICATION_MODES.PRIMARY_EMAIL]: '<EMAIL>',
        [OPT_IN_COMMUNICATION_MODES.MOBILE_PHONE]: '1234567890',
        [OPT_IN_COMMUNICATION_MODES.HOME_PHONE]: '0987654321',
      });
    });

    it('should return empty object when no valid communication modes', () => {
      const mockInitialValues = {};

      customerReader.email.mockReturnValue('');
      customerReader.alternateEmail.mockReturnValue('');
      customerReader.getMobilePhoneDetails.mockReturnValue({});
      customerReader.getWorkPhoneDetails.mockReturnValue({});
      customerReader.getHomePhoneDetails.mockReturnValue({});
      customerReader.number.mockReturnValue('');

      const result = getValuesForCommunicationModes(mockInitialValues);

      expect(result).toEqual({});
    });
  });

  describe('constructPayload', () => {
    it('should construct payload with requests and page size', () => {
      const mockRequests = [
        { assetId: 'asset1', assetType: COMMUNICATION_PREFERENCE },
        { assetId: 'asset2', assetType: COMMUNICATION_PREFERENCE },
      ];

      const result = constructPayload(mockRequests);

      expect(result).toEqual({
        requests: mockRequests,
        pageSize: PAGE_SIZE,
      });
    });

    it('should handle empty requests array', () => {
      const result = constructPayload([]);

      expect(result).toEqual({
        requests: [],
        pageSize: PAGE_SIZE,
      });
    });
  });

  describe('getAssetsWithNextPageToken', () => {
    it('should map next page tokens to assets', () => {
      const mockNextPageTokens = {
        [`${COMMUNICATION_PREFERENCE}-asset1`]: 'token1',
        [`${COMMUNICATION_PREFERENCE}-asset2`]: 'token2',
      };

      const result = getAssetsWithNextPageToken(mockNextPageTokens);

      expect(result).toEqual([
        {
          assetId: 'asset1',
          assetType: COMMUNICATION_PREFERENCE,
          pageToken: 'token1',
        },
        {
          assetId: 'asset2',
          assetType: COMMUNICATION_PREFERENCE,
          pageToken: 'token2',
        },
      ]);
    });

    it('should handle empty next page tokens', () => {
      const result = getAssetsWithNextPageToken({});

      expect(result).toEqual([]);
    });
  });

  describe('getDataForPanels', () => {
    it('should return mode values and payload for panels', () => {
      const mockInitialValues = { email: '<EMAIL>' };
      const mockDealerConfig = { id: 'dealer1', tenantId: 'tenant1' };

      customerReader.email.mockReturnValue('<EMAIL>');
      customerReader.alternateEmail.mockReturnValue('');
      customerReader.getMobilePhoneDetails.mockReturnValue({});
      customerReader.getWorkPhoneDetails.mockReturnValue({});
      customerReader.getHomePhoneDetails.mockReturnValue({});
      customerReader.number.mockReturnValue('');
      getCommunicationType.mockReturnValue('EMAIL');

      const result = getDataForPanels(mockInitialValues, mockDealerConfig);

      expect(result).toHaveProperty('modeValues');
      expect(result).toHaveProperty('payload');
      expect(result.payload).toHaveProperty('requests');
      expect(result.payload).toHaveProperty('pageSize', PAGE_SIZE);
    });
  });

  describe('contructDataForDisplay', () => {
    const mockGetFormattedDateAndTime = jest.fn();
    const mockUserList = {
      user1: { firstName: 'John', lastName: 'Doe' },
    };
    const mockModeValues = {
      [OPT_IN_COMMUNICATION_MODES.PRIMARY_EMAIL]: '<EMAIL>',
    };

    beforeEach(() => {
      mockGetFormattedDateAndTime.mockImplementation(({ formatType }) => {
        if (formatType === DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR) {
          return 'Jan 1, 2024';
        }
        if (formatType === DATE_TIME_FORMAT.HOUR_MINUTE) {
          return '10:30 AM';
        }
        return '';
      });
      getUserName.mockReturnValue('John Doe');
      userReader.position.mockReturnValue('Manager');
      toMoment.mockReturnValue({});
    });

    it('should construct display data from audit data', () => {
      const mockData = [
        {
          createdTime: '2024-01-01T10:30:00Z',
          userId: 'user1',
          assetid: '<EMAIL>',
          change: [
            {
              attribute: 'channelPreference',
              nestedAuditChange: [
                {
                  complexObjectKey: 'pref1',
                  nestedAuditChange: [
                    {
                      newValues: true,
                      oldValues: false,
                    },
                  ],
                },
              ],
            },
          ],
        },
      ];

      const result = contructDataForDisplay({
        data: mockData,
        userList: mockUserList,
        modeValues: mockModeValues,
        getFormattedDateAndTime: mockGetFormattedDateAndTime,
      });

      expect(result).toHaveProperty('Jan 1, 2024');
      expect(result['Jan 1, 2024']).toHaveLength(1);
      expect(result['Jan 1, 2024'][0]).toMatchObject({
        date: 'Jan 1, 2024',
        time: '10:30 AM',
        description: 'by John Doe - Manager',
      });
    });

    it('should handle data without user information', () => {
      const mockData = [
        {
          createdTime: '2024-01-01T10:30:00Z',
          userId: 'unknown_user',
          assetid: '<EMAIL>',
          change: [
            {
              attribute: 'channelPreference',
              nestedAuditChange: [
                {
                  complexObjectKey: 'pref1',
                  nestedAuditChange: [
                    {
                      newValues: true,
                      oldValues: false,
                    },
                  ],
                },
              ],
            },
          ],
        },
      ];

      const result = contructDataForDisplay({
        data: mockData,
        userList: mockUserList,
        modeValues: mockModeValues,
        getFormattedDateAndTime: mockGetFormattedDateAndTime,
      });

      expect(result['Jan 1, 2024'][0].description).toBe('by Customer');
    });

    it('should handle empty data array', () => {
      const result = contructDataForDisplay({
        data: [],
        userList: mockUserList,
        modeValues: mockModeValues,
        getFormattedDateAndTime: mockGetFormattedDateAndTime,
      });

      expect(result).toEqual({});
    });
  });

  describe('getHeight', () => {
    it('should calculate height based on preference data', () => {
      const mockPreferenceData = [
        {
          values: [{ id: 'val1' }, { id: 'val2' }],
        },
        {
          values: [{ id: 'val3' }],
        },
      ];

      const result = getHeight(mockPreferenceData);

      const expectedHeight = DESCRIPTION_HEIGHT + 2 * PREFERENCE_HEIGHT + 3 * MODE_HEIGHT; // 2 + 1 values total

      expect(result).toBe(expectedHeight);
    });

    it('should handle empty preference data', () => {
      const result = getHeight([]);

      expect(result).toBe(DESCRIPTION_HEIGHT);
    });

    it('should handle preference data without values', () => {
      const mockPreferenceData = [{ values: undefined }, { values: null }];

      const result = getHeight(mockPreferenceData);

      expect(result).toBe(DESCRIPTION_HEIGHT + 2 * PREFERENCE_HEIGHT);
    });
  });
});
