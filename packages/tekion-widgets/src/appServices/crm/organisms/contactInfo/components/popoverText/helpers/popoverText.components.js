import React from 'react';

import Content from '@tekion/tekion-components/src/atoms/Content';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';

import ICONS from '../../../../../constants/icons';

import styles from '../popoverText.module.scss';

export const renderMessageAfterCopy = () => (
  <div className="flex flex-row align-items-center">
    <Content>{__('Copied')}</Content>
    <FontIcon size={SIZES.S} className={`${styles.icon} m-l-4`}>
      {ICONS.CHECK_ACTIVE}
    </FontIcon>
  </div>
);
