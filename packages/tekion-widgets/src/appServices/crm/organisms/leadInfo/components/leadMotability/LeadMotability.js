import React, { memo, useMemo } from 'react';
import PropTypes from 'prop-types';

import { FormWithSubmission } from '@tekion/tekion-components/src/pages/formPage';

import getFields from './helpers/leadMotability.getFields';
import getSections from './helpers/leadMotability.getSections';

const MotabilityLead = ({
  contextId,
  onSubmit,
  onAction,
  isFormDisabled,
  sectionClassName,
  values,
  errors,
  isEditMode,
}) => {
  const sections = useMemo(() => getSections(sectionClassName, isEditMode), [sectionClassName, isEditMode]);

  const fields = useMemo(() => getFields(), []);

  return (
    <FormWithSubmission
      contextId={contextId}
      errors={errors}
      fields={fields}
      onAction={onAction}
      onSubmit={onSubmit}
      sections={sections}
      values={values}
      isFormDisabled={isFormDisabled}
    />
  );
};

MotabilityLead.propTypes = {
  contextId: PropTypes.string.isRequired,
  onSubmit: PropTypes.func.isRequired,
  onAction: PropTypes.func.isRequired,
  isFormDisabled: PropTypes.bool.isRequired,
  values: PropTypes.object.isRequired,
  errors: PropTypes.object.isRequired,
  sectionClassName: PropTypes.string,
  isEditMode: PropTypes.bool,
};

MotabilityLead.defaultProps = {
  sectionClassName: undefined,
  isEditMode: false,
};

export default memo(MotabilityLead);
