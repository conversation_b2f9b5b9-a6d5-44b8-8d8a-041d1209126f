import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import CUSTOMER_FIELD_IDS from '../../../../../constants/formFieldIds/customer';

const { MOTABILITY } = CUSTOMER_FIELD_IDS;

const getSections = (sectionClassName, isEditMode) => [
  {
    header: !isEditMode
      ? {
          label: __('Motability'),
          size: 4,
        }
      : EMPTY_OBJECT,
    className: sectionClassName,
    rows: [
      {
        columns: [MOTABILITY],
      },
    ],
  },
];

export default getSections;
