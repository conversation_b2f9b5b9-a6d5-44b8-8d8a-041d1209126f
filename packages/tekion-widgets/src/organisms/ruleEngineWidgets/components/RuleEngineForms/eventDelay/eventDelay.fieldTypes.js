// basic fields

const DELAY_TYPE = 'delayType';
const EXECUTION_DELAY = 'executionDelay';
const DEPARTMENT = 'department';

const EVENT_TYPE = 'expectedEventType';
const EXIT_WORKFLOW_EVENT_TYPE = 'exitWorkflowEventType';

// delay type specific fields

const EXECUTION_DELAYED_BY_OPERATING_TYPE_DEALERSHIP = 'executionDelayByOperatingTypeDealership';
const ASSET_BASED_DELAY = 'assetBasedDelay';
const EXECUTION_DELAY_WITHIN_OPERATING_HOURS = 'executionDelayWithinOperatingHours';
const EXECUTION_DELAY_WITHIN_OPERATING_HOURS_OF_DEPT = 'executionDelayByOperatingHoursOfDepartment';

const FIELD_IDS = {
  DELAY_TYPE,
  EXECUTION_DELAY,
  DEPARTMENT,
  EVENT_TYPE,
  EXIT_WORKFLOW_EVENT_TYPE,
  ASSET_BASED_DELAY,
  EXECUTION_DELAYED_BY_OPERATING_TYPE_DEALERSHIP,
  EXECUTION_DELAY_WITHIN_OPERATING_HOURS,
  EXECUTION_DELAY_WITHIN_OPERATING_HOURS_OF_DEPT,
};

export default FIELD_IDS;
